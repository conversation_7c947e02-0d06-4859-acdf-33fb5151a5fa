"""
Example Usage of Experimental Pipelines

This script demonstrates how to use the ExperimentPipelines class to run
different collaboration strategies for secure code development.
"""

import os
import sys

# Add the modules directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.llm_manager import LLMManager
from modules.codeql_analyzer import CodeQLAnalyzer
from modules.experiment_pipelines import ExperimentPipelines


def main():
    """
    Example usage of the experimental pipelines framework.
    """
    
    # Initialize components
    print("Initializing LLM Manager...")
    llm_manager = LLMManager()
    
    print("Initializing CodeQL Analyzer...")
    try:
        codeql_analyzer = CodeQLAnalyzer()
    except Exception as e:
        print(f"Warning: CodeQL analyzer initialization failed: {e}")
        codeql_analyzer = None
    
    print("Initializing Experiment Pipelines...")
    experiment_pipelines = ExperimentPipelines(llm_manager, codeql_analyzer)
    
    # Example query and parameters
    query = "Create a secure user authentication function that validates passwords"
    language = "python"
    model_names = ["gpt-4", "claude-3", "gemini-pro"]  # Adjust based on your available models
    contexts = ["Use bcrypt for password hashing", "Implement rate limiting"]
    
    print(f"\nRunning experiments with query: {query}")
    print(f"Language: {language}")
    print(f"Models: {model_names}")
    
    # Example 1: Run a single pipeline
    print("\n" + "="*60)
    print("EXAMPLE 1: Running Single Pipeline (P2)")
    print("="*60)
    
    try:
        p2_results = experiment_pipelines.run_pipeline(
            pipeline_id="P2",
            query=query,
            language=language,
            model_names=model_names[:1],  # P2 uses single model
            contexts=contexts
        )
        
        print(f"Pipeline P2 Results:")
        print(f"- Description: {p2_results['description']}")
        print(f"- Collaboration Type: {p2_results['collaboration_type']}")
        print(f"- Phases: {p2_results['phases']}")
        print(f"- Execution Time: {p2_results['execution_time']:.2f}s")
        print(f"- Final Code Length: {len(p2_results['final_code'])} characters")
        
    except Exception as e:
        print(f"Error running P2: {e}")
    
    # Example 2: Run multiple specific pipelines
    print("\n" + "="*60)
    print("EXAMPLE 2: Running Multiple Specific Pipelines")
    print("="*60)
    
    pipelines_to_test = ["P1", "P3", "P5", "P8"]
    
    for pipeline_id in pipelines_to_test:
        try:
            print(f"\nRunning {pipeline_id}: {experiment_pipelines.get_pipeline_description(pipeline_id)}")
            
            result = experiment_pipelines.run_pipeline(
                pipeline_id=pipeline_id,
                query=query,
                language=language,
                model_names=model_names,
                contexts=contexts
            )
            
            print(f"✓ {pipeline_id} completed in {result['execution_time']:.2f}s")
            print(f"  Collaboration Type: {result['collaboration_type']}")
            print(f"  Final Code Length: {len(result['final_code'])} characters")
            
        except Exception as e:
            print(f"✗ {pipeline_id} failed: {e}")
    
    # Example 3: Run all pipelines (commented out for demo - takes a long time)
    print("\n" + "="*60)
    print("EXAMPLE 3: Running All Pipelines (Demo)")
    print("="*60)
    
    print("Note: Running all 15 pipelines would take significant time.")
    print("Uncomment the code below to run all pipelines:")
    print()
    print("# all_results = experiment_pipelines.run_all_pipelines(")
    print("#     query=query,")
    print("#     language=language,")
    print("#     model_names=model_names,")
    print("#     contexts=contexts")
    print("# )")
    print("# ")
    print("# # Export results")
    print("# filepath = experiment_pipelines.export_results(all_results)")
    print("# print(f'All results exported to: {filepath}')")
    
    # Example 4: Show pipeline descriptions
    print("\n" + "="*60)
    print("EXAMPLE 4: All Pipeline Descriptions")
    print("="*60)
    
    for i in range(1, 16):
        pipeline_id = f"P{i}"
        description = experiment_pipelines.get_pipeline_description(pipeline_id)
        print(f"{pipeline_id}: {description}")
    
    print("\n" + "="*60)
    print("EXAMPLES COMPLETED")
    print("="*60)
    print("\nTo run specific experiments:")
    print("1. Modify the query, language, and model_names variables")
    print("2. Choose specific pipeline IDs to test")
    print("3. Uncomment the 'run_all_pipelines' section for comprehensive testing")
    print("4. Results will be exported to the 'results' directory")


if __name__ == "__main__":
    main()

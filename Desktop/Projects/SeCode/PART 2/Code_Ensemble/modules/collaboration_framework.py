"""
Secure Code Collaboration Framework

This module implements various collaboration strategies for secure code development,
including Sequential Collaboration, Peer Review, Chain of Debate, Parallel Solution Synthesis,
Static Analysis Overlay, and Pair Programming.

The framework leverages the LLMManager class for model interactions and implements
multiple collaboration patterns based on research in multi-agent secure coding.
"""

import json
import random
from typing import Dict, List, Tuple, Any
from .llm_manager import LLMManager


class SecureCodeCollaborationFramework:
    """
    A comprehensive framework implementing various collaboration strategies for secure code development.
    Leverages LLMManager for model interactions and implements multiple collaboration patterns.
    """
    
    def __init__(self, llm_manager: LLMManager):
        self.llm_manager = llm_manager
        
    # ==================== SEQUENTIAL COLLABORATION (SC) ====================
    
    def sequential_collaboration(self, query: str, language: str, model_names: list, 
                               contexts: list = [], max_context_length: int = 3000) -> dict:
        """
        Sequential Collaboration: Agents perform tasks in fixed order (generation → detection → patching)
        
        Args:
            query: The code generation request
            language: Programming language
            model_names: List of model names to use
            contexts: Additional context information
            max_context_length: Maximum context length
            
        Returns:
            Dictionary containing results from each stage
        """
        results = {
            'generation': {},
            'detection': {},
            'patching': {},
            'final_code': None,
            'strategy': 'Sequential Collaboration'
        }
        
        # Step 1: Code Generation
        print("[SC] Step 1: Code Generation")
        for model in model_names:
            code = self.llm_manager.generate_code(model, query, language, contexts, max_context_length)
            results['generation'][model] = code
        
        # Step 2: Vulnerability Detection
        print("[SC] Step 2: Vulnerability Detection")
        for model, code in results['generation'].items():
            vulnerability_report = self.llm_manager.llm_to_check_vulnerability(
                code, language, model, contexts, max_context_length
            )
            results['detection'][model] = vulnerability_report
        
        # Step 3: Code Patching
        print("[SC] Step 3: Code Patching")
        for model, code in results['generation'].items():
            vulnerability_report = results['detection'][model]
            if "Vulnerable : YES" in vulnerability_report:
                patched_code = self.llm_manager.patch_code_single(
                    model, code, vulnerability_report, {}, max_context_length
                )
                results['patching'][model] = patched_code
            else:
                results['patching'][model] = code  # No patching needed
        
        # Select best final code (could be enhanced with ranking)
        results['final_code'] = list(results['patching'].values())[0]
        return results
    
    # ==================== PEER REVIEW (PR) ====================
    
    def peer_review(self, query: str, language: str, model_names: list,
                   contexts: list = [], max_context_length: int = 3000) -> dict:
        """
        Peer Review: Models critique each other's outputs within the same stage
        
        Args:
            query: The code generation request
            language: Programming language  
            model_names: List of model names to use
            contexts: Additional context information
            max_context_length: Maximum context length
            
        Returns:
            Dictionary containing peer review results
        """
        results = {
            'initial_generation': {},
            'peer_reviews': {},
            'revised_code': {},
            'final_code': None,
            'strategy': 'Peer Review'
        }
        
        # Step 1: Initial code generation by all models
        print("[PR] Step 1: Initial Code Generation")
        for model in model_names:
            code = self.llm_manager.generate_code(model, query, language, contexts, max_context_length)
            results['initial_generation'][model] = code
        
        # Step 2: Peer review - each model reviews others' code
        print("[PR] Step 2: Peer Review Process")
        for reviewer_model in model_names:
            results['peer_reviews'][reviewer_model] = {}
            for author_model, code in results['initial_generation'].items():
                if reviewer_model != author_model:
                    review = self._generate_peer_review(reviewer_model, code, language, contexts)
                    results['peer_reviews'][reviewer_model][author_model] = review
        
        # Step 3: Revise code based on peer feedback
        print("[PR] Step 3: Code Revision")
        for model, original_code in results['initial_generation'].items():
            # Collect all reviews for this model's code
            all_reviews = []
            for reviewer in model_names:
                if reviewer != model and model in results['peer_reviews'][reviewer]:
                    all_reviews.append(results['peer_reviews'][reviewer][model])
            
            if all_reviews:
                revised_code = self._revise_code_with_feedback(
                    model, original_code, all_reviews, language, contexts
                )
                results['revised_code'][model] = revised_code
            else:
                results['revised_code'][model] = original_code
        
        # Select best final code
        results['final_code'] = self._select_best_code(results['revised_code'])
        return results

    # ==================== CHAIN OF DEBATE (CD) ====================

    def chain_of_debate(self, query: str, language: str, model_names: list,
                       contexts: list = [], max_context_length: int = 3000, rounds: int = 3) -> dict:
        """
        Chain of Debate: Adversarial roles (defenders vs prosecutors) debate security issues;
        a judge synthesizes outcomes

        Args:
            query: The code generation request
            language: Programming language
            model_names: List of model names (should have at least 3 for prosecutor, defender, judge)
            contexts: Additional context information
            max_context_length: Maximum context length
            rounds: Number of debate rounds

        Returns:
            Dictionary containing debate results and final verdict
        """
        if len(model_names) < 3:
            raise ValueError("Chain of Debate requires at least 3 models (prosecutor, defender, judge)")

        results = {
            'initial_code': None,
            'debate_rounds': [],
            'final_verdict': None,
            'final_code': None,
            'strategy': 'Chain of Debate'
        }

        # Step 1: Generate initial code
        print("[CD] Step 1: Initial Code Generation")
        generator_model = model_names[0]
        initial_code = self.llm_manager.generate_code(
            generator_model, query, language, contexts, max_context_length
        )
        results['initial_code'] = initial_code

        # Assign roles
        prosecutor_model = model_names[1]
        defender_model = model_names[2] if len(model_names) > 2 else model_names[0]
        judge_model = model_names[-1]  # Last model as judge

        current_code = initial_code

        # Step 2: Conduct debate rounds
        print(f"[CD] Step 2: Conducting {rounds} debate rounds")
        for round_num in range(rounds):
            print(f"[CD] Round {round_num + 1}")

            # Prosecutor argues for vulnerabilities
            prosecutor_argument = self._generate_prosecutor_argument(
                prosecutor_model, current_code, language, contexts
            )

            # Defender argues for security
            defender_argument = self._generate_defender_argument(
                defender_model, current_code, prosecutor_argument, language, contexts
            )

            # Judge evaluates and provides verdict
            judge_verdict = self._generate_judge_verdict(
                judge_model, current_code, prosecutor_argument, defender_argument, language, contexts
            )

            round_result = {
                'round': round_num + 1,
                'prosecutor_argument': prosecutor_argument,
                'defender_argument': defender_argument,
                'judge_verdict': judge_verdict,
                'code_at_round': current_code
            }
            results['debate_rounds'].append(round_result)

            # If judge suggests fixes, apply them
            if "VULNERABLE" in judge_verdict and "fix" in judge_verdict.lower():
                current_code = self._apply_judge_fixes(
                    judge_model, current_code, judge_verdict, language, contexts
                )

        results['final_verdict'] = results['debate_rounds'][-1]['judge_verdict']
        results['final_code'] = current_code
        return results

    # ==================== PARALLEL SOLUTION SYNTHESIS (PSS) ====================

    def parallel_solution_synthesis(self, query: str, language: str, model_names: list,
                                  contexts: list = [], max_context_length: int = 3000) -> dict:
        """
        Parallel Solution Synthesis: Multiple models independently generate or fix code;
        outputs are ranked or ensembled without direct interaction

        Args:
            query: The code generation request
            language: Programming language
            model_names: List of model names to use
            contexts: Additional context information
            max_context_length: Maximum context length

        Returns:
            Dictionary containing parallel solutions and ranked results
        """
        results = {
            'parallel_solutions': {},
            'vulnerability_assessments': {},
            'rankings': {},
            'ensemble_code': None,
            'final_code': None,
            'strategy': 'Parallel Solution Synthesis'
        }

        # Step 1: Parallel code generation
        print("[PSS] Step 1: Parallel Code Generation")
        for model in model_names:
            code = self.llm_manager.generate_code(model, query, language, contexts, max_context_length)
            results['parallel_solutions'][model] = code

        # Step 2: Independent vulnerability assessment
        print("[PSS] Step 2: Vulnerability Assessment")
        for model, code in results['parallel_solutions'].items():
            vulnerability_report = self.llm_manager.llm_to_check_vulnerability(
                code, language, model, contexts, max_context_length
            )
            results['vulnerability_assessments'][model] = vulnerability_report

        # Step 3: Rank solutions based on security and quality
        print("[PSS] Step 3: Solution Ranking")
        results['rankings'] = self._rank_solutions(
            results['parallel_solutions'], results['vulnerability_assessments']
        )

        # Step 4: Create ensemble solution
        print("[PSS] Step 4: Ensemble Creation")
        results['ensemble_code'] = self._create_ensemble_solution(
            results['parallel_solutions'], results['rankings'], language, contexts
        )

        # Select final code (best ranked or ensemble)
        best_model = list(results['rankings'].keys())[0]  # Top ranked
        results['final_code'] = results['ensemble_code'] or results['parallel_solutions'][best_model]
        return results

    # ==================== STATIC ANALYSIS OVERLAY (SAO) ====================

    def static_analysis_overlay(self, query: str, language: str, model_names: list,
                              contexts: list = [], max_context_length: int = 3000,
                              static_tools: list = ['codeql']) -> dict:
        """
        Static Analysis Overlay: External static tools (e.g., CodeQL, AST/CFG/DFG analysis)
        assist or validate model outputs

        Args:
            query: The code generation request
            language: Programming language
            model_names: List of model names to use
            contexts: Additional context information
            max_context_length: Maximum context length
            static_tools: List of static analysis tools to use

        Returns:
            Dictionary containing static analysis results and validated code
        """
        results = {
            'generated_code': {},
            'static_analysis_reports': {},
            'validated_code': {},
            'final_code': None,
            'strategy': 'Static Analysis Overlay'
        }

        # Step 1: Code generation
        print("[SAO] Step 1: Code Generation")
        for model in model_names:
            code = self.llm_manager.generate_code(model, query, language, contexts, max_context_length)
            results['generated_code'][model] = code

        # Step 2: Static analysis
        print("[SAO] Step 2: Static Analysis")
        for model, code in results['generated_code'].items():
            static_reports = {}
            for tool in static_tools:
                if tool == 'codeql':
                    # Simulate CodeQL analysis (in real implementation, would call actual CodeQL)
                    report = self._simulate_codeql_analysis(code, language)
                elif tool == 'ast':
                    report = self._simulate_ast_analysis(code, language)
                else:
                    report = f"Static analysis with {tool} not implemented"
                static_reports[tool] = report
            results['static_analysis_reports'][model] = static_reports

        # Step 3: Validate and fix based on static analysis
        print("[SAO] Step 3: Validation and Fixing")
        for model, code in results['generated_code'].items():
            static_reports = results['static_analysis_reports'][model]
            validated_code = self._validate_with_static_analysis(
                model, code, static_reports, language, contexts
            )
            results['validated_code'][model] = validated_code

        # Select best validated code
        results['final_code'] = self._select_best_validated_code(results['validated_code'])
        return results

    # ==================== PAIR PROGRAMMING (PP) ====================

    def pair_programming(self, query: str, language: str, model_names: list,
                        contexts: list = [], max_context_length: int = 3000,
                        iterations: int = 3) -> dict:
        """
        Pair Programming: Two roles (Driver and Navigator) iteratively collaborate
        to develop secure code

        Args:
            query: The code generation request
            language: Programming language
            model_names: List of model names (should have at least 2 for driver and navigator)
            contexts: Additional context information
            max_context_length: Maximum context length
            iterations: Number of pair programming iterations

        Returns:
            Dictionary containing pair programming session results
        """
        if len(model_names) < 2:
            raise ValueError("Pair Programming requires at least 2 models (driver and navigator)")

        results = {
            'iterations': [],
            'final_code': None,
            'strategy': 'Pair Programming'
        }

        # Assign roles
        driver_model = model_names[0]
        navigator_model = model_names[1]

        current_code = ""

        print(f"[PP] Starting Pair Programming with {iterations} iterations")
        print(f"[PP] Driver: {driver_model}, Navigator: {navigator_model}")

        for iteration in range(iterations):
            print(f"[PP] Iteration {iteration + 1}")

            # Navigator provides guidance
            navigator_guidance = self._generate_navigator_guidance(
                navigator_model, query, current_code, language, contexts, iteration
            )

            # Driver writes/modifies code based on guidance
            driver_code = self._generate_driver_code(
                driver_model, query, current_code, navigator_guidance, language, contexts
            )

            # Navigator reviews the code
            navigator_review = self._generate_navigator_review(
                navigator_model, driver_code, language, contexts
            )

            iteration_result = {
                'iteration': iteration + 1,
                'navigator_guidance': navigator_guidance,
                'driver_code': driver_code,
                'navigator_review': navigator_review,
                'code_state': driver_code
            }
            results['iterations'].append(iteration_result)

            current_code = driver_code

        results['final_code'] = current_code
        return results

    # ==================== HELPER METHODS ====================

    def _generate_peer_review(self, reviewer_model: str, code: str, language: str, contexts: list) -> str:
        """Generate a peer review of code by a specific model"""
        messages = [
            {"role": "system", "content": "You are a senior software engineer conducting a peer review focused on security, code quality, and best practices."},
            {"role": "user", "content": f"""
Please review the following {language} code for:
1. Security vulnerabilities (CWE categories)
2. Code quality and maintainability
3. Best practices adherence
4. Potential improvements

Code to review:
{code}

Provide specific, actionable feedback with line references where applicable.
"""}
        ]
        return self.llm_manager.generate_response(messages, reviewer_model)

    def _revise_code_with_feedback(self, model: str, original_code: str, reviews: list,
                                  language: str, contexts: list) -> str:
        """Revise code based on peer review feedback"""
        combined_feedback = "\n\n".join([f"Review {i+1}:\n{review}" for i, review in enumerate(reviews)])

        messages = [
            {"role": "system", "content": "You are a software engineer revising code based on peer review feedback."},
            {"role": "user", "content": f"""
Original {language} code:
{original_code}

Peer review feedback:
{combined_feedback}

Please revise the code to address the feedback while maintaining functionality.
Provide only the revised code.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

    def _select_best_code(self, code_dict: dict) -> str:
        """Select the best code from a dictionary of options"""
        # Simple selection - could be enhanced with more sophisticated ranking
        return list(code_dict.values())[0] if code_dict else ""

    def _generate_prosecutor_argument(self, model: str, code: str, language: str, contexts: list) -> str:
        """Generate prosecutor argument highlighting security vulnerabilities"""
        messages = [
            {"role": "system", "content": "You are a security prosecutor. Your role is to identify and argue for the existence of security vulnerabilities in code."},
            {"role": "user", "content": f"""
Analyze this {language} code and build a strong case for any security vulnerabilities:

{code}

Present your argument with:
1. Specific vulnerability types (CWE references)
2. Potential attack vectors
3. Risk assessment
4. Evidence from the code

Be thorough and critical in your analysis.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

    def _generate_defender_argument(self, model: str, code: str, prosecutor_arg: str,
                                   language: str, contexts: list) -> str:
        """Generate defender argument defending code security"""
        messages = [
            {"role": "system", "content": "You are a security defender. Your role is to defend the code against security accusations and highlight its secure aspects."},
            {"role": "user", "content": f"""
The prosecutor has made the following security accusations about this {language} code:

Code:
{code}

Prosecutor's argument:
{prosecutor_arg}

Defend the code by:
1. Refuting false claims with evidence
2. Highlighting security measures already in place
3. Explaining why certain patterns are actually secure
4. Acknowledging any valid concerns but minimizing their impact

Build a strong defense case.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

    def _generate_judge_verdict(self, model: str, code: str, prosecutor_arg: str,
                               defender_arg: str, language: str, contexts: list) -> str:
        """Generate judge verdict based on prosecutor and defender arguments"""
        messages = [
            {"role": "system", "content": "You are an impartial security judge. Evaluate the arguments and provide a fair verdict on code security."},
            {"role": "user", "content": f"""
You must judge the security of this {language} code based on the arguments presented:

Code under review:
{code}

Prosecutor's argument (claiming vulnerabilities):
{prosecutor_arg}

Defender's argument (defending security):
{defender_arg}

Provide your verdict:
1. VULNERABLE or NOT VULNERABLE
2. Reasoning for your decision
3. If vulnerable, suggest specific fixes
4. Rate the severity (LOW/MEDIUM/HIGH)

Be impartial and evidence-based in your judgment.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

    def _apply_judge_fixes(self, model: str, code: str, verdict: str, language: str, contexts: list) -> str:
        """Apply fixes suggested by the judge"""
        messages = [
            {"role": "system", "content": "You are a security expert implementing fixes based on a judge's verdict."},
            {"role": "user", "content": f"""
Original {language} code:
{code}

Judge's verdict and fix suggestions:
{verdict}

Implement the suggested fixes while preserving functionality.
Provide only the fixed code.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

    def _rank_solutions(self, solutions: dict, assessments: dict) -> dict:
        """Rank solutions based on security and quality metrics"""
        rankings = {}

        for model, code in solutions.items():
            score = 0
            assessment = assessments.get(model, "")

            # Simple scoring based on vulnerability assessment
            if "Vulnerable : NO" in assessment:
                score += 10
            elif "Vulnerable : YES" in assessment:
                score -= 5

            # Additional scoring could be added here (code quality, completeness, etc.)
            rankings[model] = score

        # Sort by score (highest first)
        return dict(sorted(rankings.items(), key=lambda x: x[1], reverse=True))

    def _create_ensemble_solution(self, solutions: dict, rankings: dict,
                                 language: str, contexts: list) -> str:
        """Create an ensemble solution from multiple code solutions"""
        # Get top 2 solutions
        top_models = list(rankings.keys())[:2]
        if len(top_models) < 2:
            return list(solutions.values())[0] if solutions else ""

        code1 = solutions[top_models[0]]
        code2 = solutions[top_models[1]]

        # Use first model to create ensemble
        messages = [
            {"role": "system", "content": "You are a software engineer creating an optimal solution by combining the best aspects of multiple code implementations."},
            {"role": "user", "content": f"""
Create an optimal {language} solution by combining the best aspects of these two implementations:

Solution 1 (from {top_models[0]}):
{code1}

Solution 2 (from {top_models[1]}):
{code2}

Combine the best security practices, functionality, and code quality from both.
Provide only the final combined code.
"""}
        ]
        return self.llm_manager.generate_response(messages, top_models[0])

    def _simulate_codeql_analysis(self, code: str, language: str) -> str:
        """Simulate CodeQL static analysis (placeholder for actual CodeQL integration)"""
        # In a real implementation, this would call actual CodeQL
        return f"CodeQL analysis for {language} code: Simulated static analysis report"

    def _simulate_ast_analysis(self, code: str, language: str) -> str:
        """Simulate AST-based analysis (placeholder for actual AST analysis)"""
        return f"AST analysis for {language} code: Simulated AST/CFG/DFG analysis report"

    def _validate_with_static_analysis(self, model: str, code: str, static_reports: dict,
                                      language: str, contexts: list) -> str:
        """Validate and fix code based on static analysis reports"""
        combined_reports = "\n\n".join([f"{tool}: {report}" for tool, report in static_reports.items()])

        messages = [
            {"role": "system", "content": "You are a security expert validating code against static analysis findings."},
            {"role": "user", "content": f"""
{language} code to validate:
{code}

Static analysis reports:
{combined_reports}

Fix any issues identified by the static analysis tools while preserving functionality.
Provide only the validated/fixed code.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

    def _select_best_validated_code(self, validated_code: dict) -> str:
        """Select the best validated code"""
        # Simple selection - could be enhanced with more sophisticated evaluation
        return list(validated_code.values())[0] if validated_code else ""

    def _generate_navigator_guidance(self, model: str, query: str, current_code: str,
                                   language: str, contexts: list, iteration: int) -> str:
        """Generate navigator guidance for pair programming"""
        messages = [
            {"role": "system", "content": "You are a Navigator in pair programming. Provide high-level guidance, security considerations, and architectural direction."},
            {"role": "user", "content": f"""
Pair Programming Session - Iteration {iteration + 1}

Original request: {query}
Language: {language}

Current code state:
{current_code if current_code else "No code yet - starting fresh"}

As the Navigator, provide guidance on:
1. Next steps to implement or improve
2. Security considerations to keep in mind
3. Code structure and architecture suggestions
4. Potential pitfalls to avoid

Keep guidance high-level and strategic.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

    def _generate_driver_code(self, model: str, query: str, current_code: str,
                             guidance: str, language: str, contexts: list) -> str:
        """Generate driver code based on navigator guidance"""
        messages = [
            {"role": "system", "content": "You are a Driver in pair programming. Write code based on the Navigator's guidance."},
            {"role": "user", "content": f"""
Pair Programming Session

Original request: {query}
Language: {language}

Current code:
{current_code if current_code else "Starting fresh"}

Navigator's guidance:
{guidance}

As the Driver, implement the code following the Navigator's guidance.
Focus on writing secure, functional code.
Provide only the code implementation.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

    def _generate_navigator_review(self, model: str, code: str, language: str, contexts: list) -> str:
        """Generate navigator review of driver's code"""
        messages = [
            {"role": "system", "content": "You are a Navigator reviewing the Driver's code implementation."},
            {"role": "user", "content": f"""
Review this {language} code implementation:

{code}

As the Navigator, provide feedback on:
1. Code quality and adherence to guidance
2. Security considerations
3. Suggestions for next iteration
4. Overall assessment

Keep feedback constructive and focused on improvement.
"""}
        ]
        return self.llm_manager.generate_response(messages, model)

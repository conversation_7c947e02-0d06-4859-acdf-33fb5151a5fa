"""
Experimental Pipelines for Secure Code Collaboration

This module implements 15 experimental pipelines that combine different collaboration strategies
and LLM configurations for secure code development. Each pipeline represents a different
approach to multi-agent secure coding as defined in the research framework.

The pipelines range from single-model baselines to complex multi-agent collaborations
involving generation, detection, and fixing phases with various interaction patterns.
"""

import json
import time
from typing import Dict, List, Any, Optional
from .collaboration_framework import SecureCodeCollaborationFramework
from .llm_manager import LLMManager
from .codeql_analyzer import CodeQLAnalyzer


class ExperimentPipelines:
    """
    Implements 15 experimental pipelines for secure code collaboration research.
    Each pipeline represents a different combination of collaboration strategies and LLM roles.
    """
    
    def __init__(self, llm_manager: LLMManager, codeql_analyzer: CodeQLAnalyzer = None):
        self.llm_manager = llm_manager
        self.collaboration_framework = SecureCodeCollaborationFramework(llm_manager)
        self.codeql_analyzer = codeql_analyzer or CodeQLAnalyzer()
        
    def run_pipeline(self, pipeline_id: str, query: str, language: str, 
                    model_names: List[str], contexts: List[str] = [], 
                    max_context_length: int = 3000) -> Dict[str, Any]:
        """
        Run a specific experimental pipeline.
        
        Args:
            pipeline_id: Pipeline identifier (P1-P15)
            query: Code generation request
            language: Programming language
            model_names: List of model names to use
            contexts: Additional context information
            max_context_length: Maximum context length
            
        Returns:
            Dictionary containing pipeline results and metadata
        """
        start_time = time.time()
        
        # Route to appropriate pipeline method
        pipeline_methods = {
            'P1': self._pipeline_p1,
            'P2': self._pipeline_p2,
            'P3': self._pipeline_p3,
            'P4': self._pipeline_p4,
            'P5': self._pipeline_p5,
            'P6': self._pipeline_p6,
            'P7': self._pipeline_p7,
            'P8': self._pipeline_p8,
            'P9': self._pipeline_p9,
            'P10': self._pipeline_p10,
            'P11': self._pipeline_p11,
            'P12': self._pipeline_p12,
            'P13': self._pipeline_p13,
            'P14': self._pipeline_p14,
            'P15': self._pipeline_p15,
        }
        
        if pipeline_id not in pipeline_methods:
            raise ValueError(f"Unknown pipeline ID: {pipeline_id}")
        
        print(f"[EXPERIMENT] Running Pipeline {pipeline_id}")
        results = pipeline_methods[pipeline_id](query, language, model_names, contexts, max_context_length)
        
        # Add metadata
        results['pipeline_id'] = pipeline_id
        results['execution_time'] = time.time() - start_time
        results['timestamp'] = time.time()
        
        print(f"[EXPERIMENT] Pipeline {pipeline_id} completed in {results['execution_time']:.2f}s")
        return results
    
    # ==================== PIPELINE IMPLEMENTATIONS ====================
    
    def _pipeline_p1(self, query: str, language: str, model_names: List[str], 
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P1: Single LLM, Code generation only, Optional CodeQL detection, No fixing
        Non-collaborative baseline for minimal security awareness.
        """
        model = model_names[0]  # Use first model only
        
        # Step 1: Code Generation
        code = self.llm_manager.generate_code(model, query, language, contexts, max_context_length)
        
        # Step 2: Optional CodeQL Detection
        codeql_report = None
        try:
            codeql_report = self.codeql_analyzer.analyze_code(code, language)
        except Exception as e:
            codeql_report = f"CodeQL analysis failed: {str(e)}"
        
        return {
            'description': 'Non-collaborative baseline for minimal security awareness',
            'generated_code': code,
            'codeql_report': codeql_report,
            'final_code': code,
            'collaboration_type': 'None',
            'phases': ['generation', 'detection']
        }
    
    def _pipeline_p2(self, query: str, language: str, model_names: List[str], 
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P2: Single LLM, Sequential Collaboration for all phases
        Strong monolithic baseline for comparison.
        """
        model = model_names[0]  # Use first model only
        
        # Use Sequential Collaboration with single model
        results = self.collaboration_framework.sequential_collaboration(
            query, language, [model], contexts, max_context_length
        )
        
        return {
            'description': 'Strong monolithic baseline for comparison',
            'collaboration_results': results,
            'final_code': results['final_code'],
            'collaboration_type': 'Sequential Collaboration',
            'phases': ['generation', 'detection', 'patching']
        }
    
    def _pipeline_p3(self, query: str, language: str, model_names: List[str], 
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P3: Multiple LLMs, Parallel Solution Synthesis for generation, CodeQL detection, No fixing
        Measures benefit of diverse generation without feedback.
        """
        # Step 1: Parallel generation
        parallel_results = self.collaboration_framework.parallel_solution_synthesis(
            query, language, model_names, contexts, max_context_length
        )
        
        # Step 2: CodeQL analysis on best solution
        best_code = parallel_results['final_code']
        codeql_report = None
        try:
            codeql_report = self.codeql_analyzer.analyze_code(best_code, language)
        except Exception as e:
            codeql_report = f"CodeQL analysis failed: {str(e)}"
        
        return {
            'description': 'Measures benefit of diverse generation without feedback',
            'parallel_results': parallel_results,
            'codeql_report': codeql_report,
            'final_code': best_code,
            'collaboration_type': 'Parallel Solution Synthesis',
            'phases': ['generation', 'detection']
        }
    
    def _pipeline_p4(self, query: str, language: str, model_names: List[str], 
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P4: Multiple LLMs, PSS generation, CodeQL detection, Sequential fixing fallback
        Hybrid ensemble + fallback strategy for robustness.
        """
        # Step 1: Parallel generation and detection
        parallel_results = self.collaboration_framework.parallel_solution_synthesis(
            query, language, model_names, contexts, max_context_length
        )
        
        best_code = parallel_results['final_code']
        
        # Step 2: CodeQL analysis
        codeql_report = None
        try:
            codeql_report = self.codeql_analyzer.analyze_code(best_code, language)
        except Exception as e:
            codeql_report = f"CodeQL analysis failed: {str(e)}"
        
        # Step 3: Fallback fixing if vulnerable
        final_code = best_code
        if codeql_report and ("vulnerable" in codeql_report.lower() or "CWE" in codeql_report):
            # Use first model for fallback fixing
            final_code = self.llm_manager.patch_code_single(
                model_names[0], best_code, codeql_report, {}, max_context_length
            )
        
        return {
            'description': 'Hybrid ensemble + fallback strategy for robustness',
            'parallel_results': parallel_results,
            'codeql_report': codeql_report,
            'final_code': final_code,
            'collaboration_type': 'PSS + Sequential Fixing',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p5(self, query: str, language: str, model_names: List[str],
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P5: Multiple LLMs, Peer Review generation, CodeQL detection, No fixing
        Evaluates benefit of collaborative critique in generation.
        """
        # Step 1: Peer Review for generation
        peer_review_results = self.collaboration_framework.peer_review(
            query, language, model_names, contexts, max_context_length
        )

        # Step 2: CodeQL analysis
        best_code = peer_review_results['final_code']
        codeql_report = None
        try:
            codeql_report = self.codeql_analyzer.analyze_code(best_code, language)
        except Exception as e:
            codeql_report = f"CodeQL analysis failed: {str(e)}"

        return {
            'description': 'Evaluates benefit of collaborative critique in generation',
            'peer_review_results': peer_review_results,
            'codeql_report': codeql_report,
            'final_code': best_code,
            'collaboration_type': 'Peer Review',
            'phases': ['generation', 'detection']
        }

    def _pipeline_p6(self, query: str, language: str, model_names: List[str],
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P6: Multiple LLMs, Peer Review generation, Sequential detection, Sequential fixing
        Tests multi-phase collaboration impact.
        """
        # Step 1: Peer Review for generation
        peer_review_results = self.collaboration_framework.peer_review(
            query, language, model_names, contexts, max_context_length
        )

        # Step 2: Sequential detection using LLM
        best_code = peer_review_results['final_code']
        detection_model = model_names[0]
        llm_vulnerability_report = self.llm_manager.llm_to_check_vulnerability(
            best_code, language, detection_model, contexts, max_context_length
        )

        # Step 3: Sequential fixing
        final_code = best_code
        if "Vulnerable : YES" in llm_vulnerability_report:
            final_code = self.llm_manager.patch_code_single(
                detection_model, best_code, llm_vulnerability_report, {}, max_context_length
            )

        return {
            'description': 'Tests multi-phase collaboration impact',
            'peer_review_results': peer_review_results,
            'llm_detection_report': llm_vulnerability_report,
            'final_code': final_code,
            'collaboration_type': 'Peer Review + Sequential',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p7(self, query: str, language: str, model_names: List[str],
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P7: Multiple LLMs, Sequential generation, Peer Review detection, Sequential fixing
        Tests multi-phase collaboration impact with different phase assignments.
        """
        # Step 1: Sequential generation
        generation_model = model_names[0]
        generated_code = self.llm_manager.generate_code(
            generation_model, query, language, contexts, max_context_length
        )

        # Step 2: Peer Review for detection (multiple models review for vulnerabilities)
        detection_reviews = {}
        for model in model_names:
            vulnerability_report = self.llm_manager.llm_to_check_vulnerability(
                generated_code, language, model, contexts, max_context_length
            )
            detection_reviews[model] = vulnerability_report

        # Step 3: Sequential fixing based on consensus
        final_code = generated_code
        vulnerable_count = sum(1 for report in detection_reviews.values() if "Vulnerable : YES" in report)

        if vulnerable_count > len(model_names) // 2:  # Majority says vulnerable
            # Use first model for fixing
            combined_reports = "\n\n".join([f"{model}: {report}" for model, report in detection_reviews.items()])
            final_code = self.llm_manager.patch_code_single(
                model_names[0], generated_code, combined_reports, {}, max_context_length
            )

        return {
            'description': 'Tests multi-phase collaboration with different phase assignments',
            'generated_code': generated_code,
            'detection_reviews': detection_reviews,
            'final_code': final_code,
            'collaboration_type': 'Sequential + Peer Review',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p8(self, query: str, language: str, model_names: List[str],
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P8: Multiple LLMs, No collaborative generation, Chain of Debate detection, Sequential fixing
        Studies reasoning-based collaborative detection.
        """
        # Step 1: Simple generation (first model)
        generation_model = model_names[0]
        generated_code = self.llm_manager.generate_code(
            generation_model, query, language, contexts, max_context_length
        )

        # Step 2: Chain of Debate for detection
        # Simulate debate by using the code as input to chain of debate
        debate_results = self.collaboration_framework.chain_of_debate(
            f"Analyze this code for security vulnerabilities:\n{generated_code}",
            language, model_names, contexts, max_context_length, rounds=2
        )

        # Step 3: Sequential fixing based on debate verdict
        final_code = generated_code
        if "VULNERABLE" in debate_results['final_verdict']:
            final_code = self.llm_manager.patch_code_single(
                model_names[0], generated_code, debate_results['final_verdict'], {}, max_context_length
            )

        return {
            'description': 'Studies reasoning-based collaborative detection',
            'generated_code': generated_code,
            'debate_results': debate_results,
            'final_code': final_code,
            'collaboration_type': 'Chain of Debate',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p9(self, query: str, language: str, model_names: List[str],
                    contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P9: Multiple LLMs, No collaborative generation, Chain of Debate + CodeQL detection, Sequential fixing
        Explores hybrid symbolic + adversarial detection.
        """
        # Step 1: Simple generation
        generation_model = model_names[0]
        generated_code = self.llm_manager.generate_code(
            generation_model, query, language, contexts, max_context_length
        )

        # Step 2: Hybrid detection - Chain of Debate + CodeQL
        debate_results = self.collaboration_framework.chain_of_debate(
            f"Analyze this code for security vulnerabilities:\n{generated_code}",
            language, model_names, contexts, max_context_length, rounds=2
        )

        codeql_report = None
        try:
            codeql_report = self.codeql_analyzer.analyze_code(generated_code, language)
        except Exception as e:
            codeql_report = f"CodeQL analysis failed: {str(e)}"

        # Step 3: Sequential fixing based on combined analysis
        final_code = generated_code
        debate_vulnerable = "VULNERABLE" in debate_results['final_verdict']
        codeql_vulnerable = codeql_report and ("vulnerable" in codeql_report.lower() or "CWE" in codeql_report)

        if debate_vulnerable or codeql_vulnerable:
            combined_report = f"Debate Verdict: {debate_results['final_verdict']}\n\nCodeQL Report: {codeql_report}"
            final_code = self.llm_manager.patch_code_single(
                model_names[0], generated_code, combined_report, {}, max_context_length
            )

        return {
            'description': 'Explores hybrid symbolic + adversarial detection',
            'generated_code': generated_code,
            'debate_results': debate_results,
            'codeql_report': codeql_report,
            'final_code': final_code,
            'collaboration_type': 'Chain of Debate + CodeQL',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p10(self, query: str, language: str, model_names: List[str],
                     contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P10: Multiple LLMs, PSS generation, AST-based detection, Sequential fixing
        Graph-based static analysis only; single model involvement in fixing.
        """
        # Step 1: Parallel generation
        parallel_results = self.collaboration_framework.parallel_solution_synthesis(
            query, language, model_names, contexts, max_context_length
        )

        # Step 2: AST-based detection
        best_code = parallel_results['final_code']
        ast_report = self.llm_manager.llm_to_check_vulnerability_external_ast_analysis(
            best_code, model_names[0], language
        )

        # Step 3: Sequential fixing
        final_code = best_code
        if "Vulnerable : YES" in ast_report:
            final_code = self.llm_manager.patch_code_single(
                model_names[0], best_code, ast_report, {}, max_context_length
            )

        return {
            'description': 'Graph-based static analysis only; single model involvement in fixing',
            'parallel_results': parallel_results,
            'ast_report': ast_report,
            'final_code': final_code,
            'collaboration_type': 'PSS + AST Analysis',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p11(self, query: str, language: str, model_names: List[str],
                     contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P11: Multiple LLMs, No collaborative generation, CodeQL detection, PSS fixing
        Static detection followed by multiple independent fixes.
        """
        # Step 1: Simple generation
        generation_model = model_names[0]
        generated_code = self.llm_manager.generate_code(
            generation_model, query, language, contexts, max_context_length
        )

        # Step 2: CodeQL detection
        codeql_report = None
        try:
            codeql_report = self.codeql_analyzer.analyze_code(generated_code, language)
        except Exception as e:
            codeql_report = f"CodeQL analysis failed: {str(e)}"

        # Step 3: Parallel fixing if vulnerable
        final_code = generated_code
        parallel_fixes = {}

        if codeql_report and ("vulnerable" in codeql_report.lower() or "CWE" in codeql_report):
            for model in model_names:
                fixed_code = self.llm_manager.patch_code_single(
                    model, generated_code, codeql_report, {}, max_context_length
                )
                parallel_fixes[model] = fixed_code

            # Select best fix (could be enhanced with ranking)
            final_code = list(parallel_fixes.values())[0] if parallel_fixes else generated_code

        return {
            'description': 'Static detection followed by multiple independent fixes',
            'generated_code': generated_code,
            'codeql_report': codeql_report,
            'parallel_fixes': parallel_fixes,
            'final_code': final_code,
            'collaboration_type': 'CodeQL + PSS Fixing',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p12(self, query: str, language: str, model_names: List[str],
                     contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P12: Multiple LLMs, PSS generation, CodeQL detection, PSS fixing
        Fully parallel pipeline with multiple models per phase.
        """
        # Step 1: Parallel generation
        parallel_generation = self.collaboration_framework.parallel_solution_synthesis(
            query, language, model_names, contexts, max_context_length
        )

        # Step 2: CodeQL detection on best solution
        best_code = parallel_generation['final_code']
        codeql_report = None
        try:
            codeql_report = self.codeql_analyzer.analyze_code(best_code, language)
        except Exception as e:
            codeql_report = f"CodeQL analysis failed: {str(e)}"

        # Step 3: Parallel fixing if vulnerable
        final_code = best_code
        parallel_fixes = {}

        if codeql_report and ("vulnerable" in codeql_report.lower() or "CWE" in codeql_report):
            for model in model_names:
                fixed_code = self.llm_manager.patch_code_single(
                    model, best_code, codeql_report, {}, max_context_length
                )
                parallel_fixes[model] = fixed_code

            # Create ensemble of fixes
            if parallel_fixes:
                ensemble_code = self.collaboration_framework._create_ensemble_solution(
                    parallel_fixes, {model: 1 for model in parallel_fixes}, language, contexts
                )
                final_code = ensemble_code or list(parallel_fixes.values())[0]

        return {
            'description': 'Fully parallel pipeline with multiple models per phase',
            'parallel_generation': parallel_generation,
            'codeql_report': codeql_report,
            'parallel_fixes': parallel_fixes,
            'final_code': final_code,
            'collaboration_type': 'Fully Parallel (PSS)',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p13(self, query: str, language: str, model_names: List[str],
                     contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P13: Multiple LLMs, Pair Programming generation, CodeQL detection, Sequential fixing
        Assesses impact of real-time, dialogic collaboration.
        """
        # Step 1: Pair Programming for generation
        pair_programming_results = self.collaboration_framework.pair_programming(
            query, language, model_names, contexts, max_context_length, iterations=3
        )

        # Step 2: CodeQL detection
        generated_code = pair_programming_results['final_code']
        codeql_report = None
        try:
            codeql_report = self.codeql_analyzer.analyze_code(generated_code, language)
        except Exception as e:
            codeql_report = f"CodeQL analysis failed: {str(e)}"

        # Step 3: Sequential fixing
        final_code = generated_code
        if codeql_report and ("vulnerable" in codeql_report.lower() or "CWE" in codeql_report):
            final_code = self.llm_manager.patch_code_single(
                model_names[0], generated_code, codeql_report, {}, max_context_length
            )

        return {
            'description': 'Assesses impact of real-time, dialogic collaboration',
            'pair_programming_results': pair_programming_results,
            'codeql_report': codeql_report,
            'final_code': final_code,
            'collaboration_type': 'Pair Programming',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p14(self, query: str, language: str, model_names: List[str],
                     contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P14: Multiple LLMs, PSS for all phases, no interaction
        Fully parallel workflow with no interaction or feedback.
        """
        # Step 1: Parallel generation
        parallel_solutions = {}
        for model in model_names:
            code = self.llm_manager.generate_code(model, query, language, contexts, max_context_length)
            parallel_solutions[model] = code

        # Step 2: Parallel detection (no interaction)
        parallel_detections = {}
        for model, code in parallel_solutions.items():
            vulnerability_report = self.llm_manager.llm_to_check_vulnerability(
                code, language, model, contexts, max_context_length
            )
            parallel_detections[model] = vulnerability_report

        # Step 3: Parallel fixing (no interaction)
        parallel_fixes = {}
        for model, code in parallel_solutions.items():
            detection_report = parallel_detections[model]
            if "Vulnerable : YES" in detection_report:
                fixed_code = self.llm_manager.patch_code_single(
                    model, code, detection_report, {}, max_context_length
                )
                parallel_fixes[model] = fixed_code
            else:
                parallel_fixes[model] = code

        # Select final code (first model's result)
        final_code = list(parallel_fixes.values())[0] if parallel_fixes else ""

        return {
            'description': 'Fully parallel workflow with no interaction or feedback',
            'parallel_solutions': parallel_solutions,
            'parallel_detections': parallel_detections,
            'parallel_fixes': parallel_fixes,
            'final_code': final_code,
            'collaboration_type': 'No Interaction (Parallel)',
            'phases': ['generation', 'detection', 'fixing']
        }

    def _pipeline_p15(self, query: str, language: str, model_names: List[str],
                     contexts: List[str], max_context_length: int) -> Dict[str, Any]:
        """
        P15: Single LLM per phase, Sequential execution, no collaboration within phases
        Ablation baseline: no phase-level collaboration, only role separation.
        """
        if len(model_names) < 3:
            # Reuse models if not enough provided
            generation_model = model_names[0]
            detection_model = model_names[1] if len(model_names) > 1 else model_names[0]
            fixing_model = model_names[2] if len(model_names) > 2 else model_names[0]
        else:
            generation_model = model_names[0]
            detection_model = model_names[1]
            fixing_model = model_names[2]

        # Step 1: Generation (single model)
        generated_code = self.llm_manager.generate_code(
            generation_model, query, language, contexts, max_context_length
        )

        # Step 2: Detection (single model)
        vulnerability_report = self.llm_manager.llm_to_check_vulnerability(
            generated_code, language, detection_model, contexts, max_context_length
        )

        # Step 3: Fixing (single model)
        final_code = generated_code
        if "Vulnerable : YES" in vulnerability_report:
            final_code = self.llm_manager.patch_code_single(
                fixing_model, generated_code, vulnerability_report, {}, max_context_length
            )

        return {
            'description': 'Ablation baseline: no phase-level collaboration, only role separation',
            'generated_code': generated_code,
            'vulnerability_report': vulnerability_report,
            'final_code': final_code,
            'collaboration_type': 'Role Separation Only',
            'phases': ['generation', 'detection', 'fixing'],
            'model_assignments': {
                'generation': generation_model,
                'detection': detection_model,
                'fixing': fixing_model
            }
        }

    # ==================== UTILITY METHODS ====================

    def run_all_pipelines(self, query: str, language: str, model_names: List[str],
                         contexts: List[str] = [], max_context_length: int = 3000) -> Dict[str, Any]:
        """
        Run all 15 experimental pipelines and return comprehensive results.

        Args:
            query: Code generation request
            language: Programming language
            model_names: List of model names to use
            contexts: Additional context information
            max_context_length: Maximum context length

        Returns:
            Dictionary containing results from all pipelines
        """
        all_results = {}
        pipeline_ids = [f'P{i}' for i in range(1, 16)]

        print(f"[EXPERIMENT] Running all {len(pipeline_ids)} pipelines")
        start_time = time.time()

        for pipeline_id in pipeline_ids:
            try:
                result = self.run_pipeline(
                    pipeline_id, query, language, model_names, contexts, max_context_length
                )
                all_results[pipeline_id] = result
                print(f"[EXPERIMENT] ✓ {pipeline_id} completed")
            except Exception as e:
                print(f"[EXPERIMENT] ✗ {pipeline_id} failed: {str(e)}")
                all_results[pipeline_id] = {
                    'error': str(e),
                    'pipeline_id': pipeline_id,
                    'status': 'failed'
                }

        total_time = time.time() - start_time
        print(f"[EXPERIMENT] All pipelines completed in {total_time:.2f}s")

        # Add summary metadata
        all_results['_metadata'] = {
            'total_pipelines': len(pipeline_ids),
            'successful_pipelines': len([r for r in all_results.values() if 'error' not in r]),
            'failed_pipelines': len([r for r in all_results.values() if 'error' in r]),
            'total_execution_time': total_time,
            'query': query,
            'language': language,
            'model_names': model_names,
            'timestamp': time.time()
        }

        return all_results

    def get_pipeline_description(self, pipeline_id: str) -> str:
        """Get description of a specific pipeline."""
        descriptions = {
            'P1': 'Single LLM, Code generation only, Optional CodeQL detection, No fixing',
            'P2': 'Single LLM, Sequential Collaboration for all phases',
            'P3': 'Multiple LLMs, PSS generation, CodeQL detection, No fixing',
            'P4': 'Multiple LLMs, PSS generation, CodeQL detection, Sequential fixing fallback',
            'P5': 'Multiple LLMs, Peer Review generation, CodeQL detection, No fixing',
            'P6': 'Multiple LLMs, Peer Review generation, Sequential detection, Sequential fixing',
            'P7': 'Multiple LLMs, Sequential generation, Peer Review detection, Sequential fixing',
            'P8': 'Multiple LLMs, No collaborative generation, Chain of Debate detection, Sequential fixing',
            'P9': 'Multiple LLMs, No collaborative generation, Chain of Debate + CodeQL detection, Sequential fixing',
            'P10': 'Multiple LLMs, PSS generation, AST-based detection, Sequential fixing',
            'P11': 'Multiple LLMs, No collaborative generation, CodeQL detection, PSS fixing',
            'P12': 'Multiple LLMs, PSS generation, CodeQL detection, PSS fixing',
            'P13': 'Multiple LLMs, Pair Programming generation, CodeQL detection, Sequential fixing',
            'P14': 'Multiple LLMs, PSS for all phases, no interaction',
            'P15': 'Single LLM per phase, Sequential execution, no collaboration within phases'
        }
        return descriptions.get(pipeline_id, f"Unknown pipeline: {pipeline_id}")

    def export_results(self, results: Dict[str, Any], filename: str = None) -> str:
        """
        Export pipeline results to JSON file.

        Args:
            results: Results dictionary from run_pipeline or run_all_pipelines
            filename: Output filename (auto-generated if None)

        Returns:
            Path to exported file
        """
        if filename is None:
            timestamp = int(time.time())
            filename = f"experiment_results_{timestamp}.json"

        # Ensure the results directory exists
        import os
        results_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'results')
        os.makedirs(results_dir, exist_ok=True)

        filepath = os.path.join(results_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)

        print(f"[EXPERIMENT] Results exported to: {filepath}")
        return filepath

from collections import defaultdict
import os
import re
import time
import ast
import traceback
import yaml
import requests
from pycparser import c_parser,c_ast
from typing import List, Dict, Any, Optional
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from openai import OpenAI  # Updated for openai>=1.0
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
import textwrap
# Load a suitable model — you can pick one tuned for code or general text
# e.g., 'all-MiniLM-L6-v2' for general text, or 'microsoft/codebert-base' for code
class LLMManager:
    def __init__(self, config_path: str = "config/config.yaml"):
        print("[DEBUG] Initializing LLMManager")
        try:
            # Load configuration
            with open(config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            print("[DEBUG] Config loaded successfully") 

            self.api_key = self.config['openai']['api_key']
            print("[DEBUG] OpenAI API key configured")
            self.model = SentenceTransformer('microsoft/codebert-base')  # or any other SentenceTransformer model

            # Initialize clients for different models with appropriate base URLs
            self.MODEL_REGISTRY = {
                "gpt-3.5-turbo": {
                    "client": OpenAI(api_key=self.api_key),
                    "model_name": "gpt-3.5-turbo"
                },
                "gpt-4o": {
                    "client": OpenAI(api_key=self.api_key),
                    "model_name": "gpt-4o"
                },
                "gpt-4": {
                    "client": OpenAI(api_key=self.api_key),
                    "model_name": "gpt-4"
                },
                "codellama": {
                    "client": OpenAI(api_key='ollama', base_url='http://localhost:11434/v1'),#'http://**************:8995/v1'),#
                    "model_name": "codellama:7b-instruct-fp16"
                },
                "mistral": {
                    "client": OpenAI(api_key='ollama', base_url='http://localhost:11434/v1'),#'http://**************:8992/v1'), #'http://localhost:11434/v1'
                    "model_name": "mistral:7b-instruct-fp16"
                },
                "meta_llama": {
                    "client": OpenAI(api_key='ollama', base_url='http://localhost:11434/v1'), #'base_url='http://localhost:11434/v1'
                    "model_name": "llama3:latest"
                },
               
            }

            print(f"[DEBUG] Initialized {len(self.MODEL_REGISTRY)} model clients")
        except Exception as e:
            print(f"[ERROR] Error initializing LLMManager: {e}")
            traceback.print_exc()
            raise

    def generate_response(self, prompt: str, model_name: str = None) -> str:
        try:
            model_name = model_name or self.config['llm'].get('model_name', 'gpt-3.5-turbo')
            if model_name == 'gpt-3.5-turbo' or model_name =='gpt-4o' or model_name =='gpt-4':
                return self._generate_openai_response(prompt, model_name)
            else:
                return self._generate_local_response(prompt, model_name)
        except Exception as e:
            print(f"[ERROR] Error in generate_response: {e}")
            return f"Error generating response: {str(e)}"

    def _generate_openai_response(self, messages, model_name: str) -> str:
        try:
            client = self.MODEL_REGISTRY[model_name]['client']
            response = client.chat.completions.create(
                model=model_name,
                messages=messages,
                max_tokens=3000,
                temperature=0
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            traceback.print_exc()
            return f"Error generating response from OpenAI: {str(e)}"

    def _generate_local_response(self, messages, model_name: str) -> str:
        if model_name not in self.MODEL_REGISTRY:
            print(self.MODEL_REGISTRY)
            raise ValueError(f"Model {model_name} not found in registry")

        messages = messages
        max_tokens = self.config['llm'].get('max_new_tokens', 3000)
        temperature = self.config['llm'].get('temperature', 0)

        return self._generate_model_response(model_name, messages, temp=temperature, tokens=max_tokens)

    def _generate_model_response(self, model_key: str, messages: List[Dict[str, str]], temp: float = 0, tokens: int = 3000) -> str:
        client_info = self.MODEL_REGISTRY[model_key]
        client, model_name = client_info['client'], client_info['model_name']

        messages = self._apply_sliding_window(messages)
        max_tokens_adjusted = self._get_max_tokens(messages, tokens)
        if max_tokens_adjusted <= 0:
            return "Error: Input too long for model context window."

        try:
            completion = client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=temp,
                max_tokens=max_tokens_adjusted
            )
            content = completion.choices[0].message.content
            return self._remove_repetitive_code(self._remove_repetitive_segments(content))
        except Exception as e:
            traceback.print_exc()
            return f"Error generating response: {str(e)}"

    def _apply_sliding_window(self, messages, max_length=6):
        if len(messages) <= max_length:
            return messages
        first_three = messages[:3]
        last_messages, role_tracker, count = [], None, 0
        for i in range(len(messages) - 1, 2, -1):
            if messages[i]["role"] != role_tracker:
                last_messages.append(messages[i])
                role_tracker = messages[i]["role"]
                count += 1
                if count == 4:
                    break
        last_messages.reverse()
        return self._enforce_message_alternation(first_three + last_messages)

    def _enforce_message_alternation(self, messages):
        if not messages:
            return []
        filtered = [messages[0]]
        for msg in messages[1:]:
            if msg["role"] != filtered[-1]["role"]:
                filtered.append(msg)
        return filtered

    def _remove_repetitive_segments(self, text):
        lines = text.split('\n')
        seen, output = set(), []
        for line in lines:
            if line not in seen:
                seen.add(line)
                output.append(line)
        return '\n'.join(output)

    def _remove_repetitive_code(self, code):
        lines = code.splitlines()
        unique_lines = []
        seen = set()
        for line in lines:
            if line.strip().startswith('#include') or re.match(r'\s*(void|int|char|float|double|struct|typedef|enum)\s+', line):
                if line not in seen:
                    seen.add(line)
                    unique_lines.append(line)
            else:
                unique_lines.append(line)
        return '\n'.join(unique_lines).strip()

    def _get_max_tokens(self, messages, tokens_limit):
        total_input = sum(len(m["content"].split()) for m in messages)
        return min(tokens_limit, 4096 - total_input)

    def generate_code(self, model_name: str, query: str, language: str, contexts: List[str]= [], max_context_length: int = 3000) -> str:
        try:

            messages=[]
            if(contexts!=[]):
                context = "\n ".join(contexts)
                if len(context) > max_context_length:
                    context = context[:max_context_length] + "..."

            else:
                context=""
            role="You are a code generator "
            if(context!=''):
                role=role+f"""      
            Some related context is: {context}"""

            content= f"""Instructions: 
            - The Code MUST be Secure and Functionally Correct.
            - The Code MUST be runnable, complete and without errors. 
            - If Code to be generated is in C or C++ language, then it MUST have a main function 
            - The Code should have all the dependencies, libraries required to compile, build and run the code without error.
            
            user request: {query}
            
            Provide ONLY the code:
            ```{language}"""
            
            messages.append({"role": "system", "content":role})
            messages.append({"role": "user", "content":content})
            #`
            response = self.generate_response(messages, model_name)
            if not response.endswith("```"):
                response += "\n```"
            return response
        except Exception as e:
            traceback.print_exc()
            return f"Error generating code: {str(e)}"

    def llm_to_check_vulnerability(self,code, language: str, model_name: str,  contexts: List[str] = [], max_context_length: int = 3000) -> str:
        try:
            print("[DEBUG] Analyzing code for vulnerabilities")
            context = '\n'.join(f' {text}' for text in contexts)
            
            if len(context) > max_context_length:
                context = context[:max_context_length] + "..."

            messages=[]

            
            role=f""" You are a software security expert. Act as a static vulnerability detector """

            if(context!=''):
                role=role+f"""      
            Some related context is: {context}"""

            content = (
                    "Task: Is the given code susceptible to any security vulnerabilities (MITRE) [USE step-by-step analysis].\n"
                    "Code Snippet:" + code + "\n"
                    "Analyze the above code **line by line** using chain-of-thought reasoning.\n\n"
                    "If the code is secure, output: Vulnerable : NO"
                    "else use the output response format below\n"
                    "Vulnerable : YES"
                    " CWE-ID: ________ | Description:__________| List of Vulnerable Line(s):___________| One-Line Justification:\n\n"
                )


            messages.append({"role": "system", "content":role})
            messages.append({"role": "user", "content":content})

            return self.generate_response(messages, model_name)
        except Exception as e:
            print(f"[ERROR] Failed in llm_to_check_vulnerability: {e}")
            traceback.print_exc()
            return f"Error: {e}"
        
    def llm_to_check_vulnerability_ast_analysis(self,code, language: str, model_name: str,  contexts: List[str] = [], max_context_length: int = 3000) -> str:
        try:
            print("[DEBUG] Analyzing code for vulnerabilities")
            context = '\n'.join(f' {text}' for text in contexts)
            
            if len(context) > max_context_length:
                context = context[:max_context_length] + "..."

            messages=[]

            
            role=f""" You are a software security expert. Act as a static vulnerability detector """

            if(context!=''):
                role=role+f"""      
            Some related context is: {context}"""

            content = (
                    "Task: Is the given code susceptible to any security vulnerabilities (MITRE) [USE step-by-step analysis].\n"
                    "Code Snippet:" + code + "\n"
                    "Incorporate step-by-step analysis using control flow graphs, data flow graphs, and abstract syntax trees (AST) to support your reasoning. \n\n"
                    "If the code is secure, output: Vulnerable : NO"
                    "else use the output response format below\n"
                    "Vulnerable : YES"
                    " CWE-ID: ________ | Description:__________| List of Vulnerable Line(s):___________| One-Line Justification:\n\n"
                )


            messages.append({"role": "system", "content":role})
            messages.append({"role": "user", "content":content})

            return self.generate_response(messages, model_name)
        except Exception as e:
            print(f"[ERROR] Failed in llm_to_check_vulnerability_ast_analysis: {e}")
            traceback.print_exc()
            return f"Error: {e}"
        

   

    def extract_ast_summary(self,code: str, language: str = "python") -> str:
        """
        Extracts a summary of the AST node types in the provided code.
        
        Parameters:
            code (str): The source code to analyze.
            language (str): Either 'python' or 'c'.
            
        Returns:
            str: A string summary of AST node types.
        """
        if language.lower() == "python":
            try:
                tree = ast.parse(code)
                summary = [type(node).__name__ for node in ast.walk(tree)]
                seen = set()
                ordered_summary = [n for n in summary if not (n in seen or seen.add(n))]
                return " → ".join(ordered_summary)
            except Exception as e:
                return f"[PYTHON AST ERROR] {e}"

        elif language.lower() == "c":
            try:
                parser = c_parser.CParser()
                tree = parser.parse(code)
                summary = []

                def visit(node):
                    if node is None:
                        return
                    summary.append(type(node).__name__)
                    for _, child in node.children():
                        visit(child)

                visit(tree)
                seen = set()
                ordered_summary = [n for n in summary if not (n in seen or seen.add(n))]
                return " → ".join(ordered_summary)
            except Exception as e:
                return f"[C AST ERROR] {e}"

        else:
            return "[ERROR] Unsupported language. Use 'python' or 'c'."


   
    def extract_dataflow_taint(self, code: str, language: str = "python") -> str:
            if language.lower() == "python":
                try:
                    tree = ast.parse(code)
                    input_vars = set()
                    sinks = {'eval', 'exec', 'os.system', 'subprocess.call', 'subprocess.run', 'system'}
                    flows = []

                    class Analyzer(ast.NodeVisitor):
                        def visit_FunctionDef(self, node):
                            # Function args are inputs
                            for arg in node.args.args:
                                input_vars.add(arg.arg)
                            self.generic_visit(node)

                        def visit_Assign(self, node):
                            # Track variables assigned from input()
                            if isinstance(node.value, ast.Call) and isinstance(node.value.func, ast.Name):
                                if node.value.func.id == "input":
                                    for target in node.targets:
                                        if isinstance(target, ast.Name):
                                            input_vars.add(target.id)
                            self.generic_visit(node)

                        def visit_Call(self, node):
                            try:
                                # Get full function name (with module if any)
                                if isinstance(node.func, ast.Attribute):
                                    func_name = f"{ast.unparse(node.func.value)}.{node.func.attr}"
                                elif isinstance(node.func, ast.Name):
                                    func_name = node.func.id
                                else:
                                    func_name = ""

                                if func_name in sinks:
                                    # Check if any input var is passed as argument
                                    for arg in node.args:
                                        if isinstance(arg, ast.Name) and arg.id in input_vars:
                                            flows.append((arg.id, func_name))
                                        else:
                                            # For complex args, convert to string and check input vars presence
                                            arg_str = ast.unparse(arg)
                                            for var in input_vars:
                                                if var in arg_str:
                                                    flows.append((var, func_name))
                            except Exception:
                                pass
                            self.generic_visit(node)

                    Analyzer().visit(tree)
                    if flows:
                        return "; ".join([f"{src} → {sink}" for src, sink in flows])
                    else:
                        return "No obvious input → sink dataflows found."
                except Exception as e:
                    return f"[PYTHON Flow ERROR] {e}"

            # Existing C-language logic unchanged
            elif language.lower() == "c":
                # your existing C code...
                pass

            else:
                return "[ERROR] Unsupported language. Use 'python' or 'c'."

    def llm_to_check_vulnerability_external_ast_analysis(self, code: str,model_name, language: str = "python") -> str:
        try: 
            ast_summary = self.extract_ast_summary(code)
            dataflow = self.extract_dataflow_taint(code)
            print(ast_summary)
            print("\n\n\n",dataflow)
            messages=[]
            role="You are a software security expert. Act as a static vulnerability detector."

            content = f"""
            
            Below is a function written in {language}. It may contain one or more security vulnerabilities.

            Analyze the above code **line by line** using chain-of-thought reasoning.

            Use the following structural and semantic analysis to guide your review:

            ❖ Abstract Syntax Tree Summary:
            {ast_summary}

            ❖ Simplified Dataflow Analysis:
            {dataflow}

            ---
            Output Format: 
            "If the code is secure, output: Vulnerable : NO"
            "else use the output response format below\n"
                 "Vulnerable : YES"
                 " CWE-ID: ________ | Description:__________| List of Vulnerable Line(s):___________| One-Line Justification:\n\n"
            """

            messages.append({"role": "system", "content":role})
            messages.append({"role": "user", "content":content})

            return self.generate_response(messages, model_name)
        except Exception as e:
            print(f"[ERROR] Failed in generate_structured_vuln_prompt: {e}")
            traceback.print_exc()
            return f"Error: {e}"


    def llm_to_verify_explanation(self, code_snippet: str, explanation: str, model_name: str, max_context_length: int = 2000) -> str:
        """
        Uses a trusted LLM to verify whether an explanation correctly identifies vulnerabilities in code.
        Returns model response text.
        """
        prompt = (
                "You are a security analyst. Your task is to assess whether the explanation below correctly "
                "identifies and justifies a security vulnerability in the provided code snippet, based on the "
                "MITRE CWE (Common Weakness Enumeration) framework.\n\n"
                "Code Snippet:\n"
                f"{code_snippet}\n\n"
                "Explanation to Verify:\n"
                f"{explanation}\n\n"
                "Instructions:\n"
                "1. Determine whether the explanation accurately describes a real vulnerability in the code.\n"
                "2. Base your judgment on the MITRE CWE framework.\n"
                "3. Respond with either 'yes' or 'no' — followed by a brief, clear justification (1-3 sentences).\n\n"
                "Response Format (strictly follow this format):\n"
                "Answer: yes/no\n"
                "Justification: <one or two sentences explaining your reasoning>\n"
            )

        messages = [{"role": "user", "content": prompt}]
        return self.generate_response(messages, model_name)


    def fix_code(self, model_name: str, code_dict, explanations, contexts: List[str] = [], max_context_length: int = 3000) -> str:
        print("[DEBUG] Starting code fix process")

        # Unpack explanations if it's wrapped in a list
        
        
        # Combine all code snippets
        combined_code = "\n\n".join([f"{name}:\n{code}" for name, code in code_dict.items()])
        reasoning = "\n\n".join([f"{name}:\n{reason}" for name, reason in explanations.items()])
        print("[DEBUG] Combined code snippets:\n", combined_code)

        # Concatenate and truncate context if needed
        context = "\n\n".join(contexts) if contexts else ""
        if len(context) > max_context_length:
            context = context[:max_context_length] + "..."

       
        # Construct the full prompt
        full_prompt = f"""
    Input:
    - Code snippets:
    {combined_code}

    - Vulnerability Reasoning:
    Reasoning check: {reasoning}
   
    Task:
    1. Select the most secure code snippet based on the reasoning.
    2. If none of the code is fully secure, produce a fixed secure version that:
    - Fixes all identified vulnerabilities without introducing new ones.
    - Applies secure coding best practices (input validation, error handling, memory safety, encryption).
    - Removes any hardcoded sensitive data.
    - Is runnable and suitable for deployment with proper dependencies and main function if applicable.
    - Avoids insecure defaults and manages resources correctly.

    Output format (strict):
    
    a) Fixed Code:
    - Complete fixed code with visible changes 
    b) Final step-by-step verification that no security vulnerability exists in the fixed code.

    """

        # Role assignment
        role = "You are a software security expert. Review the code snippets and produce a single secure and non-vulnerable code."
        if context:
            role += f"\n\nContext:\n{context}"

        messages = [
            {"role": "system", "content": role},
            {"role": "user", "content": full_prompt}
        ]

        # Generate the response using the specified model
        response = self.generate_response(messages, model_name)
        print("[DEBUG] Generated fix:\n", response)
        return response
    
    def patch_code_single(
            self,
            model_name: str,
            code: str,
            explanations: str,  # now includes CodeQL reports as rationale,
            llm_explanations: dict={}, # now includes LLM reports as rationale
            max_context_length: int = 3000
        ) -> str:
            print("[DEBUG] Starting code fix process")

            # Combine code snippets from different models
            
            
            # Prompt to instruct the model on how to generate a fixed version
            # Use CodeQL report if available, otherwise use LLM explanations
            if explanations and explanations.strip():
                vulnerability_report_section = f"""
        Associated CodeQL Vulnerability Report:
        {explanations}"""
                analysis_instruction = "1. Analyze the code and the associated CodeQL findings."
                fix_instruction = "- Fixes all CodeQL-detected vulnerabilities."
            else:
                # Fallback to LLM explanations if CodeQL explanations are empty
                llm_reports = "\n\n".join([f"{name} LLM Report:\n{report}" for name, report in llm_explanations.items()]) if llm_explanations else "No specific vulnerability reports available."
                vulnerability_report_section = f"""
        Associated LLM Vulnerability Report:
        {llm_reports}"""
                analysis_instruction = "1. Analyze the code and the associated vulnerability findings."
                fix_instruction = "- Fixes all detected vulnerabilities."

            prompt = f"""
        You are a secure coding assistant. The following code snippet have been flagged with vulnerabilities. A security report explaining its weaknesses is provided.

        Input Code Snippet:
        {code}
        {vulnerability_report_section}

        Task:
        {analysis_instruction}
        2. If any code is not fully secure, produce a new fixed version that:
        {fix_instruction}
        - Avoids introducing new vulnerabilities.
        - Applies security best practices: input validation, memory safety, error handling, and secure defaults.
        - Eliminates hardcoded secrets or insecure cryptographic configurations.
        - Produces syntactically correct and deployable code.

        Expected Output (Strict Format):

        a) Fixed Code:
        - Provide the full secure, fixed code (no explanations inline).

        b) Security Verification:
        - Step-by-step reasoning to confirm that no known vulnerability remains in the code.
        """

            # Build system prompt (role) and messages
            role = "You are a software security expert tasked with producing the most secure version of given code using static analysis."
    

            messages = [
                {"role": "system", "content": role},
                {"role": "user", "content": prompt}
            ]

            # Generate secure code using the model
            response = self.generate_response(messages, model_name)
            print("[DEBUG] Generated fix response:\n", response[:800])  # Truncated preview

            return response



    def patch_code(
            self,
            model_name: str,
            code_dict: dict,
            explanations: dict={},  # now includes CodeQL reports as rationale
            llm_explanations: dict={},
            contexts: List[str] = [],
            max_context_length: int = 3000
        ) -> str:
            print("[DEBUG] Starting code fix process")

            # Combine code snippets from different models
            combined_code = "\n\n".join([f"{name} Code:\n{code}" for name, code in code_dict.items()])
            print("[DEBUG] Combined code snippets:\n", combined_code[:500])  # Preview
            
            if explanations and explanations.strip():
                reasoning = "\n\n".join([f"{name} CodeQL Report:\n{report}" for name, report in explanations.items()])
            # Combine explanations (e.g., CodeQL vulnerability reports)
            else:
                reasoning=''
            
            llm_reasoning = "\n\n".join([f"{name} LLM Report:\n{report}" for name, report in llm_explanations.items()])
            print("[DEBUG] Combined LLM reports:\n", llm_reasoning[:500])  # Preview

            # Concatenate context if any and truncate to max length
            context = "\n\n".join(contexts) if contexts else ""
            if len(context) > max_context_length:
                context = context[:max_context_length] + "..."

            # Prompt to instruct the model on how to generate a fixed version
            prompt = f"""
        You are a secure coding assistant. The following code snippets from different models have been flagged with vulnerabilities. Each is followed by a CodeQL security report explaining its weaknesses.

        Input Code Snippets:
        {combined_code}

        {reasoning}

        {llm_reasoning}

        Task:
        1. Analyze the code and the associated CodeQL findings.
        2. If any code is not fully secure, produce a new fixed version that:
        - Fixes all CodeQL-detected vulnerabilities.
        - Avoids introducing new vulnerabilities.
        - Applies security best practices: input validation, memory safety, error handling, and secure defaults.
        - Eliminates hardcoded secrets or insecure cryptographic configurations.
        - Produces syntactically correct and deployable code.

        Expected Output (Strict Format):

        a) Fixed Code:
        - Provide the full secure, fixed code (no explanations inline).

        b) Security Verification:
        - Step-by-step reasoning to confirm that no known vulnerability remains in the code.
        """

            # Build system prompt (role) and messages
            role = "You are a software security expert tasked with producing the most secure version of given code using static analysis."
            if context:
                role += f"\n\nContext:\n{context}"

            messages = [
                {"role": "system", "content": role},
                {"role": "user", "content": prompt}
            ]

            # Generate secure code using the model
            response = self.generate_response(messages, model_name)
            print("[DEBUG] Generated fix response:\n", response[:800])  # Truncated preview

            return response


    def embed_text_real(self,text: str) -> np.ndarray:
        """
        Generate embeddings for the input text using SentenceTransformer.
        Returns a numpy array embedding vector.
        """
        embedding = self.model.encode(text, convert_to_numpy=True, normalize_embeddings=True)
        return embedding

    def cosine_sim(self, a, b):
        # Ensure inputs are 2D arrays of shape (1, dim)
        a = np.array(a).reshape(1, -1)
        b = np.array(b).reshape(1, -1)
        return cosine_similarity(a, b)[0][0]

    def er_verification_prompt(self,explanation, code, language, model_name):
        """
        ERVP: Re-ask the model if it's sure about the vulnerability explanation.
        If the model flips answer or explanation is contradictory, downgrade vote."""
        prompt="""
            You are a software security expert reviewing the following code snippet:

            {code}

            Previous explanation flagged this code as vulnerable:

            {explanation}

            Please perform a thorough review and respond **explicitly** with one of these two labels at the start of your answer:

            - VULNERABLE: if the code contains a security vulnerability.
            - NOT VULNERABLE: if the code is secure .

            After the label, provide a clear, line-by-line reasoning explaining your decision, referencing specific code lines or constructs and the CWE-ID with description.

            Do not hedge or use ambiguous language."""
        response = self.generate_response(prompt, model_name)
        # Check if response starts with "VULNERABLE" or "NOT VULNERABLE"
        if not response.startswith("VULNERABLE") and not response.startswith("NOT VULNERABLE"):
            return False, response 
        return True, response

    def adversarial_reframe_prompt(self,explanation, code, language, model_name):
        """
        ARP: Generate counterarguments or flaws in explanation.
        Use one model to challenge others.
        """
        prompt = (f"Given this explanation of a vulnerability:\n{explanation}\n"
                f"Provide possible counterarguments or flaws in this logic, "
                f"pointing out why this might be a false positive or misunderstanding.")
        response = self.llm_to_check_vulnerability(
            code,
            language=language,
            model_name=model_name,
            contexts=[prompt],
            max_context_length=2000
        )
        # If response casts doubt, return False to indicate challenge succeeded
        if re.search(r"\b(no|not|unlikely|false positive|incorrect|error|flaw)\b", response, re.I):
            return False, response
        return True, response

    def reasoning_chain_check(self,explanation, code, language, model_name):
        """
        Request explanation in 3 steps:
        (1) vulnerable part, (2) consequence, (3) CWE or vulnerability class
        """
        prompt = (f"Explain the vulnerability in three steps:\n"
                f"1) Which part of the code is vulnerable?\n"
                f"2) What can happen due to it?\n"
                f"3) Which CWE or vulnerability class does it belong to?\n"
                f"Explanation to check:\n{explanation}")
        response = self.llm_to_check_vulnerability(
            code,
            language=language,
            model_name=model_name,
            contexts=[prompt],
            max_context_length=2000
        )
        # Simple heuristic: Check presence of 3 parts by looking for numbered steps or CWE mention
        steps_found = sum(bool(re.search(rf"{i}\)", response)) for i in range(1, 4))
        cwe_found = bool(re.search(r'CWE[-\s]?(\d+)', response, re.I))
        if steps_found >= 3 and cwe_found:
            return True, response
        return False, response
    def aggregate_with_cwe_awareness(self, explanations, model_weights, threshold=0.8):
        total_weight = 0
        cwe_votes = defaultdict(float)
        
        for expl_model, explanation in explanations.items():
            weight = float(model_weights.get(expl_model, 0.5))  # Cast to float
            total_weight += weight
            cwes = re.findall(r'CWE[-\s]?(\d+)', explanation, re.IGNORECASE)
            if cwes:
                for cwe in cwes:
                    cwe_votes[f"CWE-{cwe}"] += weight
        
        top_cwe_score = max(cwe_votes.values(), default=0)
        if total_weight == 0:
            return 'secure', {}  # Avoid division by zero
        if top_cwe_score / total_weight >= threshold:
            return 'insecure', cwe_votes
        return 'secure', {}


    def ensemble_with_cwe_and_weighted_voting(self,explanations_dict, model_weights, normal_vote_scores, cwe_threshold=0.8):
        # Step 1: CWE-aware aggregation
        cwe_status, cwe_votes = self.aggregate_with_cwe_awareness(
            explanations_dict, model_weights, threshold=cwe_threshold
        )

        if cwe_status == 'insecure':
            final_label = 'insecure'
            print(f"Detected insecurity based on CWE mentions with votes: {cwe_votes}")
        else:
            # Step 2: No strong CWE evidence, fallback to weighted voting
            weighted_score = 0.0
            total_weight = sum(model_weights.values())

            if total_weight == 0:
                # Avoid division by zero if weights sum to zero
                print("Warning: sum of model weights is zero. Defaulting to 'secure'.")
                final_label = 'secure'
            else:
                weighted_score = sum(
                    model_weights[m] * normal_vote_scores.get(m, 0) for m in model_weights
                ) / total_weight

                final_label = 'insecure' if weighted_score < 0 else 'secure'
                print(f"No strong CWE evidence, fallback to weighted voting with score {weighted_score:.4f}")

        print(f"Final label: {final_label}")
        return final_label


class SecureCodeCollaborationFramework:
    """
    A comprehensive framework implementing various collaboration strategies for secure code development.
    Leverages LLMManager for model interactions and implements multiple collaboration patterns.
    """

    def __init__(self, llm_manager: LLMManager):
        self.llm_manager = llm_manager

    # ==================== SEQUENTIAL COLLABORATION (SC) ====================

    def sequential_collaboration(self, query: str, language: str, model_names: list,
                               contexts: list = [], max_context_length: int = 3000) -> dict:
        """
        Sequential Collaboration: Agents perform tasks in fixed order (generation → detection → patching)
        """
        results = {
            'generation': {},
            'detection': {},
            'patching': {},
            'final_code': None
        }

        # Step 1: Code Generation
        print("[SC] Step 1: Code Generation")
        for model in model_names:
            code = self.llm_manager.generate_code(model, query, language, contexts, max_context_length)
            results['generation'][model] = code

        # Step 2: Vulnerability Detection
        print("[SC] Step 2: Vulnerability Detection")
        for model, code in results['generation'].items():
            vulnerability_report = self.llm_manager.llm_to_check_vulnerability(
                code, language, model, contexts, max_context_length
            )
            results['detection'][model] = vulnerability_report

        # Step 3: Code Patching
        print("[SC] Step 3: Code Patching")
        for model, code in results['generation'].items():
            vulnerability_report = results['detection'][model]
            if "Vulnerable : YES" in vulnerability_report:
                patched_code = self.llm_manager.patch_code_single(
                    model, code, vulnerability_report, {}, max_context_length
                )
                results['patching'][model] = patched_code
            else:
                results['patching'][model] = code  # No patching needed

        # Select best final code (could be enhanced with ranking)
        results['final_code'] = list(results['patching'].values())[0]
        return results

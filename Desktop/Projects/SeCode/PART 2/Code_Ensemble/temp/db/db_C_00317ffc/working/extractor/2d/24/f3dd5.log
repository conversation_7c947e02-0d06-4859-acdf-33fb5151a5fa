[E 18:07:35 65722] CodeQL C/C++ Extractor 2.11.2
[E 18:07:35 65722] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:07:35 65722] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /usr/bin/gcc code_00317ffc.c -o code_00317ffc
[E 18:07:36 65722] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:36 65722] Checking whether C compilation already happened.
[E 18:07:36 65722] Checking for tag c-compilation-happened
[E 18:07:36 65722] Checking CODEQL_TRACER_DB_ID 0000000000010021_0000000000000001
[E 18:07:36 65722] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:36 65722] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:36 65722] Unlocking DB
[E 18:07:36 65722] Unlocked DB
[E 18:07:36 65722] Exiting as C compilation already happened.

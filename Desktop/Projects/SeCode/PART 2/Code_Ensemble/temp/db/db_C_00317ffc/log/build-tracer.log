[T 18:07:15 65556] CodeQL CLI version 2.11.2
[T 18:07:15 65556] Initializing tracer.
[T 18:07:15 65556] Initialising tags...
[T 18:07:15 65556] ID set to 0000000000010014_0000000000000001 (parent root)
[T 18:07:15 65556] Initializing tracer.
[T 18:07:15 65556] Initialising tags...
[T 18:07:15 65556] ID set to 0000000000010014_0000000000000002 (parent root)
[T 18:07:15 65556] Warning: SEMMLE_EXEC and SEMMLE_EXECP not set. Falling back to path lookup on argv[0].
[T 18:07:15 65556] ==== Candidate to intercept: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx (canonical: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx) ====
[T 18:07:15 65556] Executing the following tracer actions:
[T 18:07:15 65556] Tracer actions:
[T 18:07:15 65556] pre_invocations(0)
[T 18:07:15 65556] post_invocations(0)
[T 18:07:15 65556] trace_languages(1): [cpp]
[T 18:07:15 65557] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/usr/bin/make.semmle.00010014.slice.x86_64: replacing existing signature
[T 18:07:15 65556] Initializing tracer.
[T 18:07:15 65556] Initialising tags...
[T 18:07:15 65556] ID set to 0000000000010014_0000000000000003 (parent 0000000000010014_0000000000000002)
[T 18:07:15 65556] ==== Candidate to intercept: /usr/bin/make (canonical: /usr/bin/make) ====
[T 18:07:15 65556] Executing the following tracer actions:
[T 18:07:15 65556] Tracer actions:
[T 18:07:15 65556] pre_invocations(0)
[T 18:07:15 65556] post_invocations(0)
[T 18:07:15 65556] trace_languages(1): [cpp]
[T 18:07:15 65561] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.00010014.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.00010014.slice.arm64: replacing existing signature
[T 18:07:16 65556] Initializing tracer.
[T 18:07:16 65556] Initialising tags...
[T 18:07:16 65556] ID set to 0000000000010014_0000000000000004 (parent 0000000000010014_0000000000000003)
[T 18:07:16 65556] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/make (canonical: /Library/Developer/CommandLineTools/usr/bin/make) ====
[T 18:07:16 65556] Executing the following tracer actions:
[T 18:07:16 65556] Tracer actions:
[T 18:07:16 65556] pre_invocations(0)
[T 18:07:16 65556] post_invocations(0)
[T 18:07:16 65556] trace_languages(1): [cpp]
[T 18:07:16 65566] Attempting to switch stdout/stderr to 4...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/usr/bin/gcc.semmle.00010014.slice.x86_64: replacing existing signature
[T 18:07:16 65569] Initializing tracer.
[T 18:07:16 65569] Initialising tags...
[T 18:07:16 65569] ID set to 0000000000010021_0000000000000001 (parent 0000000000010014_0000000000000004)
[T 18:07:16 65569] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:07:16 65569] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:07:16 65569] Executing the following tracer actions:
[T 18:07:16 65569] Tracer actions:
[T 18:07:16 65569] pre_invocations(0)
[T 18:07:16 65569] post_invocations(1)
[T 18:07:16 65569] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, code_00317ffc.c, -o, code_00317ffc]
[T 18:07:16 65569] trace_languages(1): [cpp]
[T 18:07:16 65572] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.00010023.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.00010023.slice.arm64: replacing existing signature
[T 18:07:17 65571] Initializing tracer.
[T 18:07:17 65571] Initialising tags...
[T 18:07:17 65571] ID set to 0000000000010023_0000000000000001 (parent 0000000000010021_0000000000000001)
[T 18:07:17 65571] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:17 65571] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:17 65571] Executing the following tracer actions:
[T 18:07:17 65571] Tracer actions:
[T 18:07:17 65571] pre_invocations(0)
[T 18:07:17 65571] post_invocations(1)
[T 18:07:17 65571] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, code_00317ffc.c, -o, code_00317ffc]
[T 18:07:17 65571] trace_languages(1): [cpp]
[T 18:07:17 65578] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/usr/bin/xcrun.semmle.00010029.slice.x86_64: replacing existing signature
[T 18:07:18 65577] Initializing tracer.
[T 18:07:18 65577] Initialising tags...
[T 18:07:18 65577] ID set to 0000000000010029_0000000000000001 (parent 0000000000010023_0000000000000001)
[T 18:07:18 65577] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:18 65577] Executing the following tracer actions:
[T 18:07:18 65577] Tracer actions:
[T 18:07:18 65577] pre_invocations(0)
[T 18:07:18 65577] post_invocations(0)
[T 18:07:18 65577] trace_languages(1): [cpp]
[T 18:07:18 65582] Attempting to switch stdout/stderr to 3...
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00010029.slice.x86_64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00010029.slice.x86_64: replacing existing signature
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00010029.slice.arm64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00010029.slice.arm64: replacing existing signature
[T 18:07:29 65577] Initializing tracer.
[T 18:07:29 65577] Initialising tags...
[T 18:07:29 65577] ID set to 0000000000010029_0000000000000002 (parent 0000000000010029_0000000000000001)
[T 18:07:29 65577] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:29 65577] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:29 65577] Executing the following tracer actions:
[T 18:07:29 65577] Tracer actions:
[T 18:07:29 65577] pre_invocations(0)
[T 18:07:29 65577] post_invocations(1)
[T 18:07:29 65577] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, code_00317ffc.c, -o, code_00317ffc]
[T 18:07:29 65577] trace_languages(1): [cpp]
[T 18:07:29 65590] Initializing tracer.
[T 18:07:29 65590] Initialising tags...
[T 18:07:29 65590] ID set to 0000000000010036_0000000000000001 (parent 0000000000010029_0000000000000002)
[T 18:07:29 65590] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:29 65590] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:29 65590] Executing the following tracer actions:
[T 18:07:29 65590] Tracer actions:
[T 18:07:29 65590] pre_invocations(0)
[T 18:07:29 65590] post_invocations(1)
[T 18:07:29 65590] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -dumpdir, code_00317ffc-, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, code_00317ffc.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_00317ffc-1acb30.o, -x, c, code_00317ffc.c]
[T 18:07:29 65590] trace_languages(1): [cpp]
[T 18:07:29 65593] Attempting to switch stdout/stderr to 4...
[T 18:07:29 65594] Attempting to switch stdout/stderr to 4...
[T 18:07:30 65593] Initializing tracer.
[T 18:07:30 65593] Initialising tags...
[T 18:07:30 65593] ID set to 0000000000010039_0000000000000001 (parent 0000000000010036_0000000000000001)
[E 18:07:30 65593] CodeQL C/C++ Extractor 2.11.2
[E 18:07:30 65593] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:07:30 65593] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -dumpdir code_00317ffc- -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name code_00317ffc.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon '-clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation' -fno-odr-hash-protocols '-clang-vendor-feature=+enableAggressiveVLAFolding' '-clang-vendor-feature=+revert09abecef7bbf' '-clang-vendor-feature=+thisNoAlignAttr' '-clang-vendor-feature=+thisNoNullAttr' '-clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError' -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_00317ffc-1acb30.o -x c code_00317ffc.c
[T 18:07:31 65600] Initializing tracer.
[T 18:07:31 65600] Initialising tags...
[T 18:07:31 65600] ID set to 0000000000010040_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:31 65600] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:31 65600] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:31 65600] Executing the following tracer actions:
[T 18:07:31 65600] Tracer actions:
[T 18:07:31 65600] pre_invocations(0)
[T 18:07:31 65600] post_invocations(1)
[T 18:07:31 65600] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 18:07:31 65600] trace_languages(1): [cpp]
[T 18:07:31 65602] Attempting to switch stdout/stderr to 7...
[T 18:07:31 65602] Initializing tracer.
[T 18:07:31 65602] Initialising tags...
[T 18:07:31 65602] ID set to 0000000000010042_0000000000000001 (parent 0000000000010040_0000000000000001)
[E 18:07:31 65602] Mimicry classification suppression detected; exiting.
[T 18:07:31 65600] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:31 65603] Initializing tracer.
[T 18:07:31 65603] Initialising tags...
[T 18:07:31 65603] ID set to 0000000000010043_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:31 65603] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:31 65603] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:31 65603] Executing the following tracer actions:
[T 18:07:31 65603] Tracer actions:
[T 18:07:31 65603] pre_invocations(0)
[T 18:07:31 65603] post_invocations(1)
[T 18:07:31 65603] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 18:07:31 65603] trace_languages(1): [cpp]
[T 18:07:31 65605] Attempting to switch stdout/stderr to 7...
[T 18:07:31 65605] Initializing tracer.
[T 18:07:31 65605] Initialising tags...
[T 18:07:31 65605] ID set to 0000000000010045_0000000000000001 (parent 0000000000010043_0000000000000001)
[E 18:07:31 65605] Mimicry classification suppression detected; exiting.
[T 18:07:31 65603] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:31 65607] Initializing tracer.
[T 18:07:31 65607] Initialising tags...
[T 18:07:31 65607] ID set to 0000000000010047_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:31 65607] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:31 65607] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:31 65607] Executing the following tracer actions:
[T 18:07:31 65607] Tracer actions:
[T 18:07:31 65607] pre_invocations(0)
[T 18:07:31 65607] post_invocations(1)
[T 18:07:31 65607] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -nostdsysteminc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65593_455623.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65593_455158.c]
[T 18:07:31 65607] trace_languages(1): [cpp]
[T 18:07:31 65609] Attempting to switch stdout/stderr to 7...
[T 18:07:31 65609] Initializing tracer.
[T 18:07:31 65609] Initialising tags...
[T 18:07:31 65609] ID set to 0000000000010049_0000000000000001 (parent 0000000000010047_0000000000000001)
[E 18:07:31 65609] Mimicry classification suppression detected; exiting.
[T 18:07:31 65607] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Unrecognised command line argument -dumpdir
Warning: Unrecognised command line argument -clear-ast-before-backend
Warning: Unrecognised command line argument -discard-value-names
Warning: Unrecognised command line argument -target-sdk-version=15.2
Warning: Unrecognised command line argument -tune-cpu
Warning: Unrecognised command line argument -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation
Warning: Unrecognised command line argument -clang-vendor-feature=+enableAggressiveVLAFolding
Warning: Unrecognised command line argument -clang-vendor-feature=+revert09abecef7bbf
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoAlignAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoNullAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError
[E 18:07:31 65593] Checking whether C compilation already happened.
[E 18:07:31 65593] Checking for tag c-compilation-happened
[E 18:07:31 65593] Checking CODEQL_TRACER_DB_ID 0000000000010036_0000000000000001
[E 18:07:31 65593] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65593] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65593] Unlocking DB
[E 18:07:31 65593] Unlocked DB
[E 18:07:31 65593] Looks like C compilation didn't already happen.
[E 18:07:31 65593] Checking whether C compilation has been attempted.
[E 18:07:31 65593] Checking for tag c-compilation-attempted
[E 18:07:31 65593] Checking CODEQL_TRACER_DB_ID 0000000000010036_0000000000000001
[E 18:07:31 65593] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65593] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65593] Unlocking DB
[E 18:07:31 65593] Unlocked DB
[E 18:07:31 65593] Marking C compilation as attempted.
[E 18:07:31 65593] Setting tag c-compilation-attempted
[E 18:07:31 65593] Starting from CODEQL_TRACER_DB_ID 0000000000010036_0000000000000001
[E 18:07:31 65593] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65593] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65593] Set tag for 0000000000010036_0000000000000001
[E 18:07:31 65593] Set tag for 0000000000010029_0000000000000002
[E 18:07:31 65593] Set tag for 0000000000010029_0000000000000001
[E 18:07:31 65593] Set tag for 0000000000010023_0000000000000001
[E 18:07:31 65593] Set tag for 0000000000010021_0000000000000001
[E 18:07:31 65593] Set tag for 0000000000010014_0000000000000004
[E 18:07:31 65593] Set tag for 0000000000010014_0000000000000003
[E 18:07:31 65593] Set tag for 0000000000010014_0000000000000002
[E 18:07:31 65593] Set tag for root
[E 18:07:31 65593] Unlocking DB
[E 18:07:31 65593] Unlocked DB
[E 18:07:31 65593] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:31 65593] Warning[extractor-c++]: In canonicalise_path: realpath failed
Excluded code_00317ffc- because it is an object
Excluded generic because it is an object
[T 18:07:31 65610] Initializing tracer.
[T 18:07:31 65610] Initialising tags...
[T 18:07:31 65610] ID set to 000000000001004A_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:31 65610] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:31 65610] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:31 65610] Executing the following tracer actions:
[T 18:07:31 65610] Tracer actions:
[T 18:07:31 65610] pre_invocations(0)
[T 18:07:31 65610] post_invocations(1)
[T 18:07:31 65610] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -v, -fsyntax-only, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65593_554134]
[T 18:07:31 65610] trace_languages(1): [cpp]
[T 18:07:31 65612] Attempting to switch stdout/stderr to 7...
[T 18:07:31 65612] Initializing tracer.
[T 18:07:31 65612] Initialising tags...
[T 18:07:31 65612] ID set to 000000000001004C_0000000000000001 (parent 000000000001004A_0000000000000001)
[E 18:07:31 65612] Mimicry classification suppression detected; exiting.
[T 18:07:31 65610] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:31 65613] Initializing tracer.
[T 18:07:31 65613] Initialising tags...
[T 18:07:31 65613] ID set to 000000000001004D_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:31 65613] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:31 65613] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:31 65613] Executing the following tracer actions:
[T 18:07:31 65613] Tracer actions:
[T 18:07:31 65613] pre_invocations(0)
[T 18:07:31 65613] post_invocations(1)
[T 18:07:31 65613] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, -dM, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_5_65593_640941]
[T 18:07:31 65613] trace_languages(1): [cpp]
[T 18:07:31 65615] Attempting to switch stdout/stderr to 7...
[T 18:07:31 65615] Initializing tracer.
[T 18:07:31 65615] Initialising tags...
[T 18:07:31 65615] ID set to 000000000001004F_0000000000000001 (parent 000000000001004D_0000000000000001)
[E 18:07:31 65615] Mimicry classification suppression detected; exiting.
[T 18:07:31 65613] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Apple version 160000 of clang is too new; mapping it to 14.0.0.
[T 18:07:31 65616] Initializing tracer.
[T 18:07:31 65616] Initialising tags...
[T 18:07:31 65616] ID set to 0000000000010050_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:31 65616] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:31 65616] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:31 65616] Executing the following tracer actions:
[T 18:07:31 65616] Tracer actions:
[T 18:07:31 65616] pre_invocations(0)
[T 18:07:31 65616] post_invocations(1)
[T 18:07:31 65616] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_6_65593_723405]
[T 18:07:31 65616] trace_languages(1): [cpp]
[T 18:07:31 65618] Attempting to switch stdout/stderr to 7...
[T 18:07:31 65618] Initializing tracer.
[T 18:07:31 65618] Initialising tags...
[T 18:07:31 65618] ID set to 0000000000010052_0000000000000001 (parent 0000000000010050_0000000000000001)
[E 18:07:31 65618] Mimicry classification suppression detected; exiting.
[T 18:07:31 65616] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:31 65619] Initializing tracer.
[T 18:07:31 65619] Initialising tags...
[T 18:07:31 65619] ID set to 0000000000010053_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:31 65619] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:31 65619] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:31 65619] Executing the following tracer actions:
[T 18:07:31 65619] Tracer actions:
[T 18:07:31 65619] pre_invocations(0)
[T 18:07:31 65619] post_invocations(1)
[T 18:07:31 65619] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c-header, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_7_65593_805922.h.gch, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_7_65593_805922.h]
[T 18:07:31 65619] trace_languages(1): [cpp]
[T 18:07:31 65621] Attempting to switch stdout/stderr to 7...
[T 18:07:31 65621] Initializing tracer.
[T 18:07:31 65621] Initialising tags...
[T 18:07:31 65621] ID set to 0000000000010055_0000000000000001 (parent 0000000000010053_0000000000000001)
[E 18:07:31 65621] Mimicry classification suppression detected; exiting.
[T 18:07:31 65619] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:31 65622] Initializing tracer.
[T 18:07:31 65622] Initialising tags...
[T 18:07:31 65622] ID set to 0000000000010056_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:31 65622] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:31 65622] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:31 65622] Executing the following tracer actions:
[T 18:07:31 65622] Tracer actions:
[T 18:07:31 65622] pre_invocations(0)
[T 18:07:31 65622] post_invocations(1)
[T 18:07:31 65622] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -fno-color-diagnostics, --help]
[T 18:07:31 65622] trace_languages(1): [cpp]
[T 18:07:31 65624] Attempting to switch stdout/stderr to 7...
[T 18:07:31 65624] Initializing tracer.
[T 18:07:31 65624] Initialising tags...
[T 18:07:31 65624] ID set to 0000000000010058_0000000000000001 (parent 0000000000010056_0000000000000001)
[E 18:07:31 65624] CodeQL C/C++ Extractor 2.11.2
[E 18:07:31 65624] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:07:31 65624] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -mrelax-all -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -fno-odr-hash-protocols -x c -fno-color-diagnostics --help
[E 18:07:31 65624] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:31 65624] Checking whether C compilation already happened.
[E 18:07:31 65624] Checking for tag c-compilation-happened
[E 18:07:31 65624] Checking CODEQL_TRACER_DB_ID 0000000000010056_0000000000000001
[E 18:07:31 65624] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65624] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65624] Unlocking DB
[E 18:07:31 65624] Unlocked DB
[E 18:07:31 65624] Looks like C compilation didn't already happen.
[E 18:07:31 65624] Checking whether C compilation has been attempted.
[E 18:07:31 65624] Checking for tag c-compilation-attempted
[E 18:07:31 65624] Checking CODEQL_TRACER_DB_ID 0000000000010056_0000000000000001
[E 18:07:31 65624] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65624] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65624] Unlocking DB
[E 18:07:31 65624] Unlocked DB
[E 18:07:31 65624] Marking C compilation as attempted.
[E 18:07:31 65624] Setting tag c-compilation-attempted
[E 18:07:31 65624] Starting from CODEQL_TRACER_DB_ID 0000000000010056_0000000000000001
[E 18:07:31 65624] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65624] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:31 65624] Set tag for 0000000000010056_0000000000000001
[E 18:07:31 65624] Set tag for 0000000000010039_0000000000000001
[E 18:07:31 65624] Set tag for 0000000000010036_0000000000000001
[E 18:07:31 65624] Set tag for 0000000000010029_0000000000000002
[E 18:07:31 65624] Set tag for 0000000000010029_0000000000000001
[E 18:07:31 65624] Set tag for 0000000000010023_0000000000000001
[E 18:07:31 65624] Set tag for 0000000000010021_0000000000000001
[E 18:07:31 65624] Set tag for 0000000000010014_0000000000000004
[E 18:07:31 65624] Set tag for 0000000000010014_0000000000000003
[E 18:07:31 65624] Set tag for 0000000000010014_0000000000000002
[E 18:07:31 65624] Set tag for root
[E 18:07:31 65624] Unlocking DB
[E 18:07:31 65624] Unlocked DB
[E 18:07:31 65624] 0 file groups; exiting.
[T 18:07:31 65622] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:32 65625] Initializing tracer.
[T 18:07:32 65625] Initialising tags...
[T 18:07:32 65625] ID set to 0000000000010059_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:32 65625] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:32 65625] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:32 65625] Executing the following tracer actions:
[T 18:07:32 65625] Tracer actions:
[T 18:07:32 65625] pre_invocations(0)
[T 18:07:32 65625] post_invocations(1)
[T 18:07:32 65625] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_7_65593_805922.h.gch, -ast-dump, -]
[T 18:07:32 65625] trace_languages(1): [cpp]
[T 18:07:32 65627] Attempting to switch stdout/stderr to 7...
[T 18:07:32 65627] Initializing tracer.
[T 18:07:32 65627] Initialising tags...
[T 18:07:32 65627] ID set to 000000000001005B_0000000000000001 (parent 0000000000010059_0000000000000001)
[E 18:07:32 65627] Mimicry classification suppression detected; exiting.
[T 18:07:32 65625] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, int *)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, unsigned short)'
Warning: Could not parse function type 'float (__bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'void (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(int)))) int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(float)))) float, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(short)))) short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(short)))) short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(short)))) short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__fp16 (__fp16, int)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Failed to find information about possible built-in __VA_OPT__
Warning: Failed to find information about possible built-in __blocks__
Warning: Failed to find information about possible built-in __builtin___fprintf_chk
Warning: Failed to find information about possible built-in __builtin___vfprintf_chk
Warning: Failed to find information about possible built-in __builtin_fprintf
Warning: Failed to find information about possible built-in __builtin_fscanf
Warning: Failed to find information about possible built-in __builtin_vfprintf
Warning: Failed to find information about possible built-in __builtin_vfscanf
Warning: Failed to find information about possible built-in __sigsetjmp
Warning: Failed to find information about possible built-in _longjmp
Warning: Failed to find information about possible built-in _setjmp
Warning: Ignored 11 possible built-ins
Warning: Throwing away all 2906 builtins because of 229 bad parses.
[T 18:07:32 65628] Initializing tracer.
[T 18:07:32 65628] Initialising tags...
[T 18:07:32 65628] ID set to 000000000001005C_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:32 65628] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:32 65628] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:32 65628] Executing the following tracer actions:
[T 18:07:32 65628] Tracer actions:
[T 18:07:32 65628] pre_invocations(0)
[T 18:07:32 65628] post_invocations(1)
[T 18:07:32 65628] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_9_65593_347895.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_8_65593_347605.c]
[T 18:07:32 65628] trace_languages(1): [cpp]
[T 18:07:32 65630] Attempting to switch stdout/stderr to 7...
[T 18:07:32 65630] Initializing tracer.
[T 18:07:32 65630] Initialising tags...
[T 18:07:32 65630] ID set to 000000000001005E_0000000000000001 (parent 000000000001005C_0000000000000001)
[E 18:07:32 65630] Mimicry classification suppression detected; exiting.
[T 18:07:32 65628] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:32 65631] Initializing tracer.
[T 18:07:32 65631] Initialising tags...
[T 18:07:32 65631] ID set to 000000000001005F_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:32 65631] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:32 65631] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:32 65631] Executing the following tracer actions:
[T 18:07:32 65631] Tracer actions:
[T 18:07:32 65631] pre_invocations(0)
[T 18:07:32 65631] post_invocations(1)
[T 18:07:32 65631] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_11_65593_443482.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_10_65593_442978.c]
[T 18:07:32 65631] trace_languages(1): [cpp]
[T 18:07:32 65633] Attempting to switch stdout/stderr to 7...
[T 18:07:32 65633] Initializing tracer.
[T 18:07:32 65633] Initialising tags...
[T 18:07:32 65633] ID set to 0000000000010061_0000000000000001 (parent 000000000001005F_0000000000000001)
[E 18:07:32 65633] Mimicry classification suppression detected; exiting.
[T 18:07:32 65631] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:32 65634] Initializing tracer.
[T 18:07:32 65634] Initialising tags...
[T 18:07:32 65634] ID set to 0000000000010062_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:32 65634] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:32 65634] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:32 65634] Executing the following tracer actions:
[T 18:07:32 65634] Tracer actions:
[T 18:07:32 65634] pre_invocations(0)
[T 18:07:32 65634] post_invocations(1)
[T 18:07:32 65634] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_13_65593_534819.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_12_65593_534269.c]
[T 18:07:32 65634] trace_languages(1): [cpp]
[T 18:07:32 65636] Attempting to switch stdout/stderr to 7...
[T 18:07:32 65636] Initializing tracer.
[T 18:07:32 65636] Initialising tags...
[T 18:07:32 65636] ID set to 0000000000010064_0000000000000001 (parent 0000000000010062_0000000000000001)
[E 18:07:32 65636] Mimicry classification suppression detected; exiting.
[T 18:07:32 65634] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:32 65637] Initializing tracer.
[T 18:07:32 65637] Initialising tags...
[T 18:07:32 65637] ID set to 0000000000010065_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:32 65637] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:32 65637] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:32 65637] Executing the following tracer actions:
[T 18:07:32 65637] Tracer actions:
[T 18:07:32 65637] pre_invocations(0)
[T 18:07:32 65637] post_invocations(1)
[T 18:07:32 65637] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_15_65593_635594.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_14_65593_635330.c]
[T 18:07:32 65637] trace_languages(1): [cpp]
[T 18:07:32 65639] Attempting to switch stdout/stderr to 7...
[T 18:07:32 65639] Initializing tracer.
[T 18:07:32 65639] Initialising tags...
[T 18:07:32 65639] ID set to 0000000000010067_0000000000000001 (parent 0000000000010065_0000000000000001)
[E 18:07:32 65639] Mimicry classification suppression detected; exiting.
[T 18:07:32 65637] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:32 65640] Initializing tracer.
[T 18:07:32 65640] Initialising tags...
[T 18:07:32 65640] ID set to 0000000000010068_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:32 65640] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:32 65640] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:32 65640] Executing the following tracer actions:
[T 18:07:32 65640] Tracer actions:
[T 18:07:32 65640] pre_invocations(0)
[T 18:07:32 65640] post_invocations(1)
[T 18:07:32 65640] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_17_65593_727748.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_16_65593_727479.c]
[T 18:07:32 65640] trace_languages(1): [cpp]
[T 18:07:32 65642] Attempting to switch stdout/stderr to 7...
[T 18:07:32 65642] Initializing tracer.
[T 18:07:32 65642] Initialising tags...
[T 18:07:32 65642] ID set to 000000000001006A_0000000000000001 (parent 0000000000010068_0000000000000001)
[E 18:07:32 65642] Mimicry classification suppression detected; exiting.
[T 18:07:32 65640] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:32 65643] Initializing tracer.
[T 18:07:32 65643] Initialising tags...
[T 18:07:32 65643] ID set to 000000000001006B_0000000000000001 (parent 0000000000010039_0000000000000001)
[T 18:07:32 65643] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:32 65643] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:32 65643] Executing the following tracer actions:
[T 18:07:32 65643] Tracer actions:
[T 18:07:32 65643] pre_invocations(0)
[T 18:07:32 65643] post_invocations(1)
[T 18:07:32 65643] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_19_65593_819041.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_18_65593_818551.c]
[T 18:07:32 65643] trace_languages(1): [cpp]
[T 18:07:32 65645] Attempting to switch stdout/stderr to 7...
[T 18:07:32 65645] Initializing tracer.
[T 18:07:32 65645] Initialising tags...
[T 18:07:32 65645] ID set to 000000000001006D_0000000000000001 (parent 000000000001006B_0000000000000001)
[E 18:07:32 65645] Mimicry classification suppression detected; exiting.
[T 18:07:32 65643] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 18:07:32 65593] Processed command line: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --trapfolder '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/trap/cpp' --src_archive '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src' --mimic_config '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/compiler_mimic_cache/a1592c9a78e1' --object_filename /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_00317ffc-1acb30.o -w --error_limit 1000 --disable_system_macros --variadic_macros --gcc --clang_version 140000 --gnu_version 40801 --has_feature_vector 111111111111111100000000000000000000000000000000000000000000000000011 --clang --target linux_x86_64 -D_LP64=1 -D__APPLE_CC__=6000 -D__APPLE__=1 -D__ATOMIC_ACQUIRE=2 -D__ATOMIC_ACQ_REL=4 -D__ATOMIC_CONSUME=1 -D__ATOMIC_RELAXED=0 -D__ATOMIC_RELEASE=3 -D__ATOMIC_SEQ_CST=5 -D__BIGGEST_ALIGNMENT__=16 -D__BITINT_MAXWIDTH__=8388608 -D__BLOCKS__=1 -D__BOOL_WIDTH__=8 -D__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__ -D__CHAR_BIT__=8 -D__CLANG_ATOMIC_BOOL_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR_LOCK_FREE=2 -D__CLANG_ATOMIC_INT_LOCK_FREE=2 -D__CLANG_ATOMIC_LLONG_LOCK_FREE=2 -D__CLANG_ATOMIC_LONG_LOCK_FREE=2 -D__CLANG_ATOMIC_POINTER_LOCK_FREE=2 -D__CLANG_ATOMIC_SHORT_LOCK_FREE=2 -D__CLANG_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__CONSTANT_CFSTRINGS__=1 -D__DBL_DECIMAL_DIG__=17 -D__DBL_DENORM_MIN__=4.9406564584124654e-324 -D__DBL_DIG__=15 -D__DBL_EPSILON__=2.2204460492503131e-16 -D__DBL_HAS_DENORM__=1 -D__DBL_HAS_INFINITY__=1 -D__DBL_HAS_QUIET_NAN__=1 -D__DBL_MANT_DIG__=53 -D__DBL_MAX_10_EXP__=308 -D__DBL_MAX_EXP__=1024 '-D__DBL_MAX__=1.7976931348623157e+308' '-D__DBL_MIN_10_EXP__=(-307)' '-D__DBL_MIN_EXP__=(-1021)' -D__DBL_MIN__=2.2250738585072014e-308 -D__DECIMAL_DIG__=__LDBL_DECIMAL_DIG__ -D__DYNAMIC__=1 -D__ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__=150000 -D__ENVIRONMENT_OS_VERSION_MIN_REQUIRED__=150000 -D__FINITE_MATH_ONLY__=0 -D__FLT16_DECIMAL_DIG__=5 -D__FLT16_DENORM_MIN__=5.9604644775390625e-8F16 -D__FLT16_DIG__=3 -D__FLT16_EPSILON__=9.765625e-4F16 -D__FLT16_HAS_DENORM__=1 -D__FLT16_HAS_INFINITY__=1 -D__FLT16_HAS_QUIET_NAN__=1 -D__FLT16_MANT_DIG__=11 -D__FLT16_MAX_10_EXP__=4 -D__FLT16_MAX_EXP__=16 '-D__FLT16_MAX__=6.5504e+4F16' '-D__FLT16_MIN_10_EXP__=(-4)' '-D__FLT16_MIN_EXP__=(-13)' -D__FLT16_MIN__=6.103515625e-5F16 -D__FLT_DECIMAL_DIG__=9 -D__FLT_DENORM_MIN__=1.40129846e-45F -D__FLT_DIG__=6 -D__FLT_EPSILON__=1.19209290e-7F -D__FLT_HAS_DENORM__=1 -D__FLT_HAS_INFINITY__=1 -D__FLT_HAS_QUIET_NAN__=1 -D__FLT_MANT_DIG__=24 -D__FLT_MAX_10_EXP__=38 -D__FLT_MAX_EXP__=128 '-D__FLT_MAX__=3.40282347e+38F' '-D__FLT_MIN_10_EXP__=(-37)' '-D__FLT_MIN_EXP__=(-125)' -D__FLT_MIN__=1.17549435e-38F -D__FLT_RADIX__=2 -D__FPCLASS_NEGINF=0x0004 -D__FPCLASS_NEGNORMAL=0x0008 -D__FPCLASS_NEGSUBNORMAL=0x0010 -D__FPCLASS_NEGZERO=0x0020 -D__FPCLASS_POSINF=0x0200 -D__FPCLASS_POSNORMAL=0x0100 -D__FPCLASS_POSSUBNORMAL=0x0080 -D__FPCLASS_POSZERO=0x0040 -D__FPCLASS_QNAN=0x0002 -D__FPCLASS_SNAN=0x0001 -D__FXSR__=1 -D__GCC_ASM_FLAG_OUTPUTS__=1 -D__GCC_ATOMIC_BOOL_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR_LOCK_FREE=2 -D__GCC_ATOMIC_INT_LOCK_FREE=2 -D__GCC_ATOMIC_LLONG_LOCK_FREE=2 -D__GCC_ATOMIC_LONG_LOCK_FREE=2 -D__GCC_ATOMIC_POINTER_LOCK_FREE=2 -D__GCC_ATOMIC_SHORT_LOCK_FREE=2 -D__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1 -D__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -D__GNUC__=4 -D__GXX_ABI_VERSION=1002 -D__INT16_C_SUFFIX__= '-D__INT16_FMTd__="hd"' '-D__INT16_FMTi__="hi"' -D__INT16_MAX__=32767 -D__INT16_TYPE__=short -D__INT32_C_SUFFIX__= '-D__INT32_FMTd__="d"' '-D__INT32_FMTi__="i"' -D__INT32_MAX__=2147483647 -D__INT32_TYPE__=int -D__INT64_C_SUFFIX__=LL '-D__INT64_FMTd__="lld"' '-D__INT64_FMTi__="lli"' -D__INT64_MAX__=9223372036854775807LL '-D__INT64_TYPE__=long long int' -D__INT8_C_SUFFIX__= '-D__INT8_FMTd__="hhd"' '-D__INT8_FMTi__="hhi"' -D__INT8_MAX__=127 '-D__INT8_TYPE__=signed char' -D__INTMAX_C_SUFFIX__=L '-D__INTMAX_FMTd__="ld"' '-D__INTMAX_FMTi__="li"' -D__INTMAX_MAX__=9223372036854775807L '-D__INTMAX_TYPE__=long int' -D__INTMAX_WIDTH__=64 '-D__INTPTR_FMTd__="ld"' '-D__INTPTR_FMTi__="li"' -D__INTPTR_MAX__=9223372036854775807L '-D__INTPTR_TYPE__=long int' -D__INTPTR_WIDTH__=64 '-D__INT_FAST16_FMTd__="hd"' '-D__INT_FAST16_FMTi__="hi"' -D__INT_FAST16_MAX__=32767 -D__INT_FAST16_TYPE__=short -D__INT_FAST16_WIDTH__=16 '-D__INT_FAST32_FMTd__="d"' '-D__INT_FAST32_FMTi__="i"' -D__INT_FAST32_MAX__=2147483647 -D__INT_FAST32_TYPE__=int -D__INT_FAST32_WIDTH__=32 '-D__INT_FAST64_FMTd__="lld"' '-D__INT_FAST64_FMTi__="lli"' -D__INT_FAST64_MAX__=9223372036854775807LL '-D__INT_FAST64_TYPE__=long long int' -D__INT_FAST64_WIDTH__=64 '-D__INT_FAST8_FMTd__="hhd"' '-D__INT_FAST8_FMTi__="hhi"' -D__INT_FAST8_MAX__=127 '-D__INT_FAST8_TYPE__=signed char' -D__INT_FAST8_WIDTH__=8 '-D__INT_LEAST16_FMTd__="hd"' '-D__INT_LEAST16_FMTi__="hi"' -D__INT_LEAST16_MAX__=32767 -D__INT_LEAST16_TYPE__=short -D__INT_LEAST16_WIDTH__=16 '-D__INT_LEAST32_FMTd__="d"' '-D__INT_LEAST32_FMTi__="i"' -D__INT_LEAST32_MAX__=2147483647 -D__INT_LEAST32_TYPE__=int -D__INT_LEAST32_WIDTH__=32 '-D__INT_LEAST64_FMTd__="lld"' '-D__INT_LEAST64_FMTi__="lli"' -D__INT_LEAST64_MAX__=9223372036854775807LL '-D__INT_LEAST64_TYPE__=long long int' -D__INT_LEAST64_WIDTH__=64 '-D__INT_LEAST8_FMTd__="hhd"' '-D__INT_LEAST8_FMTi__="hhi"' -D__INT_LEAST8_MAX__=127 '-D__INT_LEAST8_TYPE__=signed char' -D__INT_LEAST8_WIDTH__=8 -D__INT_MAX__=2147483647 -D__INT_WIDTH__=32 -D__LAHF_SAHF__=1 -D__LDBL_DECIMAL_DIG__=21 -D__LDBL_DENORM_MIN__=3.64519953188247460253e-4951L -D__LDBL_DIG__=18 -D__LDBL_EPSILON__=1.08420217248550443401e-19L -D__LDBL_HAS_DENORM__=1 -D__LDBL_HAS_INFINITY__=1 -D__LDBL_HAS_QUIET_NAN__=1 -D__LDBL_MANT_DIG__=64 -D__LDBL_MAX_10_EXP__=4932 -D__LDBL_MAX_EXP__=16384 '-D__LDBL_MAX__=1.18973149535723176502e+4932L' '-D__LDBL_MIN_10_EXP__=(-4931)' '-D__LDBL_MIN_EXP__=(-16381)' -D__LDBL_MIN__=3.36210314311209350626e-4932L -D__LITTLE_ENDIAN__=1 -D__LLONG_WIDTH__=64 -D__LONG_LONG_MAX__=9223372036854775807LL -D__LONG_MAX__=9223372036854775807L -D__LONG_WIDTH__=64 -D__LP64__=1 -D__MACH__=1 -D__MMX__=1 -D__NO_INLINE__=1 -D__NO_MATH_ERRNO__=1 -D__NO_MATH_INLINES=1 -D__OBJC_BOOL_IS_BOOL=0 -D__OPENCL_MEMORY_SCOPE_ALL_SVM_DEVICES=3 -D__OPENCL_MEMORY_SCOPE_DEVICE=2 -D__OPENCL_MEMORY_SCOPE_SUB_GROUP=4 -D__OPENCL_MEMORY_SCOPE_WORK_GROUP=1 -D__OPENCL_MEMORY_SCOPE_WORK_ITEM=0 -D__ORDER_BIG_ENDIAN__=4321 -D__ORDER_LITTLE_ENDIAN__=1234 -D__ORDER_PDP_ENDIAN__=3412 -D__PIC__=2 -D__POINTER_WIDTH__=64 -D__PRAGMA_REDEFINE_EXTNAME=1 '-D__PTRDIFF_FMTd__="ld"' '-D__PTRDIFF_FMTi__="li"' -D__PTRDIFF_MAX__=9223372036854775807L '-D__PTRDIFF_TYPE__=long int' -D__PTRDIFF_WIDTH__=64 -D__REGISTER_PREFIX__= -D__SCHAR_MAX__=127 -D__SEG_FS=1 -D__SEG_GS=1 -D__SHRT_MAX__=32767 -D__SHRT_WIDTH__=16 -D__SIG_ATOMIC_MAX__=2147483647 -D__SIG_ATOMIC_WIDTH__=32 -D__SIZEOF_DOUBLE__=8 -D__SIZEOF_FLOAT__=4 -D__SIZEOF_INT128__=16 -D__SIZEOF_INT__=4 -D__SIZEOF_LONG_DOUBLE__=16 -D__SIZEOF_LONG_LONG__=8 -D__SIZEOF_LONG__=8 -D__SIZEOF_POINTER__=8 -D__SIZEOF_PTRDIFF_T__=8 -D__SIZEOF_SHORT__=2 -D__SIZEOF_SIZE_T__=8 -D__SIZEOF_WCHAR_T__=4 -D__SIZEOF_WINT_T__=4 '-D__SIZE_FMTX__="lX"' '-D__SIZE_FMTo__="lo"' '-D__SIZE_FMTu__="lu"' '-D__SIZE_FMTx__="lx"' -D__SIZE_MAX__=18446744073709551615UL '-D__SIZE_TYPE__=long unsigned int' -D__SIZE_WIDTH__=64 -D__SSE2_MATH__=1 -D__SSE2__=1 -D__SSE3__=1 -D__SSE4_1__=1 -D__SSE_MATH__=1 -D__SSE__=1 -D__SSSE3__=1 -D__STDC_NO_THREADS__=1 -D__STDC_UTF_16__=1 -D__STDC_UTF_32__=1 -D__UINT16_C_SUFFIX__= '-D__UINT16_FMTX__="hX"' '-D__UINT16_FMTo__="ho"' '-D__UINT16_FMTu__="hu"' '-D__UINT16_FMTx__="hx"' -D__UINT16_MAX__=65535 '-D__UINT16_TYPE__=unsigned short' -D__UINT32_C_SUFFIX__=U '-D__UINT32_FMTX__="X"' '-D__UINT32_FMTo__="o"' '-D__UINT32_FMTu__="u"' '-D__UINT32_FMTx__="x"' -D__UINT32_MAX__=4294967295U '-D__UINT32_TYPE__=unsigned int' -D__UINT64_C_SUFFIX__=ULL '-D__UINT64_FMTX__="llX"' '-D__UINT64_FMTo__="llo"' '-D__UINT64_FMTu__="llu"' '-D__UINT64_FMTx__="llx"' -D__UINT64_MAX__=18446744073709551615ULL '-D__UINT64_TYPE__=long long unsigned int' -D__UINT8_C_SUFFIX__= '-D__UINT8_FMTX__="hhX"' '-D__UINT8_FMTo__="hho"' '-D__UINT8_FMTu__="hhu"' '-D__UINT8_FMTx__="hhx"' -D__UINT8_MAX__=255 '-D__UINT8_TYPE__=unsigned char' -D__UINTMAX_C_SUFFIX__=UL '-D__UINTMAX_FMTX__="lX"' '-D__UINTMAX_FMTo__="lo"' '-D__UINTMAX_FMTu__="lu"' '-D__UINTMAX_FMTx__="lx"' -D__UINTMAX_MAX__=18446744073709551615UL '-D__UINTMAX_TYPE__=long unsigned int' -D__UINTMAX_WIDTH__=64 '-D__UINTPTR_FMTX__="lX"' '-D__UINTPTR_FMTo__="lo"' '-D__UINTPTR_FMTu__="lu"' '-D__UINTPTR_FMTx__="lx"' -D__UINTPTR_MAX__=18446744073709551615UL '-D__UINTPTR_TYPE__=long unsigned int' -D__UINTPTR_WIDTH__=64 '-D__UINT_FAST16_FMTX__="hX"' '-D__UINT_FAST16_FMTo__="ho"' '-D__UINT_FAST16_FMTu__="hu"' '-D__UINT_FAST16_FMTx__="hx"' -D__UINT_FAST16_MAX__=65535 '-D__UINT_FAST16_TYPE__=unsigned short' '-D__UINT_FAST32_FMTX__="X"' '-D__UINT_FAST32_FMTo__="o"' '-D__UINT_FAST32_FMTu__="u"' '-D__UINT_FAST32_FMTx__="x"' -D__UINT_FAST32_MAX__=4294967295U '-D__UINT_FAST32_TYPE__=unsigned int' '-D__UINT_FAST64_FMTX__="llX"' '-D__UINT_FAST64_FMTo__="llo"' '-D__UINT_FAST64_FMTu__="llu"' '-D__UINT_FAST64_FMTx__="llx"' -D__UINT_FAST64_MAX__=18446744073709551615ULL '-D__UINT_FAST64_TYPE__=long long unsigned int' '-D__UINT_FAST8_FMTX__="hhX"' '-D__UINT_FAST8_FMTo__="hho"' '-D__UINT_FAST8_FMTu__="hhu"' '-D__UINT_FAST8_FMTx__="hhx"' -D__UINT_FAST8_MAX__=255 '-D__UINT_FAST8_TYPE__=unsigned char' '-D__UINT_LEAST16_FMTX__="hX"' '-D__UINT_LEAST16_FMTo__="ho"' '-D__UINT_LEAST16_FMTu__="hu"' '-D__UINT_LEAST16_FMTx__="hx"' -D__UINT_LEAST16_MAX__=65535 '-D__UINT_LEAST16_TYPE__=unsigned short' '-D__UINT_LEAST32_FMTX__="X"' '-D__UINT_LEAST32_FMTo__="o"' '-D__UINT_LEAST32_FMTu__="u"' '-D__UINT_LEAST32_FMTx__="x"' -D__UINT_LEAST32_MAX__=4294967295U '-D__UINT_LEAST32_TYPE__=unsigned int' '-D__UINT_LEAST64_FMTX__="llX"' '-D__UINT_LEAST64_FMTo__="llo"' '-D__UINT_LEAST64_FMTu__="llu"' '-D__UINT_LEAST64_FMTx__="llx"' -D__UINT_LEAST64_MAX__=18446744073709551615ULL '-D__UINT_LEAST64_TYPE__=long long unsigned int' '-D__UINT_LEAST8_FMTX__="hhX"' '-D__UINT_LEAST8_FMTo__="hho"' '-D__UINT_LEAST8_FMTu__="hhu"' '-D__UINT_LEAST8_FMTx__="hhx"' -D__UINT_LEAST8_MAX__=255 '-D__UINT_LEAST8_TYPE__=unsigned char' -D__USER_LABEL_PREFIX__=_ '-D__VERSION__="Apple LLVM 16.0.0 (clang-1600.0.26.6)"' -D__WCHAR_MAX__=2147483647 -D__WCHAR_TYPE__=int -D__WCHAR_WIDTH__=32 -D__WINT_MAX__=2147483647 -D__WINT_TYPE__=int -D__WINT_WIDTH__=32 -D__amd64=1 -D__amd64__=1 -D__apple_build_version__=16000026 '-D__block=__attribute__((__blocks__(byref)))' -D__clang__=1 '-D__clang_literal_encoding__="UTF-8"' -D__clang_major__=16 -D__clang_minor__=0 -D__clang_patchlevel__=0 '-D__clang_version__="16.0.0 (clang-1600.0.26.6)"' '-D__clang_wide_literal_encoding__="UTF-32"' -D__code_model_small__=1 -D__core2=1 -D__core2__=1 -D__llvm__=1 -D__nonnull=_Nonnull -D__null_unspecified=_Null_unspecified -D__nullable=_Nullable -D__pic__=2 '-D__seg_fs=__attribute__((address_space(257)))' '-D__seg_gs=__attribute__((address_space(256)))' -D__strong= -D__tune_core2__=1 -D__unsafe_unretained= '-D__weak=__attribute__((objc_gc(weak)))' -D__x86_64=1 -D__x86_64__=1 '-D__private_extern__=extern __attribute__((visibility("hidden")))' --isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include --blocks -D__GCC_HAVE_DWARF2_CFI_ASM=1 -I/usr/local/include -I/Library/Developer/CommandLineTools/usr/lib/clang/16/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -I/Library/Developer/CommandLineTools/usr/include -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks -- code_00317ffc.c
[E 18:07:32 65593] Starting compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/trap/cpp/compilations/61/44689367_0.trap.br
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_00317ffc.c
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdio.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdio.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/cdefs.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_symbol_aliasing.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_posix_availability.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/Availability.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityVersions.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternal.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternalLegacy.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_types.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_types.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_types.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_va_list.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/types.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/types.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int8_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int16_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int32_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int64_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int8_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int16_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int32_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int64_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_intptr_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uintptr_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/stdio.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_printf.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_seek_set.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_ctermid.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_off_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ssize_t.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_stdio.h
[E 18:07:32 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_common.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdlib.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdlib.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/wait.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_pid_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_id_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/signal.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/appleapiopts.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/signal.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/signal.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_mcontext.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_mcontext.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/machine/_structs.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/i386/_structs.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_attr_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigaltstack.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ucontext.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigset_t.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uid_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/resource.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/usr/lib/clang/16/include/stdint.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdint.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint8_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint16_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint32_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint64_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_intmax_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uintmax_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_timeval.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/endian.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/endian.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_endian.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_endian.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_endian.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/__endian.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/_OSByteOrder.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/i386/_OSByteOrder.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/alloca.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ct_rune_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rune_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_wchar_t.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc_type.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_ptrcheck.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_abort.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_dev_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_mode_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/string.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_string.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rsize_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_errno_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_strings.h
[E 18:07:33 65593] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_strings.h
[E 18:07:33 65593] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_string.h
[E 18:07:33 65593] Creating trap tarball /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_00317ffc.c.7157ff5f_0.trap.tar.br
[E 18:07:33 65593] Emitting trap files for code_00317ffc.c
[E 18:07:33 65593] Opening existencedb in /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/existencedb/db
[E 18:07:33 65593] Wrote 94 files to /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_00317ffc.c.7157ff5f_0.trap.tar.br
[E 18:07:33 65593] Finished compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/trap/cpp/compilations/61/44689367_0.trap.br
[E 18:07:33 65593] Marking C compilation as happened.
[E 18:07:33 65593] Setting tag c-compilation-happened
[E 18:07:33 65593] Starting from CODEQL_TRACER_DB_ID 0000000000010036_0000000000000001
[E 18:07:33 65593] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:33 65593] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:33 65593] Set tag for 0000000000010036_0000000000000001
[E 18:07:33 65593] Set tag for 0000000000010029_0000000000000002
[E 18:07:33 65593] Set tag for 0000000000010029_0000000000000001
[E 18:07:33 65593] Set tag for 0000000000010023_0000000000000001
[E 18:07:33 65593] Set tag for 0000000000010021_0000000000000001
[E 18:07:33 65593] Set tag for 0000000000010014_0000000000000004
[E 18:07:33 65593] Set tag for 0000000000010014_0000000000000003
[E 18:07:33 65593] Set tag for 0000000000010014_0000000000000002
[E 18:07:33 65593] Set tag for root
[E 18:07:33 65593] Unlocking DB
[E 18:07:33 65593] Unlocked DB
[T 18:07:33 65590] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:33 65650] Attempting to switch stdout/stderr to 3...
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/ld.semmle.00010035.slice.x86_64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/ld.semmle.00010035.slice.x86_64: replacing existing signature
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/ld.semmle.00010035.slice.arm64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/ld.semmle.00010035.slice.arm64: replacing existing signature
[T 18:07:34 65663] Initializing tracer.
[T 18:07:34 65663] Initialising tags...
[T 18:07:34 65663] ID set to 000000000001007F_0000000000000001 (parent 0000000000010029_0000000000000002)
[T 18:07:34 65663] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/ld (canonical: /Library/Developer/CommandLineTools/usr/bin/ld) ====
[T 18:07:34 65663] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/ld ===
[T 18:07:34 65663] Executing the following tracer actions:
[T 18:07:34 65663] Tracer actions:
[T 18:07:34 65663] pre_invocations(0)
[T 18:07:34 65663] post_invocations(1)
[T 18:07:34 65663] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--linker, --semmle-linker-executable, /library/developer/commandlinetools/usr/bin/ld, -demangle, -lto_library, /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib, -no_deduplicate, -dynamic, -arch, x86_64, -platform_version, macos, 15.0.0, 15.2, -syslibroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -mllvm, -enable-linkonceodr-outlining, -o, code_00317ffc, -L/usr/local/lib, /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_00317ffc-1acb30.o, -lSystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
[T 18:07:34 65663] trace_languages(1): [cpp]
[T 18:07:34 65675] Attempting to switch stdout/stderr to 4...
[T 18:07:34 65675] Initializing tracer.
[T 18:07:34 65675] Initialising tags...
[T 18:07:34 65675] ID set to 000000000001008B_0000000000000001 (parent 000000000001007F_0000000000000001)
[E 18:07:34 65675] CodeQL C/C++ Extractor 2.11.2
[E 18:07:34 65675] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:07:34 65675] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --linker --semmle-linker-executable /library/developer/commandlinetools/usr/bin/ld -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -no_deduplicate -dynamic -arch x86_64 -platform_version macos 15.0.0 15.2 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o code_00317ffc -L/usr/local/lib /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_00317ffc-1acb30.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
Warning: Unrecognised command line argument -no_deduplicate
Warning: Unrecognised command line argument -platform_version
Warning: Unrecognised command line argument -enable-linkonceodr-outlining
[E 18:07:34 65675] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:34 65675] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:34 65675] Warning[extractor-c++]: In print_object_names: Could not open object file "macos" (error 2: No such file or directory).
[E 18:07:34 65675] Warning[extractor-c++]: In print_object_names: Could not open object file "15.0.0" (error 2: No such file or directory).
[E 18:07:34 65675] Warning[extractor-c++]: In print_object_names: Could not open object file "15.2" (error 2: No such file or directory).
[E 18:07:34 65675] Warning[extractor-c++]: In canonicalise_path: realpath failed
[T 18:07:34 65663] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65676] Attempting to switch stdout/stderr to 4...
[T 18:07:34 65676] Initializing tracer.
[T 18:07:34 65676] Initialising tags...
[T 18:07:34 65676] ID set to 000000000001008C_0000000000000001 (parent 0000000000010029_0000000000000002)
[E 18:07:34 65676] CodeQL C/C++ Extractor 2.11.2
[E 18:07:34 65676] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:07:34 65676] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_00317ffc.c -o code_00317ffc
[T 18:07:34 65677] Initializing tracer.
[T 18:07:34 65677] Initialising tags...
[T 18:07:34 65677] ID set to 000000000001008D_0000000000000001 (parent 000000000001008C_0000000000000001)
[T 18:07:34 65677] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:34 65677] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:34 65677] Executing the following tracer actions:
[T 18:07:34 65677] Tracer actions:
[T 18:07:34 65677] pre_invocations(0)
[T 18:07:34 65677] post_invocations(1)
[T 18:07:34 65677] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 18:07:34 65677] trace_languages(1): [cpp]
[T 18:07:34 65679] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65679] Initializing tracer.
[T 18:07:34 65679] Initialising tags...
[T 18:07:34 65679] ID set to 000000000001008F_0000000000000001 (parent 000000000001008D_0000000000000001)
[E 18:07:34 65679] Mimicry classification suppression detected; exiting.
[T 18:07:34 65677] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65680] Initializing tracer.
[T 18:07:34 65680] Initialising tags...
[T 18:07:34 65680] ID set to 0000000000010090_0000000000000001 (parent 000000000001008C_0000000000000001)
[T 18:07:34 65680] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:34 65680] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:34 65680] Executing the following tracer actions:
[T 18:07:34 65680] Tracer actions:
[T 18:07:34 65680] pre_invocations(0)
[T 18:07:34 65680] post_invocations(1)
[T 18:07:34 65680] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 18:07:34 65680] trace_languages(1): [cpp]
[T 18:07:34 65682] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65682] Initializing tracer.
[T 18:07:34 65682] Initialising tags...
[T 18:07:34 65682] ID set to 0000000000010092_0000000000000001 (parent 0000000000010090_0000000000000001)
[E 18:07:34 65682] Mimicry classification suppression detected; exiting.
[T 18:07:34 65680] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65683] Initializing tracer.
[T 18:07:34 65683] Initialising tags...
[T 18:07:34 65683] ID set to 0000000000010093_0000000000000001 (parent 000000000001008C_0000000000000001)
[T 18:07:34 65683] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:34 65683] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:34 65683] Executing the following tracer actions:
[T 18:07:34 65683] Tracer actions:
[T 18:07:34 65683] pre_invocations(0)
[T 18:07:34 65683] post_invocations(1)
[T 18:07:34 65683] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65676_319406.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65676_318983.c]
[T 18:07:34 65683] trace_languages(1): [cpp]
[T 18:07:34 65685] Initializing tracer.
[T 18:07:34 65685] Initialising tags...
[T 18:07:34 65685] ID set to 0000000000010095_0000000000000001 (parent 0000000000010093_0000000000000001)
[T 18:07:34 65685] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:34 65685] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:34 65685] Executing the following tracer actions:
[T 18:07:34 65685] Tracer actions:
[T 18:07:34 65685] pre_invocations(0)
[T 18:07:34 65685] post_invocations(1)
[T 18:07:34 65685] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_65676_318983.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65676_319406.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65676_318983.c]
[T 18:07:34 65685] trace_languages(1): [cpp]
[T 18:07:34 65687] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65687] Initializing tracer.
[T 18:07:34 65687] Initialising tags...
[T 18:07:34 65687] ID set to 0000000000010097_0000000000000001 (parent 0000000000010095_0000000000000001)
[E 18:07:34 65687] Mimicry classification suppression detected; exiting.
[T 18:07:34 65685] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65688] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65688] Initializing tracer.
[T 18:07:34 65688] Initialising tags...
[T 18:07:34 65688] ID set to 0000000000010098_0000000000000001 (parent 0000000000010093_0000000000000001)
[E 18:07:34 65688] Mimicry classification suppression detected; exiting.
[T 18:07:34 65683] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65689] Initializing tracer.
[T 18:07:34 65689] Initialising tags...
[T 18:07:34 65689] ID set to 0000000000010099_0000000000000001 (parent 000000000001008C_0000000000000001)
[T 18:07:34 65689] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:34 65689] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:34 65689] Executing the following tracer actions:
[T 18:07:34 65689] Tracer actions:
[T 18:07:34 65689] pre_invocations(0)
[T 18:07:34 65689] post_invocations(1)
[T 18:07:34 65689] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65676_494890.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65676_494516.c]
[T 18:07:34 65689] trace_languages(1): [cpp]
[T 18:07:34 65691] Initializing tracer.
[T 18:07:34 65691] Initialising tags...
[T 18:07:34 65691] ID set to 000000000001009B_0000000000000001 (parent 0000000000010099_0000000000000001)
[T 18:07:34 65691] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:34 65691] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:34 65691] Executing the following tracer actions:
[T 18:07:34 65691] Tracer actions:
[T 18:07:34 65691] pre_invocations(0)
[T 18:07:34 65691] post_invocations(1)
[T 18:07:34 65691] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_65676_494516.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65676_494890.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65676_494516.c]
[T 18:07:34 65691] trace_languages(1): [cpp]
[T 18:07:34 65693] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65693] Initializing tracer.
[T 18:07:34 65693] Initialising tags...
[T 18:07:34 65693] ID set to 000000000001009D_0000000000000001 (parent 000000000001009B_0000000000000001)
[E 18:07:34 65693] Mimicry classification suppression detected; exiting.
[T 18:07:34 65691] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65694] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65694] Initializing tracer.
[T 18:07:34 65694] Initialising tags...
[T 18:07:34 65694] ID set to 000000000001009E_0000000000000001 (parent 0000000000010099_0000000000000001)
[E 18:07:34 65694] Mimicry classification suppression detected; exiting.
[T 18:07:34 65689] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 18:07:34 65676] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:34 65676] Checking whether C compilation already happened.
[E 18:07:34 65676] Checking for tag c-compilation-happened
[E 18:07:34 65676] Checking CODEQL_TRACER_DB_ID 0000000000010029_0000000000000002
[E 18:07:34 65676] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:34 65676] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:34 65676] Unlocking DB
[E 18:07:34 65676] Unlocked DB
[E 18:07:34 65676] Exiting as C compilation already happened.
[T 18:07:34 65577] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65695] Attempting to switch stdout/stderr to 4...
[T 18:07:34 65695] Initializing tracer.
[T 18:07:34 65695] Initialising tags...
[T 18:07:34 65695] ID set to 000000000001009F_0000000000000001 (parent 0000000000010023_0000000000000001)
[E 18:07:34 65695] CodeQL C/C++ Extractor 2.11.2
[E 18:07:34 65695] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:07:34 65695] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/gcc code_00317ffc.c -o code_00317ffc
[T 18:07:34 65696] Initializing tracer.
[T 18:07:34 65696] Initialising tags...
[T 18:07:34 65696] ID set to 00000000000100A0_0000000000000001 (parent 000000000001009F_0000000000000001)
[T 18:07:34 65696] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:34 65696] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:34 65696] Executing the following tracer actions:
[T 18:07:34 65696] Tracer actions:
[T 18:07:34 65696] pre_invocations(0)
[T 18:07:34 65696] post_invocations(1)
[T 18:07:34 65696] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 18:07:34 65696] trace_languages(1): [cpp]
[T 18:07:34 65697] Initializing tracer.
[T 18:07:34 65697] Initialising tags...
[T 18:07:34 65697] ID set to 00000000000100A1_0000000000000001 (parent 00000000000100A0_0000000000000001)
[T 18:07:34 65697] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:34 65697] Executing the following tracer actions:
[T 18:07:34 65697] Tracer actions:
[T 18:07:34 65697] pre_invocations(0)
[T 18:07:34 65697] post_invocations(0)
[T 18:07:34 65697] trace_languages(1): [cpp]
[T 18:07:34 65697] Initializing tracer.
[T 18:07:34 65697] Initialising tags...
[T 18:07:34 65697] ID set to 00000000000100A1_0000000000000002 (parent 00000000000100A1_0000000000000001)
[T 18:07:34 65697] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:34 65697] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:34 65697] Executing the following tracer actions:
[T 18:07:34 65697] Tracer actions:
[T 18:07:34 65697] pre_invocations(0)
[T 18:07:34 65697] post_invocations(1)
[T 18:07:34 65697] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 18:07:34 65697] trace_languages(1): [cpp]
[T 18:07:34 65699] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65699] Initializing tracer.
[T 18:07:34 65699] Initialising tags...
[T 18:07:34 65699] ID set to 00000000000100A3_0000000000000001 (parent 00000000000100A1_0000000000000002)
[E 18:07:34 65699] Mimicry classification suppression detected; exiting.
[T 18:07:34 65697] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65700] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65700] Initializing tracer.
[T 18:07:34 65700] Initialising tags...
[T 18:07:34 65700] ID set to 00000000000100A4_0000000000000001 (parent 00000000000100A0_0000000000000001)
[E 18:07:34 65700] Mimicry classification suppression detected; exiting.
[T 18:07:34 65696] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65701] Initializing tracer.
[T 18:07:34 65701] Initialising tags...
[T 18:07:34 65701] ID set to 00000000000100A5_0000000000000001 (parent 000000000001009F_0000000000000001)
[T 18:07:34 65701] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:34 65701] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:34 65701] Executing the following tracer actions:
[T 18:07:34 65701] Tracer actions:
[T 18:07:34 65701] pre_invocations(0)
[T 18:07:34 65701] post_invocations(1)
[T 18:07:34 65701] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 18:07:34 65701] trace_languages(1): [cpp]
[T 18:07:34 65702] Initializing tracer.
[T 18:07:34 65702] Initialising tags...
[T 18:07:34 65702] ID set to 00000000000100A6_0000000000000001 (parent 00000000000100A5_0000000000000001)
[T 18:07:34 65702] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:34 65702] Executing the following tracer actions:
[T 18:07:34 65702] Tracer actions:
[T 18:07:34 65702] pre_invocations(0)
[T 18:07:34 65702] post_invocations(0)
[T 18:07:34 65702] trace_languages(1): [cpp]
[T 18:07:34 65702] Initializing tracer.
[T 18:07:34 65702] Initialising tags...
[T 18:07:34 65702] ID set to 00000000000100A6_0000000000000002 (parent 00000000000100A6_0000000000000001)
[T 18:07:34 65702] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:34 65702] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:34 65702] Executing the following tracer actions:
[T 18:07:34 65702] Tracer actions:
[T 18:07:34 65702] pre_invocations(0)
[T 18:07:34 65702] post_invocations(1)
[T 18:07:34 65702] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 18:07:34 65702] trace_languages(1): [cpp]
[T 18:07:34 65704] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65704] Initializing tracer.
[T 18:07:34 65704] Initialising tags...
[T 18:07:34 65704] ID set to 00000000000100A8_0000000000000001 (parent 00000000000100A6_0000000000000002)
[E 18:07:34 65704] Mimicry classification suppression detected; exiting.
[T 18:07:34 65702] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:34 65705] Attempting to switch stdout/stderr to 7...
[T 18:07:34 65705] Initializing tracer.
[T 18:07:34 65705] Initialising tags...
[T 18:07:34 65705] ID set to 00000000000100A9_0000000000000001 (parent 00000000000100A5_0000000000000001)
[E 18:07:34 65705] Mimicry classification suppression detected; exiting.
[T 18:07:34 65701] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65706] Initializing tracer.
[T 18:07:35 65706] Initialising tags...
[T 18:07:35 65706] ID set to 00000000000100AA_0000000000000001 (parent 000000000001009F_0000000000000001)
[T 18:07:35 65706] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:35 65706] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:35 65706] Executing the following tracer actions:
[T 18:07:35 65706] Tracer actions:
[T 18:07:35 65706] pre_invocations(0)
[T 18:07:35 65706] post_invocations(1)
[T 18:07:35 65706] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65695_995244.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65695_994838.c]
[T 18:07:35 65706] trace_languages(1): [cpp]
[T 18:07:35 65707] Initializing tracer.
[T 18:07:35 65707] Initialising tags...
[T 18:07:35 65707] ID set to 00000000000100AB_0000000000000001 (parent 00000000000100AA_0000000000000001)
[T 18:07:35 65707] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:35 65707] Executing the following tracer actions:
[T 18:07:35 65707] Tracer actions:
[T 18:07:35 65707] pre_invocations(0)
[T 18:07:35 65707] post_invocations(0)
[T 18:07:35 65707] trace_languages(1): [cpp]
[T 18:07:35 65707] Initializing tracer.
[T 18:07:35 65707] Initialising tags...
[T 18:07:35 65707] ID set to 00000000000100AB_0000000000000002 (parent 00000000000100AB_0000000000000001)
[T 18:07:35 65707] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:35 65707] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:35 65707] Executing the following tracer actions:
[T 18:07:35 65707] Tracer actions:
[T 18:07:35 65707] pre_invocations(0)
[T 18:07:35 65707] post_invocations(1)
[T 18:07:35 65707] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65695_995244.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65695_994838.c]
[T 18:07:35 65707] trace_languages(1): [cpp]
[T 18:07:35 65709] Initializing tracer.
[T 18:07:35 65709] Initialising tags...
[T 18:07:35 65709] ID set to 00000000000100AD_0000000000000001 (parent 00000000000100AB_0000000000000002)
[T 18:07:35 65709] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:35 65709] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:35 65709] Executing the following tracer actions:
[T 18:07:35 65709] Tracer actions:
[T 18:07:35 65709] pre_invocations(0)
[T 18:07:35 65709] post_invocations(1)
[T 18:07:35 65709] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_65695_994838.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65695_995244.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65695_994838.c]
[T 18:07:35 65709] trace_languages(1): [cpp]
[T 18:07:35 65711] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65711] Initializing tracer.
[T 18:07:35 65711] Initialising tags...
[T 18:07:35 65711] ID set to 00000000000100AF_0000000000000001 (parent 00000000000100AD_0000000000000001)
[E 18:07:35 65711] Mimicry classification suppression detected; exiting.
[T 18:07:35 65709] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65712] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65712] Initializing tracer.
[T 18:07:35 65712] Initialising tags...
[T 18:07:35 65712] ID set to 00000000000100B0_0000000000000001 (parent 00000000000100AB_0000000000000002)
[E 18:07:35 65712] Mimicry classification suppression detected; exiting.
[T 18:07:35 65707] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65713] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65713] Initializing tracer.
[T 18:07:35 65713] Initialising tags...
[T 18:07:35 65713] ID set to 00000000000100B1_0000000000000001 (parent 00000000000100AA_0000000000000001)
[E 18:07:35 65713] Mimicry classification suppression detected; exiting.
[T 18:07:35 65706] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65714] Initializing tracer.
[T 18:07:35 65714] Initialising tags...
[T 18:07:35 65714] ID set to 00000000000100B2_0000000000000001 (parent 000000000001009F_0000000000000001)
[T 18:07:35 65714] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:35 65714] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:35 65714] Executing the following tracer actions:
[T 18:07:35 65714] Tracer actions:
[T 18:07:35 65714] pre_invocations(0)
[T 18:07:35 65714] post_invocations(1)
[T 18:07:35 65714] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65695_239734.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65695_239419.c]
[T 18:07:35 65714] trace_languages(1): [cpp]
[T 18:07:35 65715] Initializing tracer.
[T 18:07:35 65715] Initialising tags...
[T 18:07:35 65715] ID set to 00000000000100B3_0000000000000001 (parent 00000000000100B2_0000000000000001)
[T 18:07:35 65715] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:35 65715] Executing the following tracer actions:
[T 18:07:35 65715] Tracer actions:
[T 18:07:35 65715] pre_invocations(0)
[T 18:07:35 65715] post_invocations(0)
[T 18:07:35 65715] trace_languages(1): [cpp]
[T 18:07:35 65715] Initializing tracer.
[T 18:07:35 65715] Initialising tags...
[T 18:07:35 65715] ID set to 00000000000100B3_0000000000000002 (parent 00000000000100B3_0000000000000001)
[T 18:07:35 65715] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:35 65715] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:35 65715] Executing the following tracer actions:
[T 18:07:35 65715] Tracer actions:
[T 18:07:35 65715] pre_invocations(0)
[T 18:07:35 65715] post_invocations(1)
[T 18:07:35 65715] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65695_239734.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65695_239419.c]
[T 18:07:35 65715] trace_languages(1): [cpp]
[T 18:07:35 65717] Initializing tracer.
[T 18:07:35 65717] Initialising tags...
[T 18:07:35 65717] ID set to 00000000000100B5_0000000000000001 (parent 00000000000100B3_0000000000000002)
[T 18:07:35 65717] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:35 65717] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:35 65717] Executing the following tracer actions:
[T 18:07:35 65717] Tracer actions:
[T 18:07:35 65717] pre_invocations(0)
[T 18:07:35 65717] post_invocations(1)
[T 18:07:35 65717] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_65695_239419.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65695_239734.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65695_239419.c]
[T 18:07:35 65717] trace_languages(1): [cpp]
[T 18:07:35 65719] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65719] Initializing tracer.
[T 18:07:35 65719] Initialising tags...
[T 18:07:35 65719] ID set to 00000000000100B7_0000000000000001 (parent 00000000000100B5_0000000000000001)
[E 18:07:35 65719] Mimicry classification suppression detected; exiting.
[T 18:07:35 65717] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65720] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65720] Initializing tracer.
[T 18:07:35 65720] Initialising tags...
[T 18:07:35 65720] ID set to 00000000000100B8_0000000000000001 (parent 00000000000100B3_0000000000000002)
[E 18:07:35 65720] Mimicry classification suppression detected; exiting.
[T 18:07:35 65715] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65721] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65721] Initializing tracer.
[T 18:07:35 65721] Initialising tags...
[T 18:07:35 65721] ID set to 00000000000100B9_0000000000000001 (parent 00000000000100B2_0000000000000001)
[E 18:07:35 65721] Mimicry classification suppression detected; exiting.
[T 18:07:35 65714] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 18:07:35 65695] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:35 65695] Checking whether C compilation already happened.
[E 18:07:35 65695] Checking for tag c-compilation-happened
[E 18:07:35 65695] Checking CODEQL_TRACER_DB_ID 0000000000010023_0000000000000001
[E 18:07:35 65695] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:35 65695] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:35 65695] Unlocking DB
[E 18:07:35 65695] Unlocked DB
[E 18:07:35 65695] Exiting as C compilation already happened.
[T 18:07:35 65571] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65722] Attempting to switch stdout/stderr to 4...
[T 18:07:35 65722] Initializing tracer.
[T 18:07:35 65722] Initialising tags...
[T 18:07:35 65722] ID set to 00000000000100BA_0000000000000001 (parent 0000000000010021_0000000000000001)
[E 18:07:35 65722] CodeQL C/C++ Extractor 2.11.2
[E 18:07:35 65722] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:07:35 65722] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /usr/bin/gcc code_00317ffc.c -o code_00317ffc
[T 18:07:35 65723] Initializing tracer.
[T 18:07:35 65723] Initialising tags...
[T 18:07:35 65723] ID set to 00000000000100BB_0000000000000001 (parent 00000000000100BA_0000000000000001)
[T 18:07:35 65723] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:07:35 65723] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:07:35 65723] Executing the following tracer actions:
[T 18:07:35 65723] Tracer actions:
[T 18:07:35 65723] pre_invocations(0)
[T 18:07:35 65723] post_invocations(1)
[T 18:07:35 65723] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --version]
[T 18:07:35 65723] trace_languages(1): [cpp]
[T 18:07:35 65724] Initializing tracer.
[T 18:07:35 65724] Initialising tags...
[T 18:07:35 65724] ID set to 00000000000100BC_0000000000000001 (parent 00000000000100BB_0000000000000001)
[T 18:07:35 65724] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:35 65724] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:35 65724] Executing the following tracer actions:
[T 18:07:35 65724] Tracer actions:
[T 18:07:35 65724] pre_invocations(0)
[T 18:07:35 65724] post_invocations(1)
[T 18:07:35 65724] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 18:07:35 65724] trace_languages(1): [cpp]
[T 18:07:35 65725] Initializing tracer.
[T 18:07:35 65725] Initialising tags...
[T 18:07:35 65725] ID set to 00000000000100BD_0000000000000001 (parent 00000000000100BC_0000000000000001)
[T 18:07:35 65725] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:35 65725] Executing the following tracer actions:
[T 18:07:35 65725] Tracer actions:
[T 18:07:35 65725] pre_invocations(0)
[T 18:07:35 65725] post_invocations(0)
[T 18:07:35 65725] trace_languages(1): [cpp]
[T 18:07:35 65725] Initializing tracer.
[T 18:07:35 65725] Initialising tags...
[T 18:07:35 65725] ID set to 00000000000100BD_0000000000000002 (parent 00000000000100BD_0000000000000001)
[T 18:07:35 65725] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:35 65725] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:35 65725] Executing the following tracer actions:
[T 18:07:35 65725] Tracer actions:
[T 18:07:35 65725] pre_invocations(0)
[T 18:07:35 65725] post_invocations(1)
[T 18:07:35 65725] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 18:07:35 65725] trace_languages(1): [cpp]
[T 18:07:35 65727] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65727] Initializing tracer.
[T 18:07:35 65727] Initialising tags...
[T 18:07:35 65727] ID set to 00000000000100BF_0000000000000001 (parent 00000000000100BD_0000000000000002)
[E 18:07:35 65727] Mimicry classification suppression detected; exiting.
[T 18:07:35 65725] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65728] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65728] Initializing tracer.
[T 18:07:35 65728] Initialising tags...
[T 18:07:35 65728] ID set to 00000000000100C0_0000000000000001 (parent 00000000000100BC_0000000000000001)
[E 18:07:35 65728] Mimicry classification suppression detected; exiting.
[T 18:07:35 65724] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65729] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65729] Initializing tracer.
[T 18:07:35 65729] Initialising tags...
[T 18:07:35 65729] ID set to 00000000000100C1_0000000000000001 (parent 00000000000100BB_0000000000000001)
[E 18:07:35 65729] Mimicry classification suppression detected; exiting.
[T 18:07:35 65723] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65730] Initializing tracer.
[T 18:07:35 65730] Initialising tags...
[T 18:07:35 65730] ID set to 00000000000100C2_0000000000000001 (parent 00000000000100BA_0000000000000001)
[T 18:07:35 65730] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:07:35 65730] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:07:35 65730] Executing the following tracer actions:
[T 18:07:35 65730] Tracer actions:
[T 18:07:35 65730] pre_invocations(0)
[T 18:07:35 65730] post_invocations(1)
[T 18:07:35 65730] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --help]
[T 18:07:35 65730] trace_languages(1): [cpp]
[T 18:07:35 65731] Initializing tracer.
[T 18:07:35 65731] Initialising tags...
[T 18:07:35 65731] ID set to 00000000000100C3_0000000000000001 (parent 00000000000100C2_0000000000000001)
[T 18:07:35 65731] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:35 65731] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:35 65731] Executing the following tracer actions:
[T 18:07:35 65731] Tracer actions:
[T 18:07:35 65731] pre_invocations(0)
[T 18:07:35 65731] post_invocations(1)
[T 18:07:35 65731] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 18:07:35 65731] trace_languages(1): [cpp]
[T 18:07:35 65732] Initializing tracer.
[T 18:07:35 65732] Initialising tags...
[T 18:07:35 65732] ID set to 00000000000100C4_0000000000000001 (parent 00000000000100C3_0000000000000001)
[T 18:07:35 65732] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:35 65732] Executing the following tracer actions:
[T 18:07:35 65732] Tracer actions:
[T 18:07:35 65732] pre_invocations(0)
[T 18:07:35 65732] post_invocations(0)
[T 18:07:35 65732] trace_languages(1): [cpp]
[T 18:07:35 65732] Initializing tracer.
[T 18:07:35 65732] Initialising tags...
[T 18:07:35 65732] ID set to 00000000000100C4_0000000000000002 (parent 00000000000100C4_0000000000000001)
[T 18:07:35 65732] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:35 65732] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:35 65732] Executing the following tracer actions:
[T 18:07:35 65732] Tracer actions:
[T 18:07:35 65732] pre_invocations(0)
[T 18:07:35 65732] post_invocations(1)
[T 18:07:35 65732] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 18:07:35 65732] trace_languages(1): [cpp]
[T 18:07:35 65734] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65734] Initializing tracer.
[T 18:07:35 65734] Initialising tags...
[T 18:07:35 65734] ID set to 00000000000100C6_0000000000000001 (parent 00000000000100C4_0000000000000002)
[E 18:07:35 65734] Mimicry classification suppression detected; exiting.
[T 18:07:35 65732] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65735] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65735] Initializing tracer.
[T 18:07:35 65735] Initialising tags...
[T 18:07:35 65735] ID set to 00000000000100C7_0000000000000001 (parent 00000000000100C3_0000000000000001)
[E 18:07:35 65735] Mimicry classification suppression detected; exiting.
[T 18:07:35 65731] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65736] Attempting to switch stdout/stderr to 7...
[T 18:07:35 65736] Initializing tracer.
[T 18:07:35 65736] Initialising tags...
[T 18:07:35 65736] ID set to 00000000000100C8_0000000000000001 (parent 00000000000100C2_0000000000000001)
[E 18:07:35 65736] Mimicry classification suppression detected; exiting.
[T 18:07:35 65730] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:35 65737] Initializing tracer.
[T 18:07:35 65737] Initialising tags...
[T 18:07:35 65737] ID set to 00000000000100C9_0000000000000001 (parent 00000000000100BA_0000000000000001)
[T 18:07:35 65737] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:07:35 65737] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:07:35 65737] Executing the following tracer actions:
[T 18:07:35 65737] Tracer actions:
[T 18:07:35 65737] pre_invocations(0)
[T 18:07:35 65737] post_invocations(1)
[T 18:07:35 65737] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65722_926916.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65722_926624.c]
[T 18:07:35 65737] trace_languages(1): [cpp]
[T 18:07:35 65738] Initializing tracer.
[T 18:07:35 65738] Initialising tags...
[T 18:07:35 65738] ID set to 00000000000100CA_0000000000000001 (parent 00000000000100C9_0000000000000001)
[T 18:07:35 65738] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:35 65738] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:35 65738] Executing the following tracer actions:
[T 18:07:35 65738] Tracer actions:
[T 18:07:35 65738] pre_invocations(0)
[T 18:07:35 65738] post_invocations(1)
[T 18:07:35 65738] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65722_926916.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65722_926624.c]
[T 18:07:35 65738] trace_languages(1): [cpp]
[T 18:07:35 65739] Initializing tracer.
[T 18:07:35 65739] Initialising tags...
[T 18:07:35 65739] ID set to 00000000000100CB_0000000000000001 (parent 00000000000100CA_0000000000000001)
[T 18:07:35 65739] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:35 65739] Executing the following tracer actions:
[T 18:07:35 65739] Tracer actions:
[T 18:07:35 65739] pre_invocations(0)
[T 18:07:35 65739] post_invocations(0)
[T 18:07:35 65739] trace_languages(1): [cpp]
[T 18:07:36 65739] Initializing tracer.
[T 18:07:36 65739] Initialising tags...
[T 18:07:36 65739] ID set to 00000000000100CB_0000000000000002 (parent 00000000000100CB_0000000000000001)
[T 18:07:36 65739] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:36 65739] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:36 65739] Executing the following tracer actions:
[T 18:07:36 65739] Tracer actions:
[T 18:07:36 65739] pre_invocations(0)
[T 18:07:36 65739] post_invocations(1)
[T 18:07:36 65739] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65722_926916.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65722_926624.c]
[T 18:07:36 65739] trace_languages(1): [cpp]
[T 18:07:36 65742] Initializing tracer.
[T 18:07:36 65742] Initialising tags...
[T 18:07:36 65742] ID set to 00000000000100CE_0000000000000001 (parent 00000000000100CB_0000000000000002)
[T 18:07:36 65742] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:36 65742] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:36 65742] Executing the following tracer actions:
[T 18:07:36 65742] Tracer actions:
[T 18:07:36 65742] pre_invocations(0)
[T 18:07:36 65742] post_invocations(1)
[T 18:07:36 65742] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_65722_926624.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_2_65722_926916.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_1_65722_926624.c]
[T 18:07:36 65742] trace_languages(1): [cpp]
[T 18:07:36 65744] Attempting to switch stdout/stderr to 7...
[T 18:07:36 65744] Initializing tracer.
[T 18:07:36 65744] Initialising tags...
[T 18:07:36 65744] ID set to 00000000000100D0_0000000000000001 (parent 00000000000100CE_0000000000000001)
[E 18:07:36 65744] Mimicry classification suppression detected; exiting.
[T 18:07:36 65742] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:36 65745] Attempting to switch stdout/stderr to 7...
[T 18:07:36 65745] Initializing tracer.
[T 18:07:36 65745] Initialising tags...
[T 18:07:36 65745] ID set to 00000000000100D1_0000000000000001 (parent 00000000000100CB_0000000000000002)
[E 18:07:36 65745] Mimicry classification suppression detected; exiting.
[T 18:07:36 65739] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:36 65746] Attempting to switch stdout/stderr to 7...
[T 18:07:36 65746] Initializing tracer.
[T 18:07:36 65746] Initialising tags...
[T 18:07:36 65746] ID set to 00000000000100D2_0000000000000001 (parent 00000000000100CA_0000000000000001)
[E 18:07:36 65746] Mimicry classification suppression detected; exiting.
[T 18:07:36 65738] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:36 65747] Attempting to switch stdout/stderr to 7...
[T 18:07:36 65747] Initializing tracer.
[T 18:07:36 65747] Initialising tags...
[T 18:07:36 65747] ID set to 00000000000100D3_0000000000000001 (parent 00000000000100C9_0000000000000001)
[E 18:07:36 65747] Mimicry classification suppression detected; exiting.
[T 18:07:36 65737] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:36 65748] Initializing tracer.
[T 18:07:36 65748] Initialising tags...
[T 18:07:36 65748] ID set to 00000000000100D4_0000000000000001 (parent 00000000000100BA_0000000000000001)
[T 18:07:36 65748] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:07:36 65748] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:07:36 65748] Executing the following tracer actions:
[T 18:07:36 65748] Tracer actions:
[T 18:07:36 65748] pre_invocations(0)
[T 18:07:36 65748] post_invocations(1)
[T 18:07:36 65748] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65722_226262.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65722_226058.c]
[T 18:07:36 65748] trace_languages(1): [cpp]
[T 18:07:36 65749] Initializing tracer.
[T 18:07:36 65749] Initialising tags...
[T 18:07:36 65749] ID set to 00000000000100D5_0000000000000001 (parent 00000000000100D4_0000000000000001)
[T 18:07:36 65749] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:07:36 65749] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:07:36 65749] Executing the following tracer actions:
[T 18:07:36 65749] Tracer actions:
[T 18:07:36 65749] pre_invocations(0)
[T 18:07:36 65749] post_invocations(1)
[T 18:07:36 65749] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65722_226262.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65722_226058.c]
[T 18:07:36 65749] trace_languages(1): [cpp]
[T 18:07:36 65750] Initializing tracer.
[T 18:07:36 65750] Initialising tags...
[T 18:07:36 65750] ID set to 00000000000100D6_0000000000000001 (parent 00000000000100D5_0000000000000001)
[T 18:07:36 65750] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:07:36 65750] Executing the following tracer actions:
[T 18:07:36 65750] Tracer actions:
[T 18:07:36 65750] pre_invocations(0)
[T 18:07:36 65750] post_invocations(0)
[T 18:07:36 65750] trace_languages(1): [cpp]
[T 18:07:36 65750] Initializing tracer.
[T 18:07:36 65750] Initialising tags...
[T 18:07:36 65750] ID set to 00000000000100D6_0000000000000002 (parent 00000000000100D6_0000000000000001)
[T 18:07:36 65750] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:36 65750] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:36 65750] Executing the following tracer actions:
[T 18:07:36 65750] Tracer actions:
[T 18:07:36 65750] pre_invocations(0)
[T 18:07:36 65750] post_invocations(1)
[T 18:07:36 65750] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65722_226262.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65722_226058.c]
[T 18:07:36 65750] trace_languages(1): [cpp]
[T 18:07:36 65752] Initializing tracer.
[T 18:07:36 65752] Initialising tags...
[T 18:07:36 65752] ID set to 00000000000100D8_0000000000000001 (parent 00000000000100D6_0000000000000002)
[T 18:07:36 65752] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:07:36 65752] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:07:36 65752] Executing the following tracer actions:
[T 18:07:36 65752] Tracer actions:
[T 18:07:36 65752] pre_invocations(0)
[T 18:07:36 65752] post_invocations(1)
[T 18:07:36 65752] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_65722_226058.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_4_65722_226262.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tmp//semmle_3_65722_226058.c]
[T 18:07:36 65752] trace_languages(1): [cpp]
[T 18:07:36 65754] Attempting to switch stdout/stderr to 7...
[T 18:07:36 65754] Initializing tracer.
[T 18:07:36 65754] Initialising tags...
[T 18:07:36 65754] ID set to 00000000000100DA_0000000000000001 (parent 00000000000100D8_0000000000000001)
[E 18:07:36 65754] Mimicry classification suppression detected; exiting.
[T 18:07:36 65752] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:36 65755] Attempting to switch stdout/stderr to 7...
[T 18:07:36 65755] Initializing tracer.
[T 18:07:36 65755] Initialising tags...
[T 18:07:36 65755] ID set to 00000000000100DB_0000000000000001 (parent 00000000000100D6_0000000000000002)
[E 18:07:36 65755] Mimicry classification suppression detected; exiting.
[T 18:07:36 65750] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:36 65756] Attempting to switch stdout/stderr to 7...
[T 18:07:36 65756] Initializing tracer.
[T 18:07:36 65756] Initialising tags...
[T 18:07:36 65756] ID set to 00000000000100DC_0000000000000001 (parent 00000000000100D5_0000000000000001)
[E 18:07:36 65756] Mimicry classification suppression detected; exiting.
[T 18:07:36 65749] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:07:36 65757] Attempting to switch stdout/stderr to 7...
[T 18:07:36 65757] Initializing tracer.
[T 18:07:36 65757] Initialising tags...
[T 18:07:36 65757] ID set to 00000000000100DD_0000000000000001 (parent 00000000000100D4_0000000000000001)
[E 18:07:36 65757] Mimicry classification suppression detected; exiting.
[T 18:07:36 65748] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 18:07:36 65722] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:36 65722] Checking whether C compilation already happened.
[E 18:07:36 65722] Checking for tag c-compilation-happened
[E 18:07:36 65722] Checking CODEQL_TRACER_DB_ID 0000000000010021_0000000000000001
[E 18:07:36 65722] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:36 65722] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_00317ffc/working/tags.db
[E 18:07:36 65722] Unlocking DB
[E 18:07:36 65722] Unlocked DB
[E 18:07:36 65722] Exiting as C compilation already happened.
[T 18:07:36 65569] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.

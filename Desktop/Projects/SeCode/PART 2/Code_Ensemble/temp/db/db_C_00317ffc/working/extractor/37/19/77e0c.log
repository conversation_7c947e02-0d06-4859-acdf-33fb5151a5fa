[E 18:07:34 65675] CodeQL C/C++ Extractor 2.11.2
[E 18:07:34 65675] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:07:34 65675] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --linker --semmle-linker-executable /library/developer/commandlinetools/usr/bin/ld -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -no_deduplicate -dynamic -arch x86_64 -platform_version macos 15.0.0 15.2 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o code_00317ffc -L/usr/local/lib /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_00317ffc-1acb30.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
[E 18:07:34 65675] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:34 65675] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:07:34 65675] Warning[extractor-c++]: In print_object_names: Could not open object file "macos" (error 2: No such file or directory).
[E 18:07:34 65675] Warning[extractor-c++]: In print_object_names: Could not open object file "15.0.0" (error 2: No such file or directory).
[E 18:07:34 65675] Warning[extractor-c++]: In print_object_names: Could not open object file "15.2" (error 2: No such file or directory).
[E 18:07:34 65675] Warning[extractor-c++]: In canonicalise_path: realpath failed

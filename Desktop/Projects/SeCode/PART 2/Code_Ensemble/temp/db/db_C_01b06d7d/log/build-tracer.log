[T 15:45:36 31902] CodeQL CLI version 2.11.2
[T 15:45:36 31902] Initializing tracer.
[T 15:45:36 31902] Initialising tags...
[T 15:45:36 31902] ID set to 0000000000007C9E_0000000000000001 (parent root)
[T 15:45:36 31902] Initializing tracer.
[T 15:45:36 31902] Initialising tags...
[T 15:45:36 31902] ID set to 0000000000007C9E_0000000000000002 (parent root)
[T 15:45:36 31902] Warning: SEMMLE_EXEC and SEMMLE_EXECP not set. Falling back to path lookup on argv[0].
[T 15:45:36 31902] ==== Candidate to intercept: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx (canonical: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx) ====
[T 15:45:36 31902] Executing the following tracer actions:
[T 15:45:36 31902] Tracer actions:
[T 15:45:36 31902] pre_invocations(0)
[T 15:45:36 31902] post_invocations(0)
[T 15:45:36 31902] trace_languages(1): [cpp]
[T 15:45:36 31903] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/usr/bin/make.semmle.00007C9E.slice.x86_64: replacing existing signature
[T 15:45:37 31902] Initializing tracer.
[T 15:45:37 31902] Initialising tags...
[T 15:45:37 31902] ID set to 0000000000007C9E_0000000000000003 (parent 0000000000007C9E_0000000000000002)
[T 15:45:37 31902] ==== Candidate to intercept: /usr/bin/make (canonical: /usr/bin/make) ====
[T 15:45:37 31902] Executing the following tracer actions:
[T 15:45:37 31902] Tracer actions:
[T 15:45:37 31902] pre_invocations(0)
[T 15:45:37 31902] post_invocations(0)
[T 15:45:37 31902] trace_languages(1): [cpp]
[T 15:45:37 31907] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.00007C9E.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.00007C9E.slice.arm64: replacing existing signature
[T 15:45:37 31902] Initializing tracer.
[T 15:45:37 31902] Initialising tags...
[T 15:45:37 31902] ID set to 0000000000007C9E_0000000000000004 (parent 0000000000007C9E_0000000000000003)
[T 15:45:37 31902] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/make (canonical: /Library/Developer/CommandLineTools/usr/bin/make) ====
[T 15:45:37 31902] Executing the following tracer actions:
[T 15:45:37 31902] Tracer actions:
[T 15:45:37 31902] pre_invocations(0)
[T 15:45:37 31902] post_invocations(0)
[T 15:45:37 31902] trace_languages(1): [cpp]
[T 15:45:37 31913] Attempting to switch stdout/stderr to 4...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/usr/bin/gcc.semmle.00007C9E.slice.x86_64: replacing existing signature
[T 15:45:38 31916] Initializing tracer.
[T 15:45:38 31916] Initialising tags...
[T 15:45:38 31916] ID set to 0000000000007CAC_0000000000000001 (parent 0000000000007C9E_0000000000000004)
[T 15:45:38 31916] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 15:45:38 31916] Lua: === Intercepted call to /usr/bin/gcc ===
[T 15:45:38 31916] Executing the following tracer actions:
[T 15:45:38 31916] Tracer actions:
[T 15:45:38 31916] pre_invocations(0)
[T 15:45:38 31916] post_invocations(1)
[T 15:45:38 31916] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, code_01b06d7d.c, -o, code_01b06d7d]
[T 15:45:38 31916] trace_languages(1): [cpp]
[T 15:45:38 31921] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.00007CAE.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.00007CAE.slice.arm64: replacing existing signature
[T 15:45:38 31918] Initializing tracer.
[T 15:45:38 31918] Initialising tags...
[T 15:45:38 31918] ID set to 0000000000007CAE_0000000000000001 (parent 0000000000007CAC_0000000000000001)
[T 15:45:38 31918] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:38 31918] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:38 31918] Executing the following tracer actions:
[T 15:45:38 31918] Tracer actions:
[T 15:45:38 31918] pre_invocations(0)
[T 15:45:38 31918] post_invocations(1)
[T 15:45:38 31918] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, code_01b06d7d.c, -o, code_01b06d7d]
[T 15:45:38 31918] trace_languages(1): [cpp]
[T 15:45:38 31927] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/usr/bin/xcrun.semmle.00007CB6.slice.x86_64: replacing existing signature
[T 15:45:39 31926] Initializing tracer.
[T 15:45:39 31926] Initialising tags...
[T 15:45:39 31926] ID set to 0000000000007CB6_0000000000000001 (parent 0000000000007CAE_0000000000000001)
[T 15:45:39 31926] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:39 31926] Executing the following tracer actions:
[T 15:45:39 31926] Tracer actions:
[T 15:45:39 31926] pre_invocations(0)
[T 15:45:39 31926] post_invocations(0)
[T 15:45:39 31926] trace_languages(1): [cpp]
[T 15:45:39 31931] Attempting to switch stdout/stderr to 3...
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00007CB6.slice.x86_64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00007CB6.slice.x86_64: replacing existing signature
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00007CB6.slice.arm64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00007CB6.slice.arm64: replacing existing signature
[T 15:45:49 31926] Initializing tracer.
[T 15:45:49 31926] Initialising tags...
[T 15:45:49 31926] ID set to 0000000000007CB6_0000000000000002 (parent 0000000000007CB6_0000000000000001)
[T 15:45:49 31926] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:49 31926] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:49 31926] Executing the following tracer actions:
[T 15:45:49 31926] Tracer actions:
[T 15:45:49 31926] pre_invocations(0)
[T 15:45:49 31926] post_invocations(1)
[T 15:45:49 31926] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, code_01b06d7d.c, -o, code_01b06d7d]
[T 15:45:49 31926] trace_languages(1): [cpp]
[T 15:45:50 31939] Initializing tracer.
[T 15:45:50 31939] Initialising tags...
[T 15:45:50 31939] ID set to 0000000000007CC3_0000000000000001 (parent 0000000000007CB6_0000000000000002)
[T 15:45:50 31939] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:50 31939] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:50 31939] Executing the following tracer actions:
[T 15:45:50 31939] Tracer actions:
[T 15:45:50 31939] pre_invocations(0)
[T 15:45:50 31939] post_invocations(1)
[T 15:45:50 31939] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -dumpdir, code_01b06d7d-, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, code_01b06d7d.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_01b06d7d-14b794.o, -x, c, code_01b06d7d.c]
[T 15:45:50 31939] trace_languages(1): [cpp]
[T 15:45:50 31941] Attempting to switch stdout/stderr to 4...
[T 15:45:50 31942] Attempting to switch stdout/stderr to 4...
[T 15:45:51 31941] Initializing tracer.
[T 15:45:51 31941] Initialising tags...
[T 15:45:51 31941] ID set to 0000000000007CC5_0000000000000001 (parent 0000000000007CC3_0000000000000001)
[E 15:45:51 31941] CodeQL C/C++ Extractor 2.11.2
[E 15:45:51 31941] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 15:45:51 31941] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -dumpdir code_01b06d7d- -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name code_01b06d7d.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon '-clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation' -fno-odr-hash-protocols '-clang-vendor-feature=+enableAggressiveVLAFolding' '-clang-vendor-feature=+revert09abecef7bbf' '-clang-vendor-feature=+thisNoAlignAttr' '-clang-vendor-feature=+thisNoNullAttr' '-clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError' -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_01b06d7d-14b794.o -x c code_01b06d7d.c
[T 15:45:51 31946] Initializing tracer.
[T 15:45:51 31946] Initialising tags...
[T 15:45:51 31946] ID set to 0000000000007CCA_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:51 31946] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:51 31946] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:51 31946] Executing the following tracer actions:
[T 15:45:51 31946] Tracer actions:
[T 15:45:51 31946] pre_invocations(0)
[T 15:45:51 31946] post_invocations(1)
[T 15:45:51 31946] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 15:45:51 31946] trace_languages(1): [cpp]
[T 15:45:51 31948] Attempting to switch stdout/stderr to 7...
[T 15:45:51 31948] Initializing tracer.
[T 15:45:51 31948] Initialising tags...
[T 15:45:51 31948] ID set to 0000000000007CCC_0000000000000001 (parent 0000000000007CCA_0000000000000001)
[E 15:45:51 31948] Mimicry classification suppression detected; exiting.
[T 15:45:51 31946] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:51 31949] Initializing tracer.
[T 15:45:51 31949] Initialising tags...
[T 15:45:51 31949] ID set to 0000000000007CCD_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:51 31949] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:51 31949] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:51 31949] Executing the following tracer actions:
[T 15:45:51 31949] Tracer actions:
[T 15:45:51 31949] pre_invocations(0)
[T 15:45:51 31949] post_invocations(1)
[T 15:45:51 31949] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 15:45:51 31949] trace_languages(1): [cpp]
[T 15:45:51 31951] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31951] Initializing tracer.
[T 15:45:52 31951] Initialising tags...
[T 15:45:52 31951] ID set to 0000000000007CCF_0000000000000001 (parent 0000000000007CCD_0000000000000001)
[E 15:45:52 31951] Mimicry classification suppression detected; exiting.
[T 15:45:52 31949] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:52 31952] Initializing tracer.
[T 15:45:52 31952] Initialising tags...
[T 15:45:52 31952] ID set to 0000000000007CD0_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:52 31952] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:52 31952] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:52 31952] Executing the following tracer actions:
[T 15:45:52 31952] Tracer actions:
[T 15:45:52 31952] pre_invocations(0)
[T 15:45:52 31952] post_invocations(1)
[T 15:45:52 31952] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -nostdsysteminc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_31941_16054.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_31941_15685.c]
[T 15:45:52 31952] trace_languages(1): [cpp]
[T 15:45:52 31954] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31954] Initializing tracer.
[T 15:45:52 31954] Initialising tags...
[T 15:45:52 31954] ID set to 0000000000007CD2_0000000000000001 (parent 0000000000007CD0_0000000000000001)
[E 15:45:52 31954] Mimicry classification suppression detected; exiting.
[T 15:45:52 31952] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Unrecognised command line argument -dumpdir
Warning: Unrecognised command line argument -clear-ast-before-backend
Warning: Unrecognised command line argument -discard-value-names
Warning: Unrecognised command line argument -target-sdk-version=15.2
Warning: Unrecognised command line argument -tune-cpu
Warning: Unrecognised command line argument -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation
Warning: Unrecognised command line argument -clang-vendor-feature=+enableAggressiveVLAFolding
Warning: Unrecognised command line argument -clang-vendor-feature=+revert09abecef7bbf
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoAlignAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoNullAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError
[E 15:45:52 31941] Checking whether C compilation already happened.
[E 15:45:52 31941] Checking for tag c-compilation-happened
[E 15:45:52 31941] Checking CODEQL_TRACER_DB_ID 0000000000007CC3_0000000000000001
[E 15:45:52 31941] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31941] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31941] Unlocking DB
[E 15:45:52 31941] Unlocked DB
[E 15:45:52 31941] Looks like C compilation didn't already happen.
[E 15:45:52 31941] Checking whether C compilation has been attempted.
[E 15:45:52 31941] Checking for tag c-compilation-attempted
[E 15:45:52 31941] Checking CODEQL_TRACER_DB_ID 0000000000007CC3_0000000000000001
[E 15:45:52 31941] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31941] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31941] Unlocking DB
[E 15:45:52 31941] Unlocked DB
[E 15:45:52 31941] Marking C compilation as attempted.
[E 15:45:52 31941] Setting tag c-compilation-attempted
[E 15:45:52 31941] Starting from CODEQL_TRACER_DB_ID 0000000000007CC3_0000000000000001
[E 15:45:52 31941] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31941] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31941] Set tag for 0000000000007CC3_0000000000000001
[E 15:45:52 31941] Set tag for 0000000000007CB6_0000000000000002
[E 15:45:52 31941] Set tag for 0000000000007CB6_0000000000000001
[E 15:45:52 31941] Set tag for 0000000000007CAE_0000000000000001
[E 15:45:52 31941] Set tag for 0000000000007CAC_0000000000000001
[E 15:45:52 31941] Set tag for 0000000000007C9E_0000000000000004
[E 15:45:52 31941] Set tag for 0000000000007C9E_0000000000000003
[E 15:45:52 31941] Set tag for 0000000000007C9E_0000000000000002
[E 15:45:52 31941] Set tag for root
[E 15:45:52 31941] Unlocking DB
[E 15:45:52 31941] Unlocked DB
[E 15:45:52 31941] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 15:45:52 31941] Warning[extractor-c++]: In canonicalise_path: realpath failed
Excluded code_01b06d7d- because it is an object
Excluded generic because it is an object
[T 15:45:52 31955] Initializing tracer.
[T 15:45:52 31955] Initialising tags...
[T 15:45:52 31955] ID set to 0000000000007CD3_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:52 31955] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:52 31955] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:52 31955] Executing the following tracer actions:
[T 15:45:52 31955] Tracer actions:
[T 15:45:52 31955] pre_invocations(0)
[T 15:45:52 31955] post_invocations(1)
[T 15:45:52 31955] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -v, -fsyntax-only, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_31941_120160]
[T 15:45:52 31955] trace_languages(1): [cpp]
[T 15:45:52 31957] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31957] Initializing tracer.
[T 15:45:52 31957] Initialising tags...
[T 15:45:52 31957] ID set to 0000000000007CD5_0000000000000001 (parent 0000000000007CD3_0000000000000001)
[E 15:45:52 31957] Mimicry classification suppression detected; exiting.
[T 15:45:52 31955] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:52 31958] Initializing tracer.
[T 15:45:52 31958] Initialising tags...
[T 15:45:52 31958] ID set to 0000000000007CD6_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:52 31958] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:52 31958] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:52 31958] Executing the following tracer actions:
[T 15:45:52 31958] Tracer actions:
[T 15:45:52 31958] pre_invocations(0)
[T 15:45:52 31958] post_invocations(1)
[T 15:45:52 31958] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, -dM, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_5_31941_207374]
[T 15:45:52 31958] trace_languages(1): [cpp]
[T 15:45:52 31960] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31960] Initializing tracer.
[T 15:45:52 31960] Initialising tags...
[T 15:45:52 31960] ID set to 0000000000007CD8_0000000000000001 (parent 0000000000007CD6_0000000000000001)
[E 15:45:52 31960] Mimicry classification suppression detected; exiting.
[T 15:45:52 31958] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Apple version 160000 of clang is too new; mapping it to 14.0.0.
[T 15:45:52 31961] Initializing tracer.
[T 15:45:52 31961] Initialising tags...
[T 15:45:52 31961] ID set to 0000000000007CD9_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:52 31961] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:52 31961] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:52 31961] Executing the following tracer actions:
[T 15:45:52 31961] Tracer actions:
[T 15:45:52 31961] pre_invocations(0)
[T 15:45:52 31961] post_invocations(1)
[T 15:45:52 31961] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_6_31941_289284]
[T 15:45:52 31961] trace_languages(1): [cpp]
[T 15:45:52 31963] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31963] Initializing tracer.
[T 15:45:52 31963] Initialising tags...
[T 15:45:52 31963] ID set to 0000000000007CDB_0000000000000001 (parent 0000000000007CD9_0000000000000001)
[E 15:45:52 31963] Mimicry classification suppression detected; exiting.
[T 15:45:52 31961] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:52 31964] Initializing tracer.
[T 15:45:52 31964] Initialising tags...
[T 15:45:52 31964] ID set to 0000000000007CDC_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:52 31964] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:52 31964] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:52 31964] Executing the following tracer actions:
[T 15:45:52 31964] Tracer actions:
[T 15:45:52 31964] pre_invocations(0)
[T 15:45:52 31964] post_invocations(1)
[T 15:45:52 31964] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c-header, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_7_31941_373187.h.gch, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_7_31941_373187.h]
[T 15:45:52 31964] trace_languages(1): [cpp]
[T 15:45:52 31966] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31966] Initializing tracer.
[T 15:45:52 31966] Initialising tags...
[T 15:45:52 31966] ID set to 0000000000007CDE_0000000000000001 (parent 0000000000007CDC_0000000000000001)
[E 15:45:52 31966] Mimicry classification suppression detected; exiting.
[T 15:45:52 31964] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:52 31967] Initializing tracer.
[T 15:45:52 31967] Initialising tags...
[T 15:45:52 31967] ID set to 0000000000007CDF_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:52 31967] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:52 31967] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:52 31967] Executing the following tracer actions:
[T 15:45:52 31967] Tracer actions:
[T 15:45:52 31967] pre_invocations(0)
[T 15:45:52 31967] post_invocations(1)
[T 15:45:52 31967] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -fno-color-diagnostics, --help]
[T 15:45:52 31967] trace_languages(1): [cpp]
[T 15:45:52 31969] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31969] Initializing tracer.
[T 15:45:52 31969] Initialising tags...
[T 15:45:52 31969] ID set to 0000000000007CE1_0000000000000001 (parent 0000000000007CDF_0000000000000001)
[E 15:45:52 31969] CodeQL C/C++ Extractor 2.11.2
[E 15:45:52 31969] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 15:45:52 31969] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -mrelax-all -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -fno-odr-hash-protocols -x c -fno-color-diagnostics --help
[E 15:45:52 31969] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 15:45:52 31969] Checking whether C compilation already happened.
[E 15:45:52 31969] Checking for tag c-compilation-happened
[E 15:45:52 31969] Checking CODEQL_TRACER_DB_ID 0000000000007CDF_0000000000000001
[E 15:45:52 31969] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31969] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31969] Unlocking DB
[E 15:45:52 31969] Unlocked DB
[E 15:45:52 31969] Looks like C compilation didn't already happen.
[E 15:45:52 31969] Checking whether C compilation has been attempted.
[E 15:45:52 31969] Checking for tag c-compilation-attempted
[E 15:45:52 31969] Checking CODEQL_TRACER_DB_ID 0000000000007CDF_0000000000000001
[E 15:45:52 31969] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31969] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31969] Unlocking DB
[E 15:45:52 31969] Unlocked DB
[E 15:45:52 31969] Marking C compilation as attempted.
[E 15:45:52 31969] Setting tag c-compilation-attempted
[E 15:45:52 31969] Starting from CODEQL_TRACER_DB_ID 0000000000007CDF_0000000000000001
[E 15:45:52 31969] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31969] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:52 31969] Set tag for 0000000000007CDF_0000000000000001
[E 15:45:52 31969] Set tag for 0000000000007CC5_0000000000000001
[E 15:45:52 31969] Set tag for 0000000000007CC3_0000000000000001
[E 15:45:52 31969] Set tag for 0000000000007CB6_0000000000000002
[E 15:45:52 31969] Set tag for 0000000000007CB6_0000000000000001
[E 15:45:52 31969] Set tag for 0000000000007CAE_0000000000000001
[E 15:45:52 31969] Set tag for 0000000000007CAC_0000000000000001
[E 15:45:52 31969] Set tag for 0000000000007C9E_0000000000000004
[E 15:45:52 31969] Set tag for 0000000000007C9E_0000000000000003
[E 15:45:52 31969] Set tag for 0000000000007C9E_0000000000000002
[E 15:45:52 31969] Set tag for root
[E 15:45:52 31969] Unlocking DB
[E 15:45:52 31969] Unlocked DB
[E 15:45:52 31969] 0 file groups; exiting.
[T 15:45:52 31967] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:52 31970] Initializing tracer.
[T 15:45:52 31970] Initialising tags...
[T 15:45:52 31970] ID set to 0000000000007CE2_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:52 31970] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:52 31970] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:52 31970] Executing the following tracer actions:
[T 15:45:52 31970] Tracer actions:
[T 15:45:52 31970] pre_invocations(0)
[T 15:45:52 31970] post_invocations(1)
[T 15:45:52 31970] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_7_31941_373187.h.gch, -ast-dump, -]
[T 15:45:52 31970] trace_languages(1): [cpp]
[T 15:45:52 31972] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31972] Initializing tracer.
[T 15:45:52 31972] Initialising tags...
[T 15:45:52 31972] ID set to 0000000000007CE4_0000000000000001 (parent 0000000000007CE2_0000000000000001)
[E 15:45:52 31972] Mimicry classification suppression detected; exiting.
[T 15:45:52 31970] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, int *)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, unsigned short)'
Warning: Could not parse function type 'float (__bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'void (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(int)))) int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(float)))) float, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(short)))) short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(short)))) short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(short)))) short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__fp16 (__fp16, int)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Failed to find information about possible built-in __VA_OPT__
Warning: Failed to find information about possible built-in __blocks__
Warning: Failed to find information about possible built-in __builtin___fprintf_chk
Warning: Failed to find information about possible built-in __builtin___vfprintf_chk
Warning: Failed to find information about possible built-in __builtin_fprintf
Warning: Failed to find information about possible built-in __builtin_fscanf
Warning: Failed to find information about possible built-in __builtin_vfprintf
Warning: Failed to find information about possible built-in __builtin_vfscanf
Warning: Failed to find information about possible built-in __sigsetjmp
Warning: Failed to find information about possible built-in _longjmp
Warning: Failed to find information about possible built-in _setjmp
Warning: Ignored 11 possible built-ins
Warning: Throwing away all 2906 builtins because of 229 bad parses.
[T 15:45:52 31973] Initializing tracer.
[T 15:45:52 31973] Initialising tags...
[T 15:45:52 31973] ID set to 0000000000007CE5_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:52 31973] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:52 31973] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:52 31973] Executing the following tracer actions:
[T 15:45:52 31973] Tracer actions:
[T 15:45:52 31973] pre_invocations(0)
[T 15:45:52 31973] post_invocations(1)
[T 15:45:52 31973] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_9_31941_912299.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_8_31941_912016.c]
[T 15:45:52 31973] trace_languages(1): [cpp]
[T 15:45:52 31975] Attempting to switch stdout/stderr to 7...
[T 15:45:52 31975] Initializing tracer.
[T 15:45:52 31975] Initialising tags...
[T 15:45:52 31975] ID set to 0000000000007CE7_0000000000000001 (parent 0000000000007CE5_0000000000000001)
[E 15:45:53 31975] Mimicry classification suppression detected; exiting.
[T 15:45:53 31973] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:53 31976] Initializing tracer.
[T 15:45:53 31976] Initialising tags...
[T 15:45:53 31976] ID set to 0000000000007CE8_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:53 31976] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 31976] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 31976] Executing the following tracer actions:
[T 15:45:53 31976] Tracer actions:
[T 15:45:53 31976] pre_invocations(0)
[T 15:45:53 31976] post_invocations(1)
[T 15:45:53 31976] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_11_31941_7781.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_10_31941_7300.c]
[T 15:45:53 31976] trace_languages(1): [cpp]
[T 15:45:53 31978] Attempting to switch stdout/stderr to 7...
[T 15:45:53 31978] Initializing tracer.
[T 15:45:53 31978] Initialising tags...
[T 15:45:53 31978] ID set to 0000000000007CEA_0000000000000001 (parent 0000000000007CE8_0000000000000001)
[E 15:45:53 31978] Mimicry classification suppression detected; exiting.
[T 15:45:53 31976] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:53 31979] Initializing tracer.
[T 15:45:53 31979] Initialising tags...
[T 15:45:53 31979] ID set to 0000000000007CEB_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:53 31979] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 31979] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 31979] Executing the following tracer actions:
[T 15:45:53 31979] Tracer actions:
[T 15:45:53 31979] pre_invocations(0)
[T 15:45:53 31979] post_invocations(1)
[T 15:45:53 31979] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_13_31941_103032.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_12_31941_102744.c]
[T 15:45:53 31979] trace_languages(1): [cpp]
[T 15:45:53 31981] Attempting to switch stdout/stderr to 7...
[T 15:45:53 31981] Initializing tracer.
[T 15:45:53 31981] Initialising tags...
[T 15:45:53 31981] ID set to 0000000000007CED_0000000000000001 (parent 0000000000007CEB_0000000000000001)
[E 15:45:53 31981] Mimicry classification suppression detected; exiting.
[T 15:45:53 31979] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:53 31982] Initializing tracer.
[T 15:45:53 31982] Initialising tags...
[T 15:45:53 31982] ID set to 0000000000007CEE_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:53 31982] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 31982] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 31982] Executing the following tracer actions:
[T 15:45:53 31982] Tracer actions:
[T 15:45:53 31982] pre_invocations(0)
[T 15:45:53 31982] post_invocations(1)
[T 15:45:53 31982] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_15_31941_206729.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_14_31941_206338.c]
[T 15:45:53 31982] trace_languages(1): [cpp]
[T 15:45:53 31984] Attempting to switch stdout/stderr to 7...
[T 15:45:53 31984] Initializing tracer.
[T 15:45:53 31984] Initialising tags...
[T 15:45:53 31984] ID set to 0000000000007CF0_0000000000000001 (parent 0000000000007CEE_0000000000000001)
[E 15:45:53 31984] Mimicry classification suppression detected; exiting.
[T 15:45:53 31982] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:53 31985] Initializing tracer.
[T 15:45:53 31985] Initialising tags...
[T 15:45:53 31985] ID set to 0000000000007CF1_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:53 31985] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 31985] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 31985] Executing the following tracer actions:
[T 15:45:53 31985] Tracer actions:
[T 15:45:53 31985] pre_invocations(0)
[T 15:45:53 31985] post_invocations(1)
[T 15:45:53 31985] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_17_31941_299705.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_16_31941_299412.c]
[T 15:45:53 31985] trace_languages(1): [cpp]
[T 15:45:53 31987] Attempting to switch stdout/stderr to 7...
[T 15:45:53 31987] Initializing tracer.
[T 15:45:53 31987] Initialising tags...
[T 15:45:53 31987] ID set to 0000000000007CF3_0000000000000001 (parent 0000000000007CF1_0000000000000001)
[E 15:45:53 31987] Mimicry classification suppression detected; exiting.
[T 15:45:53 31985] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:53 31988] Initializing tracer.
[T 15:45:53 31988] Initialising tags...
[T 15:45:53 31988] ID set to 0000000000007CF4_0000000000000001 (parent 0000000000007CC5_0000000000000001)
[T 15:45:53 31988] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 31988] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 31988] Executing the following tracer actions:
[T 15:45:53 31988] Tracer actions:
[T 15:45:53 31988] pre_invocations(0)
[T 15:45:53 31988] post_invocations(1)
[T 15:45:53 31988] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_19_31941_392026.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_18_31941_391725.c]
[T 15:45:53 31988] trace_languages(1): [cpp]
[T 15:45:53 31990] Attempting to switch stdout/stderr to 7...
[T 15:45:53 31990] Initializing tracer.
[T 15:45:53 31990] Initialising tags...
[T 15:45:53 31990] ID set to 0000000000007CF6_0000000000000001 (parent 0000000000007CF4_0000000000000001)
[E 15:45:53 31990] Mimicry classification suppression detected; exiting.
[T 15:45:53 31988] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 15:45:53 31941] Processed command line: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --trapfolder '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/trap/cpp' --src_archive '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src' --mimic_config '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/compiler_mimic_cache/a1592c9a78e1' --object_filename /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_01b06d7d-14b794.o -w --error_limit 1000 --disable_system_macros --variadic_macros --gcc --clang_version 140000 --gnu_version 40801 --has_feature_vector 111111111111111100000000000000000000000000000000000000000000000000011 --clang --target linux_x86_64 -D_LP64=1 -D__APPLE_CC__=6000 -D__APPLE__=1 -D__ATOMIC_ACQUIRE=2 -D__ATOMIC_ACQ_REL=4 -D__ATOMIC_CONSUME=1 -D__ATOMIC_RELAXED=0 -D__ATOMIC_RELEASE=3 -D__ATOMIC_SEQ_CST=5 -D__BIGGEST_ALIGNMENT__=16 -D__BITINT_MAXWIDTH__=8388608 -D__BLOCKS__=1 -D__BOOL_WIDTH__=8 -D__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__ -D__CHAR_BIT__=8 -D__CLANG_ATOMIC_BOOL_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR_LOCK_FREE=2 -D__CLANG_ATOMIC_INT_LOCK_FREE=2 -D__CLANG_ATOMIC_LLONG_LOCK_FREE=2 -D__CLANG_ATOMIC_LONG_LOCK_FREE=2 -D__CLANG_ATOMIC_POINTER_LOCK_FREE=2 -D__CLANG_ATOMIC_SHORT_LOCK_FREE=2 -D__CLANG_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__CONSTANT_CFSTRINGS__=1 -D__DBL_DECIMAL_DIG__=17 -D__DBL_DENORM_MIN__=4.9406564584124654e-324 -D__DBL_DIG__=15 -D__DBL_EPSILON__=2.2204460492503131e-16 -D__DBL_HAS_DENORM__=1 -D__DBL_HAS_INFINITY__=1 -D__DBL_HAS_QUIET_NAN__=1 -D__DBL_MANT_DIG__=53 -D__DBL_MAX_10_EXP__=308 -D__DBL_MAX_EXP__=1024 '-D__DBL_MAX__=1.7976931348623157e+308' '-D__DBL_MIN_10_EXP__=(-307)' '-D__DBL_MIN_EXP__=(-1021)' -D__DBL_MIN__=2.2250738585072014e-308 -D__DECIMAL_DIG__=__LDBL_DECIMAL_DIG__ -D__DYNAMIC__=1 -D__ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__=150000 -D__ENVIRONMENT_OS_VERSION_MIN_REQUIRED__=150000 -D__FINITE_MATH_ONLY__=0 -D__FLT16_DECIMAL_DIG__=5 -D__FLT16_DENORM_MIN__=5.9604644775390625e-8F16 -D__FLT16_DIG__=3 -D__FLT16_EPSILON__=9.765625e-4F16 -D__FLT16_HAS_DENORM__=1 -D__FLT16_HAS_INFINITY__=1 -D__FLT16_HAS_QUIET_NAN__=1 -D__FLT16_MANT_DIG__=11 -D__FLT16_MAX_10_EXP__=4 -D__FLT16_MAX_EXP__=16 '-D__FLT16_MAX__=6.5504e+4F16' '-D__FLT16_MIN_10_EXP__=(-4)' '-D__FLT16_MIN_EXP__=(-13)' -D__FLT16_MIN__=6.103515625e-5F16 -D__FLT_DECIMAL_DIG__=9 -D__FLT_DENORM_MIN__=1.40129846e-45F -D__FLT_DIG__=6 -D__FLT_EPSILON__=1.19209290e-7F -D__FLT_HAS_DENORM__=1 -D__FLT_HAS_INFINITY__=1 -D__FLT_HAS_QUIET_NAN__=1 -D__FLT_MANT_DIG__=24 -D__FLT_MAX_10_EXP__=38 -D__FLT_MAX_EXP__=128 '-D__FLT_MAX__=3.40282347e+38F' '-D__FLT_MIN_10_EXP__=(-37)' '-D__FLT_MIN_EXP__=(-125)' -D__FLT_MIN__=1.17549435e-38F -D__FLT_RADIX__=2 -D__FPCLASS_NEGINF=0x0004 -D__FPCLASS_NEGNORMAL=0x0008 -D__FPCLASS_NEGSUBNORMAL=0x0010 -D__FPCLASS_NEGZERO=0x0020 -D__FPCLASS_POSINF=0x0200 -D__FPCLASS_POSNORMAL=0x0100 -D__FPCLASS_POSSUBNORMAL=0x0080 -D__FPCLASS_POSZERO=0x0040 -D__FPCLASS_QNAN=0x0002 -D__FPCLASS_SNAN=0x0001 -D__FXSR__=1 -D__GCC_ASM_FLAG_OUTPUTS__=1 -D__GCC_ATOMIC_BOOL_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR_LOCK_FREE=2 -D__GCC_ATOMIC_INT_LOCK_FREE=2 -D__GCC_ATOMIC_LLONG_LOCK_FREE=2 -D__GCC_ATOMIC_LONG_LOCK_FREE=2 -D__GCC_ATOMIC_POINTER_LOCK_FREE=2 -D__GCC_ATOMIC_SHORT_LOCK_FREE=2 -D__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1 -D__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -D__GNUC__=4 -D__GXX_ABI_VERSION=1002 -D__INT16_C_SUFFIX__= '-D__INT16_FMTd__="hd"' '-D__INT16_FMTi__="hi"' -D__INT16_MAX__=32767 -D__INT16_TYPE__=short -D__INT32_C_SUFFIX__= '-D__INT32_FMTd__="d"' '-D__INT32_FMTi__="i"' -D__INT32_MAX__=2147483647 -D__INT32_TYPE__=int -D__INT64_C_SUFFIX__=LL '-D__INT64_FMTd__="lld"' '-D__INT64_FMTi__="lli"' -D__INT64_MAX__=9223372036854775807LL '-D__INT64_TYPE__=long long int' -D__INT8_C_SUFFIX__= '-D__INT8_FMTd__="hhd"' '-D__INT8_FMTi__="hhi"' -D__INT8_MAX__=127 '-D__INT8_TYPE__=signed char' -D__INTMAX_C_SUFFIX__=L '-D__INTMAX_FMTd__="ld"' '-D__INTMAX_FMTi__="li"' -D__INTMAX_MAX__=9223372036854775807L '-D__INTMAX_TYPE__=long int' -D__INTMAX_WIDTH__=64 '-D__INTPTR_FMTd__="ld"' '-D__INTPTR_FMTi__="li"' -D__INTPTR_MAX__=9223372036854775807L '-D__INTPTR_TYPE__=long int' -D__INTPTR_WIDTH__=64 '-D__INT_FAST16_FMTd__="hd"' '-D__INT_FAST16_FMTi__="hi"' -D__INT_FAST16_MAX__=32767 -D__INT_FAST16_TYPE__=short -D__INT_FAST16_WIDTH__=16 '-D__INT_FAST32_FMTd__="d"' '-D__INT_FAST32_FMTi__="i"' -D__INT_FAST32_MAX__=2147483647 -D__INT_FAST32_TYPE__=int -D__INT_FAST32_WIDTH__=32 '-D__INT_FAST64_FMTd__="lld"' '-D__INT_FAST64_FMTi__="lli"' -D__INT_FAST64_MAX__=9223372036854775807LL '-D__INT_FAST64_TYPE__=long long int' -D__INT_FAST64_WIDTH__=64 '-D__INT_FAST8_FMTd__="hhd"' '-D__INT_FAST8_FMTi__="hhi"' -D__INT_FAST8_MAX__=127 '-D__INT_FAST8_TYPE__=signed char' -D__INT_FAST8_WIDTH__=8 '-D__INT_LEAST16_FMTd__="hd"' '-D__INT_LEAST16_FMTi__="hi"' -D__INT_LEAST16_MAX__=32767 -D__INT_LEAST16_TYPE__=short -D__INT_LEAST16_WIDTH__=16 '-D__INT_LEAST32_FMTd__="d"' '-D__INT_LEAST32_FMTi__="i"' -D__INT_LEAST32_MAX__=2147483647 -D__INT_LEAST32_TYPE__=int -D__INT_LEAST32_WIDTH__=32 '-D__INT_LEAST64_FMTd__="lld"' '-D__INT_LEAST64_FMTi__="lli"' -D__INT_LEAST64_MAX__=9223372036854775807LL '-D__INT_LEAST64_TYPE__=long long int' -D__INT_LEAST64_WIDTH__=64 '-D__INT_LEAST8_FMTd__="hhd"' '-D__INT_LEAST8_FMTi__="hhi"' -D__INT_LEAST8_MAX__=127 '-D__INT_LEAST8_TYPE__=signed char' -D__INT_LEAST8_WIDTH__=8 -D__INT_MAX__=2147483647 -D__INT_WIDTH__=32 -D__LAHF_SAHF__=1 -D__LDBL_DECIMAL_DIG__=21 -D__LDBL_DENORM_MIN__=3.64519953188247460253e-4951L -D__LDBL_DIG__=18 -D__LDBL_EPSILON__=1.08420217248550443401e-19L -D__LDBL_HAS_DENORM__=1 -D__LDBL_HAS_INFINITY__=1 -D__LDBL_HAS_QUIET_NAN__=1 -D__LDBL_MANT_DIG__=64 -D__LDBL_MAX_10_EXP__=4932 -D__LDBL_MAX_EXP__=16384 '-D__LDBL_MAX__=1.18973149535723176502e+4932L' '-D__LDBL_MIN_10_EXP__=(-4931)' '-D__LDBL_MIN_EXP__=(-16381)' -D__LDBL_MIN__=3.36210314311209350626e-4932L -D__LITTLE_ENDIAN__=1 -D__LLONG_WIDTH__=64 -D__LONG_LONG_MAX__=9223372036854775807LL -D__LONG_MAX__=9223372036854775807L -D__LONG_WIDTH__=64 -D__LP64__=1 -D__MACH__=1 -D__MMX__=1 -D__NO_INLINE__=1 -D__NO_MATH_ERRNO__=1 -D__NO_MATH_INLINES=1 -D__OBJC_BOOL_IS_BOOL=0 -D__OPENCL_MEMORY_SCOPE_ALL_SVM_DEVICES=3 -D__OPENCL_MEMORY_SCOPE_DEVICE=2 -D__OPENCL_MEMORY_SCOPE_SUB_GROUP=4 -D__OPENCL_MEMORY_SCOPE_WORK_GROUP=1 -D__OPENCL_MEMORY_SCOPE_WORK_ITEM=0 -D__ORDER_BIG_ENDIAN__=4321 -D__ORDER_LITTLE_ENDIAN__=1234 -D__ORDER_PDP_ENDIAN__=3412 -D__PIC__=2 -D__POINTER_WIDTH__=64 -D__PRAGMA_REDEFINE_EXTNAME=1 '-D__PTRDIFF_FMTd__="ld"' '-D__PTRDIFF_FMTi__="li"' -D__PTRDIFF_MAX__=9223372036854775807L '-D__PTRDIFF_TYPE__=long int' -D__PTRDIFF_WIDTH__=64 -D__REGISTER_PREFIX__= -D__SCHAR_MAX__=127 -D__SEG_FS=1 -D__SEG_GS=1 -D__SHRT_MAX__=32767 -D__SHRT_WIDTH__=16 -D__SIG_ATOMIC_MAX__=2147483647 -D__SIG_ATOMIC_WIDTH__=32 -D__SIZEOF_DOUBLE__=8 -D__SIZEOF_FLOAT__=4 -D__SIZEOF_INT128__=16 -D__SIZEOF_INT__=4 -D__SIZEOF_LONG_DOUBLE__=16 -D__SIZEOF_LONG_LONG__=8 -D__SIZEOF_LONG__=8 -D__SIZEOF_POINTER__=8 -D__SIZEOF_PTRDIFF_T__=8 -D__SIZEOF_SHORT__=2 -D__SIZEOF_SIZE_T__=8 -D__SIZEOF_WCHAR_T__=4 -D__SIZEOF_WINT_T__=4 '-D__SIZE_FMTX__="lX"' '-D__SIZE_FMTo__="lo"' '-D__SIZE_FMTu__="lu"' '-D__SIZE_FMTx__="lx"' -D__SIZE_MAX__=18446744073709551615UL '-D__SIZE_TYPE__=long unsigned int' -D__SIZE_WIDTH__=64 -D__SSE2_MATH__=1 -D__SSE2__=1 -D__SSE3__=1 -D__SSE4_1__=1 -D__SSE_MATH__=1 -D__SSE__=1 -D__SSSE3__=1 -D__STDC_NO_THREADS__=1 -D__STDC_UTF_16__=1 -D__STDC_UTF_32__=1 -D__UINT16_C_SUFFIX__= '-D__UINT16_FMTX__="hX"' '-D__UINT16_FMTo__="ho"' '-D__UINT16_FMTu__="hu"' '-D__UINT16_FMTx__="hx"' -D__UINT16_MAX__=65535 '-D__UINT16_TYPE__=unsigned short' -D__UINT32_C_SUFFIX__=U '-D__UINT32_FMTX__="X"' '-D__UINT32_FMTo__="o"' '-D__UINT32_FMTu__="u"' '-D__UINT32_FMTx__="x"' -D__UINT32_MAX__=4294967295U '-D__UINT32_TYPE__=unsigned int' -D__UINT64_C_SUFFIX__=ULL '-D__UINT64_FMTX__="llX"' '-D__UINT64_FMTo__="llo"' '-D__UINT64_FMTu__="llu"' '-D__UINT64_FMTx__="llx"' -D__UINT64_MAX__=18446744073709551615ULL '-D__UINT64_TYPE__=long long unsigned int' -D__UINT8_C_SUFFIX__= '-D__UINT8_FMTX__="hhX"' '-D__UINT8_FMTo__="hho"' '-D__UINT8_FMTu__="hhu"' '-D__UINT8_FMTx__="hhx"' -D__UINT8_MAX__=255 '-D__UINT8_TYPE__=unsigned char' -D__UINTMAX_C_SUFFIX__=UL '-D__UINTMAX_FMTX__="lX"' '-D__UINTMAX_FMTo__="lo"' '-D__UINTMAX_FMTu__="lu"' '-D__UINTMAX_FMTx__="lx"' -D__UINTMAX_MAX__=18446744073709551615UL '-D__UINTMAX_TYPE__=long unsigned int' -D__UINTMAX_WIDTH__=64 '-D__UINTPTR_FMTX__="lX"' '-D__UINTPTR_FMTo__="lo"' '-D__UINTPTR_FMTu__="lu"' '-D__UINTPTR_FMTx__="lx"' -D__UINTPTR_MAX__=18446744073709551615UL '-D__UINTPTR_TYPE__=long unsigned int' -D__UINTPTR_WIDTH__=64 '-D__UINT_FAST16_FMTX__="hX"' '-D__UINT_FAST16_FMTo__="ho"' '-D__UINT_FAST16_FMTu__="hu"' '-D__UINT_FAST16_FMTx__="hx"' -D__UINT_FAST16_MAX__=65535 '-D__UINT_FAST16_TYPE__=unsigned short' '-D__UINT_FAST32_FMTX__="X"' '-D__UINT_FAST32_FMTo__="o"' '-D__UINT_FAST32_FMTu__="u"' '-D__UINT_FAST32_FMTx__="x"' -D__UINT_FAST32_MAX__=4294967295U '-D__UINT_FAST32_TYPE__=unsigned int' '-D__UINT_FAST64_FMTX__="llX"' '-D__UINT_FAST64_FMTo__="llo"' '-D__UINT_FAST64_FMTu__="llu"' '-D__UINT_FAST64_FMTx__="llx"' -D__UINT_FAST64_MAX__=18446744073709551615ULL '-D__UINT_FAST64_TYPE__=long long unsigned int' '-D__UINT_FAST8_FMTX__="hhX"' '-D__UINT_FAST8_FMTo__="hho"' '-D__UINT_FAST8_FMTu__="hhu"' '-D__UINT_FAST8_FMTx__="hhx"' -D__UINT_FAST8_MAX__=255 '-D__UINT_FAST8_TYPE__=unsigned char' '-D__UINT_LEAST16_FMTX__="hX"' '-D__UINT_LEAST16_FMTo__="ho"' '-D__UINT_LEAST16_FMTu__="hu"' '-D__UINT_LEAST16_FMTx__="hx"' -D__UINT_LEAST16_MAX__=65535 '-D__UINT_LEAST16_TYPE__=unsigned short' '-D__UINT_LEAST32_FMTX__="X"' '-D__UINT_LEAST32_FMTo__="o"' '-D__UINT_LEAST32_FMTu__="u"' '-D__UINT_LEAST32_FMTx__="x"' -D__UINT_LEAST32_MAX__=4294967295U '-D__UINT_LEAST32_TYPE__=unsigned int' '-D__UINT_LEAST64_FMTX__="llX"' '-D__UINT_LEAST64_FMTo__="llo"' '-D__UINT_LEAST64_FMTu__="llu"' '-D__UINT_LEAST64_FMTx__="llx"' -D__UINT_LEAST64_MAX__=18446744073709551615ULL '-D__UINT_LEAST64_TYPE__=long long unsigned int' '-D__UINT_LEAST8_FMTX__="hhX"' '-D__UINT_LEAST8_FMTo__="hho"' '-D__UINT_LEAST8_FMTu__="hhu"' '-D__UINT_LEAST8_FMTx__="hhx"' -D__UINT_LEAST8_MAX__=255 '-D__UINT_LEAST8_TYPE__=unsigned char' -D__USER_LABEL_PREFIX__=_ '-D__VERSION__="Apple LLVM 16.0.0 (clang-1600.0.26.6)"' -D__WCHAR_MAX__=2147483647 -D__WCHAR_TYPE__=int -D__WCHAR_WIDTH__=32 -D__WINT_MAX__=2147483647 -D__WINT_TYPE__=int -D__WINT_WIDTH__=32 -D__amd64=1 -D__amd64__=1 -D__apple_build_version__=16000026 '-D__block=__attribute__((__blocks__(byref)))' -D__clang__=1 '-D__clang_literal_encoding__="UTF-8"' -D__clang_major__=16 -D__clang_minor__=0 -D__clang_patchlevel__=0 '-D__clang_version__="16.0.0 (clang-1600.0.26.6)"' '-D__clang_wide_literal_encoding__="UTF-32"' -D__code_model_small__=1 -D__core2=1 -D__core2__=1 -D__llvm__=1 -D__nonnull=_Nonnull -D__null_unspecified=_Null_unspecified -D__nullable=_Nullable -D__pic__=2 '-D__seg_fs=__attribute__((address_space(257)))' '-D__seg_gs=__attribute__((address_space(256)))' -D__strong= -D__tune_core2__=1 -D__unsafe_unretained= '-D__weak=__attribute__((objc_gc(weak)))' -D__x86_64=1 -D__x86_64__=1 '-D__private_extern__=extern __attribute__((visibility("hidden")))' --isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include --blocks -D__GCC_HAVE_DWARF2_CFI_ASM=1 -I/usr/local/include -I/Library/Developer/CommandLineTools/usr/lib/clang/16/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -I/Library/Developer/CommandLineTools/usr/include -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks -- code_01b06d7d.c
[E 15:45:53 31941] Starting compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/trap/cpp/compilations/8/23477607_0.trap.br
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_01b06d7d.c
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdio.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdio.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/cdefs.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_symbol_aliasing.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_posix_availability.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/Availability.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityVersions.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternal.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternalLegacy.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_types.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_types.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_types.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_va_list.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/types.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/types.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int8_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int16_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int32_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int64_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int8_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int16_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int32_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int64_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_intptr_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uintptr_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/stdio.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_printf.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_seek_set.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_ctermid.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_off_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ssize_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_stdio.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_common.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdlib.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdlib.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/wait.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_pid_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_id_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/signal.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/appleapiopts.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/signal.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/signal.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_mcontext.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_mcontext.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/machine/_structs.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/i386/_structs.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_attr_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigaltstack.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ucontext.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigset_t.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uid_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/resource.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/usr/lib/clang/16/include/stdint.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdint.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint8_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint16_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint32_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint64_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_intmax_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uintmax_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_timeval.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/endian.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/endian.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_endian.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_endian.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_endian.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/__endian.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/_OSByteOrder.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/i386/_OSByteOrder.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/alloca.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ct_rune_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rune_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_wchar_t.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc_type.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_ptrcheck.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_abort.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_dev_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_mode_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/string.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_string.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rsize_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_errno_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_strings.h
[E 15:45:53 31941] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_strings.h
[E 15:45:53 31941] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_string.h
At end of source: error: expected a "}"
"code_01b06d7d.c", line 14: note: to match this "{"
      if (len >= maxLen) {
                         ^

[E 15:45:53 31941] Warning[extractor-c++]: In construct_message: At end of source: error: expected a "}"
"code_01b06d7d.c", line 14: note: to match this "{"
      if (len >= maxLen) {
                         ^


At end of source: error: expected a "}"
"code_01b06d7d.c", line 5: note: to match this "{"
  char* replaceSpecialChars(char* str) {
                                       ^

[E 15:45:53 31941] Warning[extractor-c++]: In construct_message: At end of source: error: expected a "}"
"code_01b06d7d.c", line 5: note: to match this "{"
  char* replaceSpecialChars(char* str) {
                                       ^


[E 15:45:53 31941] Creating trap tarball /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_01b06d7d.c.59b976e1_0.trap.tar.br
[E 15:45:53 31941] Emitting trap files for code_01b06d7d.c
[E 15:45:53 31941] Opening existencedb in /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/existencedb/db
[E 15:45:53 31941] Wrote 94 files to /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_01b06d7d.c.59b976e1_0.trap.tar.br
2 errors detected in the compilation of "code_01b06d7d.c".
[E 15:45:53 31941] Finished compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/trap/cpp/compilations/8/23477607_0.trap.br
[E 15:45:53 31941] Marking C compilation as happened.
[E 15:45:53 31941] Setting tag c-compilation-happened
[E 15:45:53 31941] Starting from CODEQL_TRACER_DB_ID 0000000000007CC3_0000000000000001
[E 15:45:53 31941] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:53 31941] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:53 31941] Set tag for 0000000000007CC3_0000000000000001
[E 15:45:53 31941] Set tag for 0000000000007CB6_0000000000000002
[E 15:45:53 31941] Set tag for 0000000000007CB6_0000000000000001
[E 15:45:53 31941] Set tag for 0000000000007CAE_0000000000000001
[E 15:45:53 31941] Set tag for 0000000000007CAC_0000000000000001
[E 15:45:53 31941] Set tag for 0000000000007C9E_0000000000000004
[E 15:45:53 31941] Set tag for 0000000000007C9E_0000000000000003
[E 15:45:53 31941] Set tag for 0000000000007C9E_0000000000000002
[E 15:45:53 31941] Set tag for root
[E 15:45:53 31941] Unlocking DB
[E 15:45:53 31941] Unlocked DB
[T 15:45:53 31939] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 1.
[T 15:45:53 31993] Attempting to switch stdout/stderr to 4...
[T 15:45:53 31993] Initializing tracer.
[T 15:45:53 31993] Initialising tags...
[T 15:45:53 31993] ID set to 0000000000007CF9_0000000000000001 (parent 0000000000007CB6_0000000000000002)
[E 15:45:53 31993] CodeQL C/C++ Extractor 2.11.2
[E 15:45:53 31993] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 15:45:53 31993] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_01b06d7d.c -o code_01b06d7d
[T 15:45:53 31994] Initializing tracer.
[T 15:45:53 31994] Initialising tags...
[T 15:45:53 31994] ID set to 0000000000007CFA_0000000000000001 (parent 0000000000007CF9_0000000000000001)
[T 15:45:53 31994] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 31994] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 31994] Executing the following tracer actions:
[T 15:45:53 31994] Tracer actions:
[T 15:45:53 31994] pre_invocations(0)
[T 15:45:53 31994] post_invocations(1)
[T 15:45:53 31994] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 15:45:53 31994] trace_languages(1): [cpp]
[T 15:45:53 31996] Attempting to switch stdout/stderr to 7...
[T 15:45:53 31996] Initializing tracer.
[T 15:45:53 31996] Initialising tags...
[T 15:45:53 31996] ID set to 0000000000007CFC_0000000000000001 (parent 0000000000007CFA_0000000000000001)
[E 15:45:53 31996] Mimicry classification suppression detected; exiting.
[T 15:45:53 31994] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:53 31997] Initializing tracer.
[T 15:45:53 31997] Initialising tags...
[T 15:45:53 31997] ID set to 0000000000007CFD_0000000000000001 (parent 0000000000007CF9_0000000000000001)
[T 15:45:53 31997] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 31997] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 31997] Executing the following tracer actions:
[T 15:45:53 31997] Tracer actions:
[T 15:45:53 31997] pre_invocations(0)
[T 15:45:53 31997] post_invocations(1)
[T 15:45:53 31997] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 15:45:53 31997] trace_languages(1): [cpp]
[T 15:45:53 31999] Attempting to switch stdout/stderr to 7...
[T 15:45:53 31999] Initializing tracer.
[T 15:45:53 31999] Initialising tags...
[T 15:45:53 31999] ID set to 0000000000007CFF_0000000000000001 (parent 0000000000007CFD_0000000000000001)
[E 15:45:53 31999] Mimicry classification suppression detected; exiting.
[T 15:45:53 31997] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:53 32000] Initializing tracer.
[T 15:45:53 32000] Initialising tags...
[T 15:45:53 32000] ID set to 0000000000007D00_0000000000000001 (parent 0000000000007CF9_0000000000000001)
[T 15:45:53 32000] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 32000] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 32000] Executing the following tracer actions:
[T 15:45:53 32000] Tracer actions:
[T 15:45:53 32000] pre_invocations(0)
[T 15:45:53 32000] post_invocations(1)
[T 15:45:53 32000] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_31993_904368.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_31993_904150.c]
[T 15:45:53 32000] trace_languages(1): [cpp]
[T 15:45:53 32002] Initializing tracer.
[T 15:45:53 32002] Initialising tags...
[T 15:45:53 32002] ID set to 0000000000007D02_0000000000000001 (parent 0000000000007D00_0000000000000001)
[T 15:45:53 32002] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:53 32002] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:53 32002] Executing the following tracer actions:
[T 15:45:53 32002] Tracer actions:
[T 15:45:53 32002] pre_invocations(0)
[T 15:45:53 32002] post_invocations(1)
[T 15:45:53 32002] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_31993_904150.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_31993_904368.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_31993_904150.c]
[T 15:45:53 32002] trace_languages(1): [cpp]
[T 15:45:54 32004] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32004] Initializing tracer.
[T 15:45:54 32004] Initialising tags...
[T 15:45:54 32004] ID set to 0000000000007D04_0000000000000001 (parent 0000000000007D02_0000000000000001)
[E 15:45:54 32004] Mimicry classification suppression detected; exiting.
[T 15:45:54 32002] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32005] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32005] Initializing tracer.
[T 15:45:54 32005] Initialising tags...
[T 15:45:54 32005] ID set to 0000000000007D05_0000000000000001 (parent 0000000000007D00_0000000000000001)
[E 15:45:54 32005] Mimicry classification suppression detected; exiting.
[T 15:45:54 32000] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32008] Initializing tracer.
[T 15:45:54 32008] Initialising tags...
[T 15:45:54 32008] ID set to 0000000000007D08_0000000000000001 (parent 0000000000007CF9_0000000000000001)
[T 15:45:54 32008] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:54 32008] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:54 32008] Executing the following tracer actions:
[T 15:45:54 32008] Tracer actions:
[T 15:45:54 32008] pre_invocations(0)
[T 15:45:54 32008] post_invocations(1)
[T 15:45:54 32008] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_31993_84646.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_31993_84515.c]
[T 15:45:54 32008] trace_languages(1): [cpp]
[T 15:45:54 32010] Initializing tracer.
[T 15:45:54 32010] Initialising tags...
[T 15:45:54 32010] ID set to 0000000000007D0A_0000000000000001 (parent 0000000000007D08_0000000000000001)
[T 15:45:54 32010] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:54 32010] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:54 32010] Executing the following tracer actions:
[T 15:45:54 32010] Tracer actions:
[T 15:45:54 32010] pre_invocations(0)
[T 15:45:54 32010] post_invocations(1)
[T 15:45:54 32010] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_31993_84515.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_31993_84646.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_31993_84515.c]
[T 15:45:54 32010] trace_languages(1): [cpp]
[T 15:45:54 32012] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32012] Initializing tracer.
[T 15:45:54 32012] Initialising tags...
[T 15:45:54 32012] ID set to 0000000000007D0C_0000000000000001 (parent 0000000000007D0A_0000000000000001)
[E 15:45:54 32012] Mimicry classification suppression detected; exiting.
[T 15:45:54 32010] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32013] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32013] Initializing tracer.
[T 15:45:54 32013] Initialising tags...
[T 15:45:54 32013] ID set to 0000000000007D0D_0000000000000001 (parent 0000000000007D08_0000000000000001)
[E 15:45:54 32013] Mimicry classification suppression detected; exiting.
[T 15:45:54 32008] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 15:45:54 31993] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 15:45:54 31993] Checking whether C compilation already happened.
[E 15:45:54 31993] Checking for tag c-compilation-happened
[E 15:45:54 31993] Checking CODEQL_TRACER_DB_ID 0000000000007CB6_0000000000000002
[E 15:45:54 31993] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:54 31993] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:54 31993] Unlocking DB
[E 15:45:54 31993] Unlocked DB
[E 15:45:54 31993] Exiting as C compilation already happened.
[T 15:45:54 31926] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32015] Attempting to switch stdout/stderr to 4...
[T 15:45:54 32015] Initializing tracer.
[T 15:45:54 32015] Initialising tags...
[T 15:45:54 32015] ID set to 0000000000007D0F_0000000000000001 (parent 0000000000007CAE_0000000000000001)
[E 15:45:54 32015] CodeQL C/C++ Extractor 2.11.2
[E 15:45:54 32015] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 15:45:54 32015] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/gcc code_01b06d7d.c -o code_01b06d7d
[T 15:45:54 32021] Initializing tracer.
[T 15:45:54 32021] Initialising tags...
[T 15:45:54 32021] ID set to 0000000000007D15_0000000000000001 (parent 0000000000007D0F_0000000000000001)
[T 15:45:54 32021] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:54 32021] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:54 32021] Executing the following tracer actions:
[T 15:45:54 32021] Tracer actions:
[T 15:45:54 32021] pre_invocations(0)
[T 15:45:54 32021] post_invocations(1)
[T 15:45:54 32021] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 15:45:54 32021] trace_languages(1): [cpp]
[T 15:45:54 32022] Initializing tracer.
[T 15:45:54 32022] Initialising tags...
[T 15:45:54 32022] ID set to 0000000000007D16_0000000000000001 (parent 0000000000007D15_0000000000000001)
[T 15:45:54 32022] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:54 32022] Executing the following tracer actions:
[T 15:45:54 32022] Tracer actions:
[T 15:45:54 32022] pre_invocations(0)
[T 15:45:54 32022] post_invocations(0)
[T 15:45:54 32022] trace_languages(1): [cpp]
[T 15:45:54 32022] Initializing tracer.
[T 15:45:54 32022] Initialising tags...
[T 15:45:54 32022] ID set to 0000000000007D16_0000000000000002 (parent 0000000000007D16_0000000000000001)
[T 15:45:54 32022] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:54 32022] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:54 32022] Executing the following tracer actions:
[T 15:45:54 32022] Tracer actions:
[T 15:45:54 32022] pre_invocations(0)
[T 15:45:54 32022] post_invocations(1)
[T 15:45:54 32022] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 15:45:54 32022] trace_languages(1): [cpp]
[T 15:45:54 32024] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32024] Initializing tracer.
[T 15:45:54 32024] Initialising tags...
[T 15:45:54 32024] ID set to 0000000000007D18_0000000000000001 (parent 0000000000007D16_0000000000000002)
[E 15:45:54 32024] Mimicry classification suppression detected; exiting.
[T 15:45:54 32022] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32025] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32025] Initializing tracer.
[T 15:45:54 32025] Initialising tags...
[T 15:45:54 32025] ID set to 0000000000007D19_0000000000000001 (parent 0000000000007D15_0000000000000001)
[E 15:45:54 32025] Mimicry classification suppression detected; exiting.
[T 15:45:54 32021] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32026] Initializing tracer.
[T 15:45:54 32026] Initialising tags...
[T 15:45:54 32026] ID set to 0000000000007D1A_0000000000000001 (parent 0000000000007D0F_0000000000000001)
[T 15:45:54 32026] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:54 32026] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:54 32026] Executing the following tracer actions:
[T 15:45:54 32026] Tracer actions:
[T 15:45:54 32026] pre_invocations(0)
[T 15:45:54 32026] post_invocations(1)
[T 15:45:54 32026] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 15:45:54 32026] trace_languages(1): [cpp]
[T 15:45:54 32027] Initializing tracer.
[T 15:45:54 32027] Initialising tags...
[T 15:45:54 32027] ID set to 0000000000007D1B_0000000000000001 (parent 0000000000007D1A_0000000000000001)
[T 15:45:54 32027] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:54 32027] Executing the following tracer actions:
[T 15:45:54 32027] Tracer actions:
[T 15:45:54 32027] pre_invocations(0)
[T 15:45:54 32027] post_invocations(0)
[T 15:45:54 32027] trace_languages(1): [cpp]
[T 15:45:54 32027] Initializing tracer.
[T 15:45:54 32027] Initialising tags...
[T 15:45:54 32027] ID set to 0000000000007D1B_0000000000000002 (parent 0000000000007D1B_0000000000000001)
[T 15:45:54 32027] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:54 32027] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:54 32027] Executing the following tracer actions:
[T 15:45:54 32027] Tracer actions:
[T 15:45:54 32027] pre_invocations(0)
[T 15:45:54 32027] post_invocations(1)
[T 15:45:54 32027] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 15:45:54 32027] trace_languages(1): [cpp]
[T 15:45:54 32029] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32029] Initializing tracer.
[T 15:45:54 32029] Initialising tags...
[T 15:45:54 32029] ID set to 0000000000007D1D_0000000000000001 (parent 0000000000007D1B_0000000000000002)
[E 15:45:54 32029] Mimicry classification suppression detected; exiting.
[T 15:45:54 32027] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32030] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32030] Initializing tracer.
[T 15:45:54 32030] Initialising tags...
[T 15:45:54 32030] ID set to 0000000000007D1E_0000000000000001 (parent 0000000000007D1A_0000000000000001)
[E 15:45:54 32030] Mimicry classification suppression detected; exiting.
[T 15:45:54 32026] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32031] Initializing tracer.
[T 15:45:54 32031] Initialising tags...
[T 15:45:54 32031] ID set to 0000000000007D1F_0000000000000001 (parent 0000000000007D0F_0000000000000001)
[T 15:45:54 32031] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:54 32031] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:54 32031] Executing the following tracer actions:
[T 15:45:54 32031] Tracer actions:
[T 15:45:54 32031] pre_invocations(0)
[T 15:45:54 32031] post_invocations(1)
[T 15:45:54 32031] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_32015_623180.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_32015_622912.c]
[T 15:45:54 32031] trace_languages(1): [cpp]
[T 15:45:54 32032] Initializing tracer.
[T 15:45:54 32032] Initialising tags...
[T 15:45:54 32032] ID set to 0000000000007D20_0000000000000001 (parent 0000000000007D1F_0000000000000001)
[T 15:45:54 32032] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:54 32032] Executing the following tracer actions:
[T 15:45:54 32032] Tracer actions:
[T 15:45:54 32032] pre_invocations(0)
[T 15:45:54 32032] post_invocations(0)
[T 15:45:54 32032] trace_languages(1): [cpp]
[T 15:45:54 32032] Initializing tracer.
[T 15:45:54 32032] Initialising tags...
[T 15:45:54 32032] ID set to 0000000000007D20_0000000000000002 (parent 0000000000007D20_0000000000000001)
[T 15:45:54 32032] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:54 32032] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:54 32032] Executing the following tracer actions:
[T 15:45:54 32032] Tracer actions:
[T 15:45:54 32032] pre_invocations(0)
[T 15:45:54 32032] post_invocations(1)
[T 15:45:54 32032] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_32015_623180.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_32015_622912.c]
[T 15:45:54 32032] trace_languages(1): [cpp]
[T 15:45:54 32034] Initializing tracer.
[T 15:45:54 32034] Initialising tags...
[T 15:45:54 32034] ID set to 0000000000007D22_0000000000000001 (parent 0000000000007D20_0000000000000002)
[T 15:45:54 32034] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:54 32034] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:54 32034] Executing the following tracer actions:
[T 15:45:54 32034] Tracer actions:
[T 15:45:54 32034] pre_invocations(0)
[T 15:45:54 32034] post_invocations(1)
[T 15:45:54 32034] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_32015_622912.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_32015_623180.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_32015_622912.c]
[T 15:45:54 32034] trace_languages(1): [cpp]
[T 15:45:54 32036] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32036] Initializing tracer.
[T 15:45:54 32036] Initialising tags...
[T 15:45:54 32036] ID set to 0000000000007D24_0000000000000001 (parent 0000000000007D22_0000000000000001)
[E 15:45:54 32036] Mimicry classification suppression detected; exiting.
[T 15:45:54 32034] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32037] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32037] Initializing tracer.
[T 15:45:54 32037] Initialising tags...
[T 15:45:54 32037] ID set to 0000000000007D25_0000000000000001 (parent 0000000000007D20_0000000000000002)
[E 15:45:54 32037] Mimicry classification suppression detected; exiting.
[T 15:45:54 32032] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32038] Attempting to switch stdout/stderr to 7...
[T 15:45:54 32038] Initializing tracer.
[T 15:45:54 32038] Initialising tags...
[T 15:45:54 32038] ID set to 0000000000007D26_0000000000000001 (parent 0000000000007D1F_0000000000000001)
[E 15:45:54 32038] Mimicry classification suppression detected; exiting.
[T 15:45:54 32031] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:54 32039] Initializing tracer.
[T 15:45:54 32039] Initialising tags...
[T 15:45:54 32039] ID set to 0000000000007D27_0000000000000001 (parent 0000000000007D0F_0000000000000001)
[T 15:45:54 32039] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:54 32039] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:54 32039] Executing the following tracer actions:
[T 15:45:54 32039] Tracer actions:
[T 15:45:54 32039] pre_invocations(0)
[T 15:45:54 32039] post_invocations(1)
[T 15:45:54 32039] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_32015_874398.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_32015_874142.c]
[T 15:45:54 32039] trace_languages(1): [cpp]
[T 15:45:54 32040] Initializing tracer.
[T 15:45:54 32040] Initialising tags...
[T 15:45:54 32040] ID set to 0000000000007D28_0000000000000001 (parent 0000000000007D27_0000000000000001)
[T 15:45:54 32040] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:54 32040] Executing the following tracer actions:
[T 15:45:54 32040] Tracer actions:
[T 15:45:54 32040] pre_invocations(0)
[T 15:45:54 32040] post_invocations(0)
[T 15:45:54 32040] trace_languages(1): [cpp]
[T 15:45:54 32040] Initializing tracer.
[T 15:45:54 32040] Initialising tags...
[T 15:45:54 32040] ID set to 0000000000007D28_0000000000000002 (parent 0000000000007D28_0000000000000001)
[T 15:45:54 32040] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:54 32040] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:54 32040] Executing the following tracer actions:
[T 15:45:54 32040] Tracer actions:
[T 15:45:54 32040] pre_invocations(0)
[T 15:45:54 32040] post_invocations(1)
[T 15:45:54 32040] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_32015_874398.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_32015_874142.c]
[T 15:45:54 32040] trace_languages(1): [cpp]
[T 15:45:55 32042] Initializing tracer.
[T 15:45:55 32042] Initialising tags...
[T 15:45:55 32042] ID set to 0000000000007D2A_0000000000000001 (parent 0000000000007D28_0000000000000002)
[T 15:45:55 32042] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:55 32042] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:55 32042] Executing the following tracer actions:
[T 15:45:55 32042] Tracer actions:
[T 15:45:55 32042] pre_invocations(0)
[T 15:45:55 32042] post_invocations(1)
[T 15:45:55 32042] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_32015_874142.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_32015_874398.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_32015_874142.c]
[T 15:45:55 32042] trace_languages(1): [cpp]
[T 15:45:55 32044] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32044] Initializing tracer.
[T 15:45:55 32044] Initialising tags...
[T 15:45:55 32044] ID set to 0000000000007D2C_0000000000000001 (parent 0000000000007D2A_0000000000000001)
[E 15:45:55 32044] Mimicry classification suppression detected; exiting.
[T 15:45:55 32042] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32045] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32045] Initializing tracer.
[T 15:45:55 32045] Initialising tags...
[T 15:45:55 32045] ID set to 0000000000007D2D_0000000000000001 (parent 0000000000007D28_0000000000000002)
[E 15:45:55 32045] Mimicry classification suppression detected; exiting.
[T 15:45:55 32040] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32046] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32046] Initializing tracer.
[T 15:45:55 32046] Initialising tags...
[T 15:45:55 32046] ID set to 0000000000007D2E_0000000000000001 (parent 0000000000007D27_0000000000000001)
[E 15:45:55 32046] Mimicry classification suppression detected; exiting.
[T 15:45:55 32039] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 15:45:55 32015] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 15:45:55 32015] Checking whether C compilation already happened.
[E 15:45:55 32015] Checking for tag c-compilation-happened
[E 15:45:55 32015] Checking CODEQL_TRACER_DB_ID 0000000000007CAE_0000000000000001
[E 15:45:55 32015] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:55 32015] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:55 32015] Unlocking DB
[E 15:45:55 32015] Unlocked DB
[E 15:45:55 32015] Exiting as C compilation already happened.
[T 15:45:55 31918] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32047] Attempting to switch stdout/stderr to 4...
[T 15:45:55 32047] Initializing tracer.
[T 15:45:55 32047] Initialising tags...
[T 15:45:55 32047] ID set to 0000000000007D2F_0000000000000001 (parent 0000000000007CAC_0000000000000001)
[E 15:45:55 32047] CodeQL C/C++ Extractor 2.11.2
[E 15:45:55 32047] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 15:45:55 32047] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /usr/bin/gcc code_01b06d7d.c -o code_01b06d7d
[T 15:45:55 32048] Initializing tracer.
[T 15:45:55 32048] Initialising tags...
[T 15:45:55 32048] ID set to 0000000000007D30_0000000000000001 (parent 0000000000007D2F_0000000000000001)
[T 15:45:55 32048] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 15:45:55 32048] Lua: === Intercepted call to /usr/bin/gcc ===
[T 15:45:55 32048] Executing the following tracer actions:
[T 15:45:55 32048] Tracer actions:
[T 15:45:55 32048] pre_invocations(0)
[T 15:45:55 32048] post_invocations(1)
[T 15:45:55 32048] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --version]
[T 15:45:55 32048] trace_languages(1): [cpp]
[T 15:45:55 32049] Initializing tracer.
[T 15:45:55 32049] Initialising tags...
[T 15:45:55 32049] ID set to 0000000000007D31_0000000000000001 (parent 0000000000007D30_0000000000000001)
[T 15:45:55 32049] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:55 32049] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:55 32049] Executing the following tracer actions:
[T 15:45:55 32049] Tracer actions:
[T 15:45:55 32049] pre_invocations(0)
[T 15:45:55 32049] post_invocations(1)
[T 15:45:55 32049] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 15:45:55 32049] trace_languages(1): [cpp]
[T 15:45:55 32050] Initializing tracer.
[T 15:45:55 32050] Initialising tags...
[T 15:45:55 32050] ID set to 0000000000007D32_0000000000000001 (parent 0000000000007D31_0000000000000001)
[T 15:45:55 32050] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:55 32050] Executing the following tracer actions:
[T 15:45:55 32050] Tracer actions:
[T 15:45:55 32050] pre_invocations(0)
[T 15:45:55 32050] post_invocations(0)
[T 15:45:55 32050] trace_languages(1): [cpp]
[T 15:45:55 32050] Initializing tracer.
[T 15:45:55 32050] Initialising tags...
[T 15:45:55 32050] ID set to 0000000000007D32_0000000000000002 (parent 0000000000007D32_0000000000000001)
[T 15:45:55 32050] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:55 32050] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:55 32050] Executing the following tracer actions:
[T 15:45:55 32050] Tracer actions:
[T 15:45:55 32050] pre_invocations(0)
[T 15:45:55 32050] post_invocations(1)
[T 15:45:55 32050] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 15:45:55 32050] trace_languages(1): [cpp]
[T 15:45:55 32052] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32052] Initializing tracer.
[T 15:45:55 32052] Initialising tags...
[T 15:45:55 32052] ID set to 0000000000007D34_0000000000000001 (parent 0000000000007D32_0000000000000002)
[E 15:45:55 32052] Mimicry classification suppression detected; exiting.
[T 15:45:55 32050] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32053] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32053] Initializing tracer.
[T 15:45:55 32053] Initialising tags...
[T 15:45:55 32053] ID set to 0000000000007D35_0000000000000001 (parent 0000000000007D31_0000000000000001)
[E 15:45:55 32053] Mimicry classification suppression detected; exiting.
[T 15:45:55 32049] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32054] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32054] Initializing tracer.
[T 15:45:55 32054] Initialising tags...
[T 15:45:55 32054] ID set to 0000000000007D36_0000000000000001 (parent 0000000000007D30_0000000000000001)
[E 15:45:55 32054] Mimicry classification suppression detected; exiting.
[T 15:45:55 32048] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32055] Initializing tracer.
[T 15:45:55 32055] Initialising tags...
[T 15:45:55 32055] ID set to 0000000000007D37_0000000000000001 (parent 0000000000007D2F_0000000000000001)
[T 15:45:55 32055] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 15:45:55 32055] Lua: === Intercepted call to /usr/bin/gcc ===
[T 15:45:55 32055] Executing the following tracer actions:
[T 15:45:55 32055] Tracer actions:
[T 15:45:55 32055] pre_invocations(0)
[T 15:45:55 32055] post_invocations(1)
[T 15:45:55 32055] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --help]
[T 15:45:55 32055] trace_languages(1): [cpp]
[T 15:45:55 32056] Initializing tracer.
[T 15:45:55 32056] Initialising tags...
[T 15:45:55 32056] ID set to 0000000000007D38_0000000000000001 (parent 0000000000007D37_0000000000000001)
[T 15:45:55 32056] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:55 32056] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:55 32056] Executing the following tracer actions:
[T 15:45:55 32056] Tracer actions:
[T 15:45:55 32056] pre_invocations(0)
[T 15:45:55 32056] post_invocations(1)
[T 15:45:55 32056] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 15:45:55 32056] trace_languages(1): [cpp]
[T 15:45:55 32057] Initializing tracer.
[T 15:45:55 32057] Initialising tags...
[T 15:45:55 32057] ID set to 0000000000007D39_0000000000000001 (parent 0000000000007D38_0000000000000001)
[T 15:45:55 32057] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:55 32057] Executing the following tracer actions:
[T 15:45:55 32057] Tracer actions:
[T 15:45:55 32057] pre_invocations(0)
[T 15:45:55 32057] post_invocations(0)
[T 15:45:55 32057] trace_languages(1): [cpp]
[T 15:45:55 32057] Initializing tracer.
[T 15:45:55 32057] Initialising tags...
[T 15:45:55 32057] ID set to 0000000000007D39_0000000000000002 (parent 0000000000007D39_0000000000000001)
[T 15:45:55 32057] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:55 32057] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:55 32057] Executing the following tracer actions:
[T 15:45:55 32057] Tracer actions:
[T 15:45:55 32057] pre_invocations(0)
[T 15:45:55 32057] post_invocations(1)
[T 15:45:55 32057] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 15:45:55 32057] trace_languages(1): [cpp]
[T 15:45:55 32059] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32059] Initializing tracer.
[T 15:45:55 32059] Initialising tags...
[T 15:45:55 32059] ID set to 0000000000007D3B_0000000000000001 (parent 0000000000007D39_0000000000000002)
[E 15:45:55 32059] Mimicry classification suppression detected; exiting.
[T 15:45:55 32057] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32060] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32060] Initializing tracer.
[T 15:45:55 32060] Initialising tags...
[T 15:45:55 32060] ID set to 0000000000007D3C_0000000000000001 (parent 0000000000007D38_0000000000000001)
[E 15:45:55 32060] Mimicry classification suppression detected; exiting.
[T 15:45:55 32056] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32061] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32061] Initializing tracer.
[T 15:45:55 32061] Initialising tags...
[T 15:45:55 32061] ID set to 0000000000007D3D_0000000000000001 (parent 0000000000007D37_0000000000000001)
[E 15:45:55 32061] Mimicry classification suppression detected; exiting.
[T 15:45:55 32055] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32062] Initializing tracer.
[T 15:45:55 32062] Initialising tags...
[T 15:45:55 32062] ID set to 0000000000007D3E_0000000000000001 (parent 0000000000007D2F_0000000000000001)
[T 15:45:55 32062] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 15:45:55 32062] Lua: === Intercepted call to /usr/bin/gcc ===
[T 15:45:55 32062] Executing the following tracer actions:
[T 15:45:55 32062] Tracer actions:
[T 15:45:55 32062] pre_invocations(0)
[T 15:45:55 32062] post_invocations(1)
[T 15:45:55 32062] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_32047_568058.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_32047_567760.c]
[T 15:45:55 32062] trace_languages(1): [cpp]
[T 15:45:55 32063] Initializing tracer.
[T 15:45:55 32063] Initialising tags...
[T 15:45:55 32063] ID set to 0000000000007D3F_0000000000000001 (parent 0000000000007D3E_0000000000000001)
[T 15:45:55 32063] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:55 32063] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:55 32063] Executing the following tracer actions:
[T 15:45:55 32063] Tracer actions:
[T 15:45:55 32063] pre_invocations(0)
[T 15:45:55 32063] post_invocations(1)
[T 15:45:55 32063] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_32047_568058.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_32047_567760.c]
[T 15:45:55 32063] trace_languages(1): [cpp]
[T 15:45:55 32064] Initializing tracer.
[T 15:45:55 32064] Initialising tags...
[T 15:45:55 32064] ID set to 0000000000007D40_0000000000000001 (parent 0000000000007D3F_0000000000000001)
[T 15:45:55 32064] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:55 32064] Executing the following tracer actions:
[T 15:45:55 32064] Tracer actions:
[T 15:45:55 32064] pre_invocations(0)
[T 15:45:55 32064] post_invocations(0)
[T 15:45:55 32064] trace_languages(1): [cpp]
[T 15:45:55 32064] Initializing tracer.
[T 15:45:55 32064] Initialising tags...
[T 15:45:55 32064] ID set to 0000000000007D40_0000000000000002 (parent 0000000000007D40_0000000000000001)
[T 15:45:55 32064] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:55 32064] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:55 32064] Executing the following tracer actions:
[T 15:45:55 32064] Tracer actions:
[T 15:45:55 32064] pre_invocations(0)
[T 15:45:55 32064] post_invocations(1)
[T 15:45:55 32064] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_32047_568058.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_32047_567760.c]
[T 15:45:55 32064] trace_languages(1): [cpp]
[T 15:45:55 32066] Initializing tracer.
[T 15:45:55 32066] Initialising tags...
[T 15:45:55 32066] ID set to 0000000000007D42_0000000000000001 (parent 0000000000007D40_0000000000000002)
[T 15:45:55 32066] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:55 32066] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:55 32066] Executing the following tracer actions:
[T 15:45:55 32066] Tracer actions:
[T 15:45:55 32066] pre_invocations(0)
[T 15:45:55 32066] post_invocations(1)
[T 15:45:55 32066] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_32047_567760.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_2_32047_568058.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_1_32047_567760.c]
[T 15:45:55 32066] trace_languages(1): [cpp]
[T 15:45:55 32068] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32068] Initializing tracer.
[T 15:45:55 32068] Initialising tags...
[T 15:45:55 32068] ID set to 0000000000007D44_0000000000000001 (parent 0000000000007D42_0000000000000001)
[E 15:45:55 32068] Mimicry classification suppression detected; exiting.
[T 15:45:55 32066] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32069] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32069] Initializing tracer.
[T 15:45:55 32069] Initialising tags...
[T 15:45:55 32069] ID set to 0000000000007D45_0000000000000001 (parent 0000000000007D40_0000000000000002)
[E 15:45:55 32069] Mimicry classification suppression detected; exiting.
[T 15:45:55 32064] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32070] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32070] Initializing tracer.
[T 15:45:55 32070] Initialising tags...
[T 15:45:55 32070] ID set to 0000000000007D46_0000000000000001 (parent 0000000000007D3F_0000000000000001)
[E 15:45:55 32070] Mimicry classification suppression detected; exiting.
[T 15:45:55 32063] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32071] Attempting to switch stdout/stderr to 7...
[T 15:45:55 32071] Initializing tracer.
[T 15:45:55 32071] Initialising tags...
[T 15:45:55 32071] ID set to 0000000000007D47_0000000000000001 (parent 0000000000007D3E_0000000000000001)
[E 15:45:55 32071] Mimicry classification suppression detected; exiting.
[T 15:45:55 32062] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:55 32072] Initializing tracer.
[T 15:45:55 32072] Initialising tags...
[T 15:45:55 32072] ID set to 0000000000007D48_0000000000000001 (parent 0000000000007D2F_0000000000000001)
[T 15:45:55 32072] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 15:45:55 32072] Lua: === Intercepted call to /usr/bin/gcc ===
[T 15:45:55 32072] Executing the following tracer actions:
[T 15:45:55 32072] Tracer actions:
[T 15:45:55 32072] pre_invocations(0)
[T 15:45:55 32072] post_invocations(1)
[T 15:45:55 32072] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_32047_870621.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_32047_870367.c]
[T 15:45:55 32072] trace_languages(1): [cpp]
[T 15:45:55 32073] Initializing tracer.
[T 15:45:55 32073] Initialising tags...
[T 15:45:55 32073] ID set to 0000000000007D49_0000000000000001 (parent 0000000000007D48_0000000000000001)
[T 15:45:55 32073] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 15:45:55 32073] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 15:45:55 32073] Executing the following tracer actions:
[T 15:45:55 32073] Tracer actions:
[T 15:45:55 32073] pre_invocations(0)
[T 15:45:55 32073] post_invocations(1)
[T 15:45:55 32073] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_32047_870621.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_32047_870367.c]
[T 15:45:55 32073] trace_languages(1): [cpp]
[T 15:45:55 32074] Initializing tracer.
[T 15:45:55 32074] Initialising tags...
[T 15:45:55 32074] ID set to 0000000000007D4A_0000000000000001 (parent 0000000000007D49_0000000000000001)
[T 15:45:55 32074] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 15:45:55 32074] Executing the following tracer actions:
[T 15:45:55 32074] Tracer actions:
[T 15:45:55 32074] pre_invocations(0)
[T 15:45:55 32074] post_invocations(0)
[T 15:45:55 32074] trace_languages(1): [cpp]
[T 15:45:55 32074] Initializing tracer.
[T 15:45:55 32074] Initialising tags...
[T 15:45:55 32074] ID set to 0000000000007D4A_0000000000000002 (parent 0000000000007D4A_0000000000000001)
[T 15:45:55 32074] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:55 32074] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:55 32074] Executing the following tracer actions:
[T 15:45:55 32074] Tracer actions:
[T 15:45:55 32074] pre_invocations(0)
[T 15:45:55 32074] post_invocations(1)
[T 15:45:55 32074] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_32047_870621.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_32047_870367.c]
[T 15:45:55 32074] trace_languages(1): [cpp]
[T 15:45:56 32076] Initializing tracer.
[T 15:45:56 32076] Initialising tags...
[T 15:45:56 32076] ID set to 0000000000007D4C_0000000000000001 (parent 0000000000007D4A_0000000000000002)
[T 15:45:56 32076] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 15:45:56 32076] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 15:45:56 32076] Executing the following tracer actions:
[T 15:45:56 32076] Tracer actions:
[T 15:45:56 32076] pre_invocations(0)
[T 15:45:56 32076] post_invocations(1)
[T 15:45:56 32076] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_32047_870367.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_4_32047_870621.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tmp//semmle_3_32047_870367.c]
[T 15:45:56 32076] trace_languages(1): [cpp]
[T 15:45:56 32078] Attempting to switch stdout/stderr to 7...
[T 15:45:56 32078] Initializing tracer.
[T 15:45:56 32078] Initialising tags...
[T 15:45:56 32078] ID set to 0000000000007D4E_0000000000000001 (parent 0000000000007D4C_0000000000000001)
[E 15:45:56 32078] Mimicry classification suppression detected; exiting.
[T 15:45:56 32076] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:56 32079] Attempting to switch stdout/stderr to 7...
[T 15:45:56 32079] Initializing tracer.
[T 15:45:56 32079] Initialising tags...
[T 15:45:56 32079] ID set to 0000000000007D4F_0000000000000001 (parent 0000000000007D4A_0000000000000002)
[E 15:45:56 32079] Mimicry classification suppression detected; exiting.
[T 15:45:56 32074] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:56 32080] Attempting to switch stdout/stderr to 7...
[T 15:45:56 32080] Initializing tracer.
[T 15:45:56 32080] Initialising tags...
[T 15:45:56 32080] ID set to 0000000000007D50_0000000000000001 (parent 0000000000007D49_0000000000000001)
[E 15:45:56 32080] Mimicry classification suppression detected; exiting.
[T 15:45:56 32073] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 15:45:56 32081] Attempting to switch stdout/stderr to 7...
[T 15:45:56 32081] Initializing tracer.
[T 15:45:56 32081] Initialising tags...
[T 15:45:56 32081] ID set to 0000000000007D51_0000000000000001 (parent 0000000000007D48_0000000000000001)
[E 15:45:56 32081] Mimicry classification suppression detected; exiting.
[T 15:45:56 32072] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 15:45:56 32047] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 15:45:56 32047] Checking whether C compilation already happened.
[E 15:45:56 32047] Checking for tag c-compilation-happened
[E 15:45:56 32047] Checking CODEQL_TRACER_DB_ID 0000000000007CAC_0000000000000001
[E 15:45:56 32047] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:56 32047] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:56 32047] Unlocking DB
[E 15:45:56 32047] Unlocked DB
[E 15:45:56 32047] Exiting as C compilation already happened.
[T 15:45:56 31916] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.

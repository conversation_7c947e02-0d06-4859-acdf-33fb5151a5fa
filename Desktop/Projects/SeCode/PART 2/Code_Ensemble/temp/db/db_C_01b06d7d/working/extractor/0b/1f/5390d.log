[E 15:45:53 31993] CodeQL C/C++ Extractor 2.11.2
[E 15:45:53 31993] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 15:45:53 31993] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_01b06d7d.c -o code_01b06d7d
[E 15:45:54 31993] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 15:45:54 31993] Checking whether C compilation already happened.
[E 15:45:54 31993] Checking for tag c-compilation-happened
[E 15:45:54 31993] Checking CODEQL_TRACER_DB_ID 0000000000007CB6_0000000000000002
[E 15:45:54 31993] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:54 31993] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_01b06d7d/working/tags.db
[E 15:45:54 31993] Unlocking DB
[E 15:45:54 31993] Unlocked DB
[E 15:45:54 31993] Exiting as C compilation already happened.

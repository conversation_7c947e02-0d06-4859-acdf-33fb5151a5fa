#include <stdio.h>
#include <stdlib.h>
#include <string.h>

char* replaceSpecialChars(char* str) {
    int maxLen = 1024;
    char* result = (char*)malloc(maxLen * sizeof(char));
    if (!result) {
        printf("Memory allocation failed\n");
        exit(1);
    }
    
    int len = strlen(str);
    if (len >= maxLen) {
        printf("Input string too long. Terminating program.\n");
    for (int i = 0; i < len; i++) {
        switch (str[i]) {
            case '&':
                strcpy(result + strlen(result), "&amp;");
                break;
            case '<':
                strcpy(result + strlen(result), "&lt;");
            case '>':
                strcpy(result + strlen(result), "&gt;");
            default:
                result = strcat(result, str + i);
        }
    return result;
}
clang -cc1 configuration (redacted redacted)
tries (redacted redacted)
SOLID-AN
/library/developer/commandlinetools/usr/bin/clang
-cc1
c
with arguments:
-triple
x86_64-apple-macosx15.0.0
-mrelax-all
-mrelocation-model
pic
-pic-level
2
-mframe-pointer=all
-fno-strict-return
-ffp-contract=on
-fno-rounding-math
-funwind-tables=2
-fvisibility-inlines-hidden-static-local-var
-fno-modulemap-allow-subdirectory-search
-target-cpu
penryn
-target-linker-version
1115.7.3
-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
-resource-dir
/Library/Developer/CommandLineTools/usr/lib/clang/16
-isysroot
/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk
-internal-isystem
/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include
-internal-isystem
/Library/Developer/CommandLineTools/usr/lib/clang/16/include
-internal-externc-isystem
/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include
-internal-externc-isystem
/Library/Developer/CommandLineTools/usr/include
-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
-fstack-check
-mdarwin-stkchk-strong-link
-fblocks
-fencode-extended-block-signature
-fregister-global-dtors-with-atexit
-fgnuc-version=4.2.1
-fmax-type-align=16
-fcommon
-fno-odr-hash-protocols
-----
ienvironment CPATH
ienvironment C_INCLUDE_PATH
append -I/Library/Developer/CommandLineTools/usr/lib/clang/16/include
append -I/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include
append -I/Library/Developer/CommandLineTools/usr/include
append -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks
predefine _LP64=1
predefine __APPLE_CC__=6000
predefine __APPLE__=1
predefine __ATOMIC_ACQUIRE=2
predefine __ATOMIC_ACQ_REL=4
predefine __ATOMIC_CONSUME=1
predefine __ATOMIC_RELAXED=0
predefine __ATOMIC_RELEASE=3
predefine __ATOMIC_SEQ_CST=5
predefine __BIGGEST_ALIGNMENT__=16
predefine __BITINT_MAXWIDTH__=8388608
predefine __BLOCKS__=1
predefine __BOOL_WIDTH__=8
predefine __BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__
predefine __CHAR_BIT__=8
predefine __CLANG_ATOMIC_BOOL_LOCK_FREE=2
predefine __CLANG_ATOMIC_CHAR16_T_LOCK_FREE=2
predefine __CLANG_ATOMIC_CHAR32_T_LOCK_FREE=2
predefine __CLANG_ATOMIC_CHAR_LOCK_FREE=2
predefine __CLANG_ATOMIC_INT_LOCK_FREE=2
predefine __CLANG_ATOMIC_LLONG_LOCK_FREE=2
predefine __CLANG_ATOMIC_LONG_LOCK_FREE=2
predefine __CLANG_ATOMIC_POINTER_LOCK_FREE=2
predefine __CLANG_ATOMIC_SHORT_LOCK_FREE=2
predefine __CLANG_ATOMIC_WCHAR_T_LOCK_FREE=2
predefine __CONSTANT_CFSTRINGS__=1
predefine __DBL_DECIMAL_DIG__=17
predefine __DBL_DENORM_MIN__=4.9406564584124654e-324
predefine __DBL_DIG__=15
predefine __DBL_EPSILON__=2.2204460492503131e-16
predefine __DBL_HAS_DENORM__=1
predefine __DBL_HAS_INFINITY__=1
predefine __DBL_HAS_QUIET_NAN__=1
predefine __DBL_MANT_DIG__=53
predefine __DBL_MAX_10_EXP__=308
predefine __DBL_MAX_EXP__=1024
predefine __DBL_MAX__=1.7976931348623157e+308
predefine __DBL_MIN_10_EXP__=(-307)
predefine __DBL_MIN_EXP__=(-1021)
predefine __DBL_MIN__=2.2250738585072014e-308
predefine __DECIMAL_DIG__=__LDBL_DECIMAL_DIG__
predefine __DYNAMIC__=1
predefine __ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__=150000
predefine __ENVIRONMENT_OS_VERSION_MIN_REQUIRED__=150000
predefine __FINITE_MATH_ONLY__=0
predefine __FLT16_DECIMAL_DIG__=5
predefine __FLT16_DENORM_MIN__=5.9604644775390625e-8F16
predefine __FLT16_DIG__=3
predefine __FLT16_EPSILON__=9.765625e-4F16
predefine __FLT16_HAS_DENORM__=1
predefine __FLT16_HAS_INFINITY__=1
predefine __FLT16_HAS_QUIET_NAN__=1
predefine __FLT16_MANT_DIG__=11
predefine __FLT16_MAX_10_EXP__=4
predefine __FLT16_MAX_EXP__=16
predefine __FLT16_MAX__=6.5504e+4F16
predefine __FLT16_MIN_10_EXP__=(-4)
predefine __FLT16_MIN_EXP__=(-13)
predefine __FLT16_MIN__=6.103515625e-5F16
predefine __FLT_DECIMAL_DIG__=9
predefine __FLT_DENORM_MIN__=1.40129846e-45F
predefine __FLT_DIG__=6
predefine __FLT_EPSILON__=1.19209290e-7F
predefine __FLT_HAS_DENORM__=1
predefine __FLT_HAS_INFINITY__=1
predefine __FLT_HAS_QUIET_NAN__=1
predefine __FLT_MANT_DIG__=24
predefine __FLT_MAX_10_EXP__=38
predefine __FLT_MAX_EXP__=128
predefine __FLT_MAX__=3.40282347e+38F
predefine __FLT_MIN_10_EXP__=(-37)
predefine __FLT_MIN_EXP__=(-125)
predefine __FLT_MIN__=1.17549435e-38F
predefine __FLT_RADIX__=2
predefine __FPCLASS_NEGINF=0x0004
predefine __FPCLASS_NEGNORMAL=0x0008
predefine __FPCLASS_NEGSUBNORMAL=0x0010
predefine __FPCLASS_NEGZERO=0x0020
predefine __FPCLASS_POSINF=0x0200
predefine __FPCLASS_POSNORMAL=0x0100
predefine __FPCLASS_POSSUBNORMAL=0x0080
predefine __FPCLASS_POSZERO=0x0040
predefine __FPCLASS_QNAN=0x0002
predefine __FPCLASS_SNAN=0x0001
predefine __FXSR__=1
predefine __GCC_ASM_FLAG_OUTPUTS__=1
predefine __GCC_ATOMIC_BOOL_LOCK_FREE=2
predefine __GCC_ATOMIC_CHAR16_T_LOCK_FREE=2
predefine __GCC_ATOMIC_CHAR32_T_LOCK_FREE=2
predefine __GCC_ATOMIC_CHAR_LOCK_FREE=2
predefine __GCC_ATOMIC_INT_LOCK_FREE=2
predefine __GCC_ATOMIC_LLONG_LOCK_FREE=2
predefine __GCC_ATOMIC_LONG_LOCK_FREE=2
predefine __GCC_ATOMIC_POINTER_LOCK_FREE=2
predefine __GCC_ATOMIC_SHORT_LOCK_FREE=2
predefine __GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1
predefine __GCC_ATOMIC_WCHAR_T_LOCK_FREE=2
predefine __GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1
predefine __GCC_HAVE_SYNC_COMPARE_AND_SWAP_16=1
predefine __GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1
predefine __GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1
predefine __GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1
predefine __GNUC_MINOR__=2
predefine __GNUC_PATCHLEVEL__=1
predefine __GNUC__=4
predefine __GXX_ABI_VERSION=1002
predefine __INT16_C_SUFFIX__=
predefine __INT16_FMTd__="hd"
predefine __INT16_FMTi__="hi"
predefine __INT16_MAX__=32767
predefine __INT16_TYPE__=short
predefine __INT32_C_SUFFIX__=
predefine __INT32_FMTd__="d"
predefine __INT32_FMTi__="i"
predefine __INT32_MAX__=2147483647
predefine __INT32_TYPE__=int
predefine __INT64_C_SUFFIX__=LL
predefine __INT64_FMTd__="lld"
predefine __INT64_FMTi__="lli"
predefine __INT64_MAX__=9223372036854775807LL
predefine __INT64_TYPE__=long long int
predefine __INT8_C_SUFFIX__=
predefine __INT8_FMTd__="hhd"
predefine __INT8_FMTi__="hhi"
predefine __INT8_MAX__=127
predefine __INT8_TYPE__=signed char
predefine __INTMAX_C_SUFFIX__=L
predefine __INTMAX_FMTd__="ld"
predefine __INTMAX_FMTi__="li"
predefine __INTMAX_MAX__=9223372036854775807L
predefine __INTMAX_TYPE__=long int
predefine __INTMAX_WIDTH__=64
predefine __INTPTR_FMTd__="ld"
predefine __INTPTR_FMTi__="li"
predefine __INTPTR_MAX__=9223372036854775807L
predefine __INTPTR_TYPE__=long int
predefine __INTPTR_WIDTH__=64
predefine __INT_FAST16_FMTd__="hd"
predefine __INT_FAST16_FMTi__="hi"
predefine __INT_FAST16_MAX__=32767
predefine __INT_FAST16_TYPE__=short
predefine __INT_FAST16_WIDTH__=16
predefine __INT_FAST32_FMTd__="d"
predefine __INT_FAST32_FMTi__="i"
predefine __INT_FAST32_MAX__=2147483647
predefine __INT_FAST32_TYPE__=int
predefine __INT_FAST32_WIDTH__=32
predefine __INT_FAST64_FMTd__="lld"
predefine __INT_FAST64_FMTi__="lli"
predefine __INT_FAST64_MAX__=9223372036854775807LL
predefine __INT_FAST64_TYPE__=long long int
predefine __INT_FAST64_WIDTH__=64
predefine __INT_FAST8_FMTd__="hhd"
predefine __INT_FAST8_FMTi__="hhi"
predefine __INT_FAST8_MAX__=127
predefine __INT_FAST8_TYPE__=signed char
predefine __INT_FAST8_WIDTH__=8
predefine __INT_LEAST16_FMTd__="hd"
predefine __INT_LEAST16_FMTi__="hi"
predefine __INT_LEAST16_MAX__=32767
predefine __INT_LEAST16_TYPE__=short
predefine __INT_LEAST16_WIDTH__=16
predefine __INT_LEAST32_FMTd__="d"
predefine __INT_LEAST32_FMTi__="i"
predefine __INT_LEAST32_MAX__=2147483647
predefine __INT_LEAST32_TYPE__=int
predefine __INT_LEAST32_WIDTH__=32
predefine __INT_LEAST64_FMTd__="lld"
predefine __INT_LEAST64_FMTi__="lli"
predefine __INT_LEAST64_MAX__=9223372036854775807LL
predefine __INT_LEAST64_TYPE__=long long int
predefine __INT_LEAST64_WIDTH__=64
predefine __INT_LEAST8_FMTd__="hhd"
predefine __INT_LEAST8_FMTi__="hhi"
predefine __INT_LEAST8_MAX__=127
predefine __INT_LEAST8_TYPE__=signed char
predefine __INT_LEAST8_WIDTH__=8
predefine __INT_MAX__=2147483647
predefine __INT_WIDTH__=32
predefine __LAHF_SAHF__=1
predefine __LDBL_DECIMAL_DIG__=21
predefine __LDBL_DENORM_MIN__=3.64519953188247460253e-4951L
predefine __LDBL_DIG__=18
predefine __LDBL_EPSILON__=1.08420217248550443401e-19L
predefine __LDBL_HAS_DENORM__=1
predefine __LDBL_HAS_INFINITY__=1
predefine __LDBL_HAS_QUIET_NAN__=1
predefine __LDBL_MANT_DIG__=64
predefine __LDBL_MAX_10_EXP__=4932
predefine __LDBL_MAX_EXP__=16384
predefine __LDBL_MAX__=1.18973149535723176502e+4932L
predefine __LDBL_MIN_10_EXP__=(-4931)
predefine __LDBL_MIN_EXP__=(-16381)
predefine __LDBL_MIN__=3.36210314311209350626e-4932L
predefine __LITTLE_ENDIAN__=1
predefine __LLONG_WIDTH__=64
predefine __LONG_LONG_MAX__=9223372036854775807LL
predefine __LONG_MAX__=9223372036854775807L
predefine __LONG_WIDTH__=64
predefine __LP64__=1
predefine __MACH__=1
predefine __MMX__=1
predefine __NO_INLINE__=1
predefine __NO_MATH_ERRNO__=1
predefine __NO_MATH_INLINES=1
predefine __OBJC_BOOL_IS_BOOL=0
predefine __OPENCL_MEMORY_SCOPE_ALL_SVM_DEVICES=3
predefine __OPENCL_MEMORY_SCOPE_DEVICE=2
predefine __OPENCL_MEMORY_SCOPE_SUB_GROUP=4
predefine __OPENCL_MEMORY_SCOPE_WORK_GROUP=1
predefine __OPENCL_MEMORY_SCOPE_WORK_ITEM=0
predefine __ORDER_BIG_ENDIAN__=4321
predefine __ORDER_LITTLE_ENDIAN__=1234
predefine __ORDER_PDP_ENDIAN__=3412
predefine __PIC__=2
predefine __POINTER_WIDTH__=64
predefine __PRAGMA_REDEFINE_EXTNAME=1
predefine __PTRDIFF_FMTd__="ld"
predefine __PTRDIFF_FMTi__="li"
predefine __PTRDIFF_MAX__=9223372036854775807L
predefine __PTRDIFF_TYPE__=long int
predefine __PTRDIFF_WIDTH__=64
predefine __REGISTER_PREFIX__=
predefine __SCHAR_MAX__=127
predefine __SEG_FS=1
predefine __SEG_GS=1
predefine __SHRT_MAX__=32767
predefine __SHRT_WIDTH__=16
predefine __SIG_ATOMIC_MAX__=2147483647
predefine __SIG_ATOMIC_WIDTH__=32
predefine __SIZEOF_DOUBLE__=8
predefine __SIZEOF_FLOAT__=4
predefine __SIZEOF_INT128__=16
predefine __SIZEOF_INT__=4
predefine __SIZEOF_LONG_DOUBLE__=16
predefine __SIZEOF_LONG_LONG__=8
predefine __SIZEOF_LONG__=8
predefine __SIZEOF_POINTER__=8
predefine __SIZEOF_PTRDIFF_T__=8
predefine __SIZEOF_SHORT__=2
predefine __SIZEOF_SIZE_T__=8
predefine __SIZEOF_WCHAR_T__=4
predefine __SIZEOF_WINT_T__=4
predefine __SIZE_FMTX__="lX"
predefine __SIZE_FMTo__="lo"
predefine __SIZE_FMTu__="lu"
predefine __SIZE_FMTx__="lx"
predefine __SIZE_MAX__=18446744073709551615UL
predefine __SIZE_TYPE__=long unsigned int
predefine __SIZE_WIDTH__=64
predefine __SSE2_MATH__=1
predefine __SSE2__=1
predefine __SSE3__=1
predefine __SSE4_1__=1
predefine __SSE_MATH__=1
predefine __SSE__=1
predefine __SSSE3__=1
predefine __STDC_NO_THREADS__=1
predefine __STDC_UTF_16__=1
predefine __STDC_UTF_32__=1
predefine __UINT16_C_SUFFIX__=
predefine __UINT16_FMTX__="hX"
predefine __UINT16_FMTo__="ho"
predefine __UINT16_FMTu__="hu"
predefine __UINT16_FMTx__="hx"
predefine __UINT16_MAX__=65535
predefine __UINT16_TYPE__=unsigned short
predefine __UINT32_C_SUFFIX__=U
predefine __UINT32_FMTX__="X"
predefine __UINT32_FMTo__="o"
predefine __UINT32_FMTu__="u"
predefine __UINT32_FMTx__="x"
predefine __UINT32_MAX__=4294967295U
predefine __UINT32_TYPE__=unsigned int
predefine __UINT64_C_SUFFIX__=ULL
predefine __UINT64_FMTX__="llX"
predefine __UINT64_FMTo__="llo"
predefine __UINT64_FMTu__="llu"
predefine __UINT64_FMTx__="llx"
predefine __UINT64_MAX__=18446744073709551615ULL
predefine __UINT64_TYPE__=long long unsigned int
predefine __UINT8_C_SUFFIX__=
predefine __UINT8_FMTX__="hhX"
predefine __UINT8_FMTo__="hho"
predefine __UINT8_FMTu__="hhu"
predefine __UINT8_FMTx__="hhx"
predefine __UINT8_MAX__=255
predefine __UINT8_TYPE__=unsigned char
predefine __UINTMAX_C_SUFFIX__=UL
predefine __UINTMAX_FMTX__="lX"
predefine __UINTMAX_FMTo__="lo"
predefine __UINTMAX_FMTu__="lu"
predefine __UINTMAX_FMTx__="lx"
predefine __UINTMAX_MAX__=18446744073709551615UL
predefine __UINTMAX_TYPE__=long unsigned int
predefine __UINTMAX_WIDTH__=64
predefine __UINTPTR_FMTX__="lX"
predefine __UINTPTR_FMTo__="lo"
predefine __UINTPTR_FMTu__="lu"
predefine __UINTPTR_FMTx__="lx"
predefine __UINTPTR_MAX__=18446744073709551615UL
predefine __UINTPTR_TYPE__=long unsigned int
predefine __UINTPTR_WIDTH__=64
predefine __UINT_FAST16_FMTX__="hX"
predefine __UINT_FAST16_FMTo__="ho"
predefine __UINT_FAST16_FMTu__="hu"
predefine __UINT_FAST16_FMTx__="hx"
predefine __UINT_FAST16_MAX__=65535
predefine __UINT_FAST16_TYPE__=unsigned short
predefine __UINT_FAST32_FMTX__="X"
predefine __UINT_FAST32_FMTo__="o"
predefine __UINT_FAST32_FMTu__="u"
predefine __UINT_FAST32_FMTx__="x"
predefine __UINT_FAST32_MAX__=4294967295U
predefine __UINT_FAST32_TYPE__=unsigned int
predefine __UINT_FAST64_FMTX__="llX"
predefine __UINT_FAST64_FMTo__="llo"
predefine __UINT_FAST64_FMTu__="llu"
predefine __UINT_FAST64_FMTx__="llx"
predefine __UINT_FAST64_MAX__=18446744073709551615ULL
predefine __UINT_FAST64_TYPE__=long long unsigned int
predefine __UINT_FAST8_FMTX__="hhX"
predefine __UINT_FAST8_FMTo__="hho"
predefine __UINT_FAST8_FMTu__="hhu"
predefine __UINT_FAST8_FMTx__="hhx"
predefine __UINT_FAST8_MAX__=255
predefine __UINT_FAST8_TYPE__=unsigned char
predefine __UINT_LEAST16_FMTX__="hX"
predefine __UINT_LEAST16_FMTo__="ho"
predefine __UINT_LEAST16_FMTu__="hu"
predefine __UINT_LEAST16_FMTx__="hx"
predefine __UINT_LEAST16_MAX__=65535
predefine __UINT_LEAST16_TYPE__=unsigned short
predefine __UINT_LEAST32_FMTX__="X"
predefine __UINT_LEAST32_FMTo__="o"
predefine __UINT_LEAST32_FMTu__="u"
predefine __UINT_LEAST32_FMTx__="x"
predefine __UINT_LEAST32_MAX__=4294967295U
predefine __UINT_LEAST32_TYPE__=unsigned int
predefine __UINT_LEAST64_FMTX__="llX"
predefine __UINT_LEAST64_FMTo__="llo"
predefine __UINT_LEAST64_FMTu__="llu"
predefine __UINT_LEAST64_FMTx__="llx"
predefine __UINT_LEAST64_MAX__=18446744073709551615ULL
predefine __UINT_LEAST64_TYPE__=long long unsigned int
predefine __UINT_LEAST8_FMTX__="hhX"
predefine __UINT_LEAST8_FMTo__="hho"
predefine __UINT_LEAST8_FMTu__="hhu"
predefine __UINT_LEAST8_FMTx__="hhx"
predefine __UINT_LEAST8_MAX__=255
predefine __UINT_LEAST8_TYPE__=unsigned char
predefine __USER_LABEL_PREFIX__=_
predefine __VERSION__="Apple LLVM 16.0.0 (clang-1600.0.26.6)"
predefine __WCHAR_MAX__=2147483647
predefine __WCHAR_TYPE__=int
predefine __WCHAR_WIDTH__=32
predefine __WINT_MAX__=2147483647
predefine __WINT_TYPE__=int
predefine __WINT_WIDTH__=32
predefine __amd64=1
predefine __amd64__=1
predefine __apple_build_version__=16000026
predefine __block=__attribute__((__blocks__(byref)))
predefine __clang__=1
predefine __clang_literal_encoding__="UTF-8"
predefine __clang_major__=16
predefine __clang_minor__=0
predefine __clang_patchlevel__=0
predefine __clang_version__="16.0.0 (clang-1600.0.26.6)"
predefine __clang_wide_literal_encoding__="UTF-32"
predefine __code_model_small__=1
predefine __core2=1
predefine __core2__=1
predefine __llvm__=1
predefine __nonnull=_Nonnull
predefine __null_unspecified=_Null_unspecified
predefine __nullable=_Nullable
predefine __pic__=2
predefine __seg_fs=__attribute__((address_space(257)))
predefine __seg_gs=__attribute__((address_space(256)))
predefine __strong=
predefine __tune_core2__=1
predefine __unsafe_unretained=
predefine __weak=__attribute__((objc_gc(weak)))
predefine __x86_64=1
predefine __x86_64__=1
prepend --gcc
prepend --clang_version
prepend 140000
prepend --gnu_version
prepend 40801
predefine __private_extern__=extern __attribute__((visibility("hidden")))
prepend --has_feature_vector
prepend 111111111111111100000000000000000000000000000000000000000000000000011
prepend --clang
prepend --target
prepend linux_x86_64

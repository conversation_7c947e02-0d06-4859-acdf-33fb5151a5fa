[T 18:42:38 79389] CodeQL CLI version 2.11.2
[T 18:42:38 79389] Initializing tracer.
[T 18:42:38 79389] Initialising tags...
[T 18:42:38 79389] ID set to 000000000001361D_0000000000000001 (parent root)
[T 18:42:38 79389] Initializing tracer.
[T 18:42:38 79389] Initialising tags...
[T 18:42:38 79389] ID set to 000000000001361D_0000000000000002 (parent root)
[T 18:42:38 79389] Warning: SEMMLE_EXEC and SEMMLE_EXECP not set. Falling back to path lookup on argv[0].
[T 18:42:38 79389] ==== Candidate to intercept: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx (canonical: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx) ====
[T 18:42:38 79389] Executing the following tracer actions:
[T 18:42:38 79389] Tracer actions:
[T 18:42:38 79389] pre_invocations(0)
[T 18:42:38 79389] post_invocations(0)
[T 18:42:38 79389] trace_languages(1): [cpp]
[T 18:42:38 79390] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/usr/bin/make.semmle.0001361D.slice.x86_64: replacing existing signature
[T 18:42:38 79389] Initializing tracer.
[T 18:42:38 79389] Initialising tags...
[T 18:42:38 79389] ID set to 000000000001361D_0000000000000003 (parent 000000000001361D_0000000000000002)
[T 18:42:38 79389] ==== Candidate to intercept: /usr/bin/make (canonical: /usr/bin/make) ====
[T 18:42:38 79389] Executing the following tracer actions:
[T 18:42:38 79389] Tracer actions:
[T 18:42:38 79389] pre_invocations(0)
[T 18:42:38 79389] post_invocations(0)
[T 18:42:38 79389] trace_languages(1): [cpp]
[T 18:42:38 79395] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.0001361D.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.0001361D.slice.arm64: replacing existing signature
[T 18:42:39 79389] Initializing tracer.
[T 18:42:39 79389] Initialising tags...
[T 18:42:39 79389] ID set to 000000000001361D_0000000000000004 (parent 000000000001361D_0000000000000003)
[T 18:42:39 79389] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/make (canonical: /Library/Developer/CommandLineTools/usr/bin/make) ====
[T 18:42:39 79389] Executing the following tracer actions:
[T 18:42:39 79389] Tracer actions:
[T 18:42:39 79389] pre_invocations(0)
[T 18:42:39 79389] post_invocations(0)
[T 18:42:39 79389] trace_languages(1): [cpp]
[T 18:42:39 79400] Attempting to switch stdout/stderr to 4...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/usr/bin/gcc.semmle.0001361D.slice.x86_64: replacing existing signature
[T 18:42:40 79403] Initializing tracer.
[T 18:42:40 79403] Initialising tags...
[T 18:42:40 79403] ID set to 000000000001362B_0000000000000001 (parent 000000000001361D_0000000000000004)
[T 18:42:40 79403] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:42:40 79403] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:42:40 79403] Executing the following tracer actions:
[T 18:42:40 79403] Tracer actions:
[T 18:42:40 79403] pre_invocations(0)
[T 18:42:40 79403] post_invocations(1)
[T 18:42:40 79403] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, code_03a7ee14.c, -o, code_03a7ee14]
[T 18:42:40 79403] trace_languages(1): [cpp]
[T 18:42:40 79406] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.0001362D.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.0001362D.slice.arm64: replacing existing signature
[T 18:42:40 79405] Initializing tracer.
[T 18:42:40 79405] Initialising tags...
[T 18:42:40 79405] ID set to 000000000001362D_0000000000000001 (parent 000000000001362B_0000000000000001)
[T 18:42:40 79405] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:40 79405] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:40 79405] Executing the following tracer actions:
[T 18:42:40 79405] Tracer actions:
[T 18:42:40 79405] pre_invocations(0)
[T 18:42:40 79405] post_invocations(1)
[T 18:42:40 79405] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, code_03a7ee14.c, -o, code_03a7ee14]
[T 18:42:40 79405] trace_languages(1): [cpp]
[T 18:42:40 79412] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/usr/bin/xcrun.semmle.00013633.slice.x86_64: replacing existing signature
[T 18:42:41 79411] Initializing tracer.
[T 18:42:41 79411] Initialising tags...
[T 18:42:41 79411] ID set to 0000000000013633_0000000000000001 (parent 000000000001362D_0000000000000001)
[T 18:42:41 79411] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:41 79411] Executing the following tracer actions:
[T 18:42:41 79411] Tracer actions:
[T 18:42:41 79411] pre_invocations(0)
[T 18:42:41 79411] post_invocations(0)
[T 18:42:41 79411] trace_languages(1): [cpp]
[T 18:42:41 79416] Attempting to switch stdout/stderr to 3...
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00013633.slice.x86_64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00013633.slice.x86_64: replacing existing signature
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00013633.slice.arm64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.00013633.slice.arm64: replacing existing signature
[T 18:42:51 79411] Initializing tracer.
[T 18:42:51 79411] Initialising tags...
[T 18:42:51 79411] ID set to 0000000000013633_0000000000000002 (parent 0000000000013633_0000000000000001)
[T 18:42:51 79411] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:51 79411] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:51 79411] Executing the following tracer actions:
[T 18:42:51 79411] Tracer actions:
[T 18:42:51 79411] pre_invocations(0)
[T 18:42:51 79411] post_invocations(1)
[T 18:42:51 79411] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, code_03a7ee14.c, -o, code_03a7ee14]
[T 18:42:51 79411] trace_languages(1): [cpp]
[T 18:42:52 79427] Initializing tracer.
[T 18:42:52 79427] Initialising tags...
[T 18:42:52 79427] ID set to 0000000000013643_0000000000000001 (parent 0000000000013633_0000000000000002)
[T 18:42:52 79427] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:52 79427] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:52 79427] Executing the following tracer actions:
[T 18:42:52 79427] Tracer actions:
[T 18:42:52 79427] pre_invocations(0)
[T 18:42:52 79427] post_invocations(1)
[T 18:42:52 79427] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -dumpdir, code_03a7ee14-, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, code_03a7ee14.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_03a7ee14-c9eebf.o, -x, c, code_03a7ee14.c]
[T 18:42:52 79427] trace_languages(1): [cpp]
[T 18:42:52 79431] Attempting to switch stdout/stderr to 4...
[T 18:42:52 79433] Attempting to switch stdout/stderr to 4...
[T 18:42:53 79431] Initializing tracer.
[T 18:42:53 79431] Initialising tags...
[T 18:42:53 79431] ID set to 0000000000013647_0000000000000001 (parent 0000000000013643_0000000000000001)
[E 18:42:53 79431] CodeQL C/C++ Extractor 2.11.2
[E 18:42:53 79431] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:42:53 79431] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -dumpdir code_03a7ee14- -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name code_03a7ee14.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon '-clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation' -fno-odr-hash-protocols '-clang-vendor-feature=+enableAggressiveVLAFolding' '-clang-vendor-feature=+revert09abecef7bbf' '-clang-vendor-feature=+thisNoAlignAttr' '-clang-vendor-feature=+thisNoNullAttr' '-clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError' -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_03a7ee14-c9eebf.o -x c code_03a7ee14.c
[T 18:42:54 79437] Initializing tracer.
[T 18:42:54 79437] Initialising tags...
[T 18:42:54 79437] ID set to 000000000001364D_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79437] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79437] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79437] Executing the following tracer actions:
[T 18:42:54 79437] Tracer actions:
[T 18:42:54 79437] pre_invocations(0)
[T 18:42:54 79437] post_invocations(1)
[T 18:42:54 79437] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 18:42:54 79437] trace_languages(1): [cpp]
[T 18:42:54 79439] Attempting to switch stdout/stderr to 7...
[T 18:42:54 79439] Initializing tracer.
[T 18:42:54 79439] Initialising tags...
[T 18:42:54 79439] ID set to 000000000001364F_0000000000000001 (parent 000000000001364D_0000000000000001)
[E 18:42:54 79439] Mimicry classification suppression detected; exiting.
[T 18:42:54 79437] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:54 79440] Initializing tracer.
[T 18:42:54 79440] Initialising tags...
[T 18:42:54 79440] ID set to 0000000000013650_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79440] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79440] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79440] Executing the following tracer actions:
[T 18:42:54 79440] Tracer actions:
[T 18:42:54 79440] pre_invocations(0)
[T 18:42:54 79440] post_invocations(1)
[T 18:42:54 79440] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 18:42:54 79440] trace_languages(1): [cpp]
[T 18:42:54 79442] Attempting to switch stdout/stderr to 7...
[T 18:42:54 79442] Initializing tracer.
[T 18:42:54 79442] Initialising tags...
[T 18:42:54 79442] ID set to 0000000000013652_0000000000000001 (parent 0000000000013650_0000000000000001)
[E 18:42:54 79442] Mimicry classification suppression detected; exiting.
[T 18:42:54 79440] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:54 79443] Initializing tracer.
[T 18:42:54 79443] Initialising tags...
[T 18:42:54 79443] ID set to 0000000000013653_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79443] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79443] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79443] Executing the following tracer actions:
[T 18:42:54 79443] Tracer actions:
[T 18:42:54 79443] pre_invocations(0)
[T 18:42:54 79443] post_invocations(1)
[T 18:42:54 79443] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -nostdsysteminc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79431_193891.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79431_193634.c]
[T 18:42:54 79443] trace_languages(1): [cpp]
[T 18:42:54 79445] Attempting to switch stdout/stderr to 7...
[T 18:42:54 79445] Initializing tracer.
[T 18:42:54 79445] Initialising tags...
[T 18:42:54 79445] ID set to 0000000000013655_0000000000000001 (parent 0000000000013653_0000000000000001)
[E 18:42:54 79445] Mimicry classification suppression detected; exiting.
[T 18:42:54 79443] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Unrecognised command line argument -dumpdir
Warning: Unrecognised command line argument -clear-ast-before-backend
Warning: Unrecognised command line argument -discard-value-names
Warning: Unrecognised command line argument -target-sdk-version=15.2
Warning: Unrecognised command line argument -tune-cpu
Warning: Unrecognised command line argument -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation
Warning: Unrecognised command line argument -clang-vendor-feature=+enableAggressiveVLAFolding
Warning: Unrecognised command line argument -clang-vendor-feature=+revert09abecef7bbf
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoAlignAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoNullAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError
[E 18:42:54 79431] Checking whether C compilation already happened.
[E 18:42:54 79431] Checking for tag c-compilation-happened
[E 18:42:54 79431] Checking CODEQL_TRACER_DB_ID 0000000000013643_0000000000000001
[E 18:42:54 79431] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79431] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79431] Unlocking DB
[E 18:42:54 79431] Unlocked DB
[E 18:42:54 79431] Looks like C compilation didn't already happen.
[E 18:42:54 79431] Checking whether C compilation has been attempted.
[E 18:42:54 79431] Checking for tag c-compilation-attempted
[E 18:42:54 79431] Checking CODEQL_TRACER_DB_ID 0000000000013643_0000000000000001
[E 18:42:54 79431] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79431] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79431] Unlocking DB
[E 18:42:54 79431] Unlocked DB
[E 18:42:54 79431] Marking C compilation as attempted.
[E 18:42:54 79431] Setting tag c-compilation-attempted
[E 18:42:54 79431] Starting from CODEQL_TRACER_DB_ID 0000000000013643_0000000000000001
[E 18:42:54 79431] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79431] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79431] Set tag for 0000000000013643_0000000000000001
[E 18:42:54 79431] Set tag for 0000000000013633_0000000000000002
[E 18:42:54 79431] Set tag for 0000000000013633_0000000000000001
[E 18:42:54 79431] Set tag for 000000000001362D_0000000000000001
[E 18:42:54 79431] Set tag for 000000000001362B_0000000000000001
[E 18:42:54 79431] Set tag for 000000000001361D_0000000000000004
[E 18:42:54 79431] Set tag for 000000000001361D_0000000000000003
[E 18:42:54 79431] Set tag for 000000000001361D_0000000000000002
[E 18:42:54 79431] Set tag for root
[E 18:42:54 79431] Unlocking DB
[E 18:42:54 79431] Unlocked DB
[E 18:42:54 79431] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:42:54 79431] Warning[extractor-c++]: In canonicalise_path: realpath failed
Excluded code_03a7ee14- because it is an object
Excluded generic because it is an object
[T 18:42:54 79446] Initializing tracer.
[T 18:42:54 79446] Initialising tags...
[T 18:42:54 79446] ID set to 0000000000013656_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79446] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79446] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79446] Executing the following tracer actions:
[T 18:42:54 79446] Tracer actions:
[T 18:42:54 79446] pre_invocations(0)
[T 18:42:54 79446] post_invocations(1)
[T 18:42:54 79446] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -v, -fsyntax-only, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79431_297225]
[T 18:42:54 79446] trace_languages(1): [cpp]
[T 18:42:54 79448] Attempting to switch stdout/stderr to 7...
[T 18:42:54 79448] Initializing tracer.
[T 18:42:54 79448] Initialising tags...
[T 18:42:54 79448] ID set to 0000000000013658_0000000000000001 (parent 0000000000013656_0000000000000001)
[E 18:42:54 79448] Mimicry classification suppression detected; exiting.
[T 18:42:54 79446] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:54 79449] Initializing tracer.
[T 18:42:54 79449] Initialising tags...
[T 18:42:54 79449] ID set to 0000000000013659_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79449] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79449] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79449] Executing the following tracer actions:
[T 18:42:54 79449] Tracer actions:
[T 18:42:54 79449] pre_invocations(0)
[T 18:42:54 79449] post_invocations(1)
[T 18:42:54 79449] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, -dM, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_5_79431_384133]
[T 18:42:54 79449] trace_languages(1): [cpp]
[T 18:42:54 79451] Attempting to switch stdout/stderr to 7...
[T 18:42:54 79451] Initializing tracer.
[T 18:42:54 79451] Initialising tags...
[T 18:42:54 79451] ID set to 000000000001365B_0000000000000001 (parent 0000000000013659_0000000000000001)
[E 18:42:54 79451] Mimicry classification suppression detected; exiting.
[T 18:42:54 79449] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Apple version 160000 of clang is too new; mapping it to 14.0.0.
[T 18:42:54 79452] Initializing tracer.
[T 18:42:54 79452] Initialising tags...
[T 18:42:54 79452] ID set to 000000000001365C_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79452] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79452] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79452] Executing the following tracer actions:
[T 18:42:54 79452] Tracer actions:
[T 18:42:54 79452] pre_invocations(0)
[T 18:42:54 79452] post_invocations(1)
[T 18:42:54 79452] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_6_79431_466220]
[T 18:42:54 79452] trace_languages(1): [cpp]
[T 18:42:54 79454] Attempting to switch stdout/stderr to 7...
[T 18:42:54 79454] Initializing tracer.
[T 18:42:54 79454] Initialising tags...
[T 18:42:54 79454] ID set to 000000000001365E_0000000000000001 (parent 000000000001365C_0000000000000001)
[E 18:42:54 79454] Mimicry classification suppression detected; exiting.
[T 18:42:54 79452] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:54 79455] Initializing tracer.
[T 18:42:54 79455] Initialising tags...
[T 18:42:54 79455] ID set to 000000000001365F_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79455] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79455] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79455] Executing the following tracer actions:
[T 18:42:54 79455] Tracer actions:
[T 18:42:54 79455] pre_invocations(0)
[T 18:42:54 79455] post_invocations(1)
[T 18:42:54 79455] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c-header, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_7_79431_547496.h.gch, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_7_79431_547496.h]
[T 18:42:54 79455] trace_languages(1): [cpp]
[T 18:42:54 79457] Attempting to switch stdout/stderr to 7...
[T 18:42:54 79457] Initializing tracer.
[T 18:42:54 79457] Initialising tags...
[T 18:42:54 79457] ID set to 0000000000013661_0000000000000001 (parent 000000000001365F_0000000000000001)
[E 18:42:54 79457] Mimicry classification suppression detected; exiting.
[T 18:42:54 79455] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:54 79458] Initializing tracer.
[T 18:42:54 79458] Initialising tags...
[T 18:42:54 79458] ID set to 0000000000013662_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79458] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79458] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79458] Executing the following tracer actions:
[T 18:42:54 79458] Tracer actions:
[T 18:42:54 79458] pre_invocations(0)
[T 18:42:54 79458] post_invocations(1)
[T 18:42:54 79458] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -fno-color-diagnostics, --help]
[T 18:42:54 79458] trace_languages(1): [cpp]
[T 18:42:54 79460] Attempting to switch stdout/stderr to 7...
[T 18:42:54 79460] Initializing tracer.
[T 18:42:54 79460] Initialising tags...
[T 18:42:54 79460] ID set to 0000000000013664_0000000000000001 (parent 0000000000013662_0000000000000001)
[E 18:42:54 79460] CodeQL C/C++ Extractor 2.11.2
[E 18:42:54 79460] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:42:54 79460] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -mrelax-all -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -fno-odr-hash-protocols -x c -fno-color-diagnostics --help
[E 18:42:54 79460] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:42:54 79460] Checking whether C compilation already happened.
[E 18:42:54 79460] Checking for tag c-compilation-happened
[E 18:42:54 79460] Checking CODEQL_TRACER_DB_ID 0000000000013662_0000000000000001
[E 18:42:54 79460] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79460] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79460] Unlocking DB
[E 18:42:54 79460] Unlocked DB
[E 18:42:54 79460] Looks like C compilation didn't already happen.
[E 18:42:54 79460] Checking whether C compilation has been attempted.
[E 18:42:54 79460] Checking for tag c-compilation-attempted
[E 18:42:54 79460] Checking CODEQL_TRACER_DB_ID 0000000000013662_0000000000000001
[E 18:42:54 79460] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79460] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79460] Unlocking DB
[E 18:42:54 79460] Unlocked DB
[E 18:42:54 79460] Marking C compilation as attempted.
[E 18:42:54 79460] Setting tag c-compilation-attempted
[E 18:42:54 79460] Starting from CODEQL_TRACER_DB_ID 0000000000013662_0000000000000001
[E 18:42:54 79460] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79460] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:54 79460] Set tag for 0000000000013662_0000000000000001
[E 18:42:54 79460] Set tag for 0000000000013647_0000000000000001
[E 18:42:54 79460] Set tag for 0000000000013643_0000000000000001
[E 18:42:54 79460] Set tag for 0000000000013633_0000000000000002
[E 18:42:54 79460] Set tag for 0000000000013633_0000000000000001
[E 18:42:54 79460] Set tag for 000000000001362D_0000000000000001
[E 18:42:54 79460] Set tag for 000000000001362B_0000000000000001
[E 18:42:54 79460] Set tag for 000000000001361D_0000000000000004
[E 18:42:54 79460] Set tag for 000000000001361D_0000000000000003
[E 18:42:54 79460] Set tag for 000000000001361D_0000000000000002
[E 18:42:54 79460] Set tag for root
[E 18:42:54 79460] Unlocking DB
[E 18:42:54 79460] Unlocked DB
[E 18:42:54 79460] 0 file groups; exiting.
[T 18:42:54 79458] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:54 79461] Initializing tracer.
[T 18:42:54 79461] Initialising tags...
[T 18:42:54 79461] ID set to 0000000000013665_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:54 79461] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:54 79461] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:54 79461] Executing the following tracer actions:
[T 18:42:54 79461] Tracer actions:
[T 18:42:54 79461] pre_invocations(0)
[T 18:42:54 79461] post_invocations(1)
[T 18:42:54 79461] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_7_79431_547496.h.gch, -ast-dump, -]
[T 18:42:54 79461] trace_languages(1): [cpp]
[T 18:42:55 79472] Attempting to switch stdout/stderr to 7...
[T 18:42:55 79472] Initializing tracer.
[T 18:42:55 79472] Initialising tags...
[T 18:42:55 79472] ID set to 0000000000013670_0000000000000001 (parent 0000000000013665_0000000000000001)
[E 18:42:55 79472] Mimicry classification suppression detected; exiting.
[T 18:42:55 79461] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, int *)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, unsigned short)'
Warning: Could not parse function type 'float (__bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'void (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(int)))) int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(float)))) float, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(short)))) short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(short)))) short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(short)))) short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__fp16 (__fp16, int)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Failed to find information about possible built-in __VA_OPT__
Warning: Failed to find information about possible built-in __blocks__
Warning: Failed to find information about possible built-in __builtin___fprintf_chk
Warning: Failed to find information about possible built-in __builtin___vfprintf_chk
Warning: Failed to find information about possible built-in __builtin_fprintf
Warning: Failed to find information about possible built-in __builtin_fscanf
Warning: Failed to find information about possible built-in __builtin_vfprintf
Warning: Failed to find information about possible built-in __builtin_vfscanf
Warning: Failed to find information about possible built-in __sigsetjmp
Warning: Failed to find information about possible built-in _longjmp
Warning: Failed to find information about possible built-in _setjmp
Warning: Ignored 11 possible built-ins
Warning: Throwing away all 2906 builtins because of 229 bad parses.
[T 18:42:55 79473] Initializing tracer.
[T 18:42:55 79473] Initialising tags...
[T 18:42:55 79473] ID set to 0000000000013671_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:55 79473] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:55 79473] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:55 79473] Executing the following tracer actions:
[T 18:42:55 79473] Tracer actions:
[T 18:42:55 79473] pre_invocations(0)
[T 18:42:55 79473] post_invocations(1)
[T 18:42:55 79473] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_9_79431_77176.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_8_79431_76851.c]
[T 18:42:55 79473] trace_languages(1): [cpp]
[T 18:42:55 79475] Attempting to switch stdout/stderr to 7...
[T 18:42:55 79475] Initializing tracer.
[T 18:42:55 79475] Initialising tags...
[T 18:42:55 79475] ID set to 0000000000013673_0000000000000001 (parent 0000000000013671_0000000000000001)
[E 18:42:55 79475] Mimicry classification suppression detected; exiting.
[T 18:42:55 79473] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:55 79476] Initializing tracer.
[T 18:42:55 79476] Initialising tags...
[T 18:42:55 79476] ID set to 0000000000013674_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:55 79476] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:55 79476] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:55 79476] Executing the following tracer actions:
[T 18:42:55 79476] Tracer actions:
[T 18:42:55 79476] pre_invocations(0)
[T 18:42:55 79476] post_invocations(1)
[T 18:42:55 79476] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_11_79431_168094.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_10_79431_167836.c]
[T 18:42:55 79476] trace_languages(1): [cpp]
[T 18:42:55 79478] Attempting to switch stdout/stderr to 7...
[T 18:42:55 79478] Initializing tracer.
[T 18:42:55 79478] Initialising tags...
[T 18:42:55 79478] ID set to 0000000000013676_0000000000000001 (parent 0000000000013674_0000000000000001)
[E 18:42:55 79478] Mimicry classification suppression detected; exiting.
[T 18:42:55 79476] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:55 79479] Initializing tracer.
[T 18:42:55 79479] Initialising tags...
[T 18:42:55 79479] ID set to 0000000000013677_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:55 79479] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:55 79479] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:55 79479] Executing the following tracer actions:
[T 18:42:55 79479] Tracer actions:
[T 18:42:55 79479] pre_invocations(0)
[T 18:42:55 79479] post_invocations(1)
[T 18:42:55 79479] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_13_79431_257338.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_12_79431_257092.c]
[T 18:42:55 79479] trace_languages(1): [cpp]
[T 18:42:55 79481] Attempting to switch stdout/stderr to 7...
[T 18:42:55 79481] Initializing tracer.
[T 18:42:55 79481] Initialising tags...
[T 18:42:55 79481] ID set to 0000000000013679_0000000000000001 (parent 0000000000013677_0000000000000001)
[E 18:42:55 79481] Mimicry classification suppression detected; exiting.
[T 18:42:55 79479] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:55 79482] Initializing tracer.
[T 18:42:55 79482] Initialising tags...
[T 18:42:55 79482] ID set to 000000000001367A_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:55 79482] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:55 79482] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:55 79482] Executing the following tracer actions:
[T 18:42:55 79482] Tracer actions:
[T 18:42:55 79482] pre_invocations(0)
[T 18:42:55 79482] post_invocations(1)
[T 18:42:55 79482] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_15_79431_361510.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_14_79431_361216.c]
[T 18:42:55 79482] trace_languages(1): [cpp]
[T 18:42:55 79484] Attempting to switch stdout/stderr to 7...
[T 18:42:55 79484] Initializing tracer.
[T 18:42:55 79484] Initialising tags...
[T 18:42:55 79484] ID set to 000000000001367C_0000000000000001 (parent 000000000001367A_0000000000000001)
[E 18:42:55 79484] Mimicry classification suppression detected; exiting.
[T 18:42:55 79482] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:55 79485] Initializing tracer.
[T 18:42:55 79485] Initialising tags...
[T 18:42:55 79485] ID set to 000000000001367D_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:55 79485] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:55 79485] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:55 79485] Executing the following tracer actions:
[T 18:42:55 79485] Tracer actions:
[T 18:42:55 79485] pre_invocations(0)
[T 18:42:55 79485] post_invocations(1)
[T 18:42:55 79485] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_17_79431_454276.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_16_79431_454013.c]
[T 18:42:55 79485] trace_languages(1): [cpp]
[T 18:42:55 79487] Attempting to switch stdout/stderr to 7...
[T 18:42:55 79487] Initializing tracer.
[T 18:42:55 79487] Initialising tags...
[T 18:42:55 79487] ID set to 000000000001367F_0000000000000001 (parent 000000000001367D_0000000000000001)
[E 18:42:55 79487] Mimicry classification suppression detected; exiting.
[T 18:42:55 79485] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:55 79488] Initializing tracer.
[T 18:42:55 79488] Initialising tags...
[T 18:42:55 79488] ID set to 0000000000013680_0000000000000001 (parent 0000000000013647_0000000000000001)
[T 18:42:55 79488] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:55 79488] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:55 79488] Executing the following tracer actions:
[T 18:42:55 79488] Tracer actions:
[T 18:42:55 79488] pre_invocations(0)
[T 18:42:55 79488] post_invocations(1)
[T 18:42:55 79488] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_19_79431_545147.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_18_79431_544910.c]
[T 18:42:55 79488] trace_languages(1): [cpp]
[T 18:42:55 79490] Attempting to switch stdout/stderr to 7...
[T 18:42:55 79490] Initializing tracer.
[T 18:42:55 79490] Initialising tags...
[T 18:42:55 79490] ID set to 0000000000013682_0000000000000001 (parent 0000000000013680_0000000000000001)
[E 18:42:55 79490] Mimicry classification suppression detected; exiting.
[T 18:42:55 79488] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 18:42:55 79431] Processed command line: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --trapfolder '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/trap/cpp' --src_archive '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src' --mimic_config '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/compiler_mimic_cache/a1592c9a78e1' --object_filename /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_03a7ee14-c9eebf.o -w --error_limit 1000 --disable_system_macros --variadic_macros --gcc --clang_version 140000 --gnu_version 40801 --has_feature_vector 111111111111111100000000000000000000000000000000000000000000000000011 --clang --target linux_x86_64 -D_LP64=1 -D__APPLE_CC__=6000 -D__APPLE__=1 -D__ATOMIC_ACQUIRE=2 -D__ATOMIC_ACQ_REL=4 -D__ATOMIC_CONSUME=1 -D__ATOMIC_RELAXED=0 -D__ATOMIC_RELEASE=3 -D__ATOMIC_SEQ_CST=5 -D__BIGGEST_ALIGNMENT__=16 -D__BITINT_MAXWIDTH__=8388608 -D__BLOCKS__=1 -D__BOOL_WIDTH__=8 -D__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__ -D__CHAR_BIT__=8 -D__CLANG_ATOMIC_BOOL_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR_LOCK_FREE=2 -D__CLANG_ATOMIC_INT_LOCK_FREE=2 -D__CLANG_ATOMIC_LLONG_LOCK_FREE=2 -D__CLANG_ATOMIC_LONG_LOCK_FREE=2 -D__CLANG_ATOMIC_POINTER_LOCK_FREE=2 -D__CLANG_ATOMIC_SHORT_LOCK_FREE=2 -D__CLANG_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__CONSTANT_CFSTRINGS__=1 -D__DBL_DECIMAL_DIG__=17 -D__DBL_DENORM_MIN__=4.9406564584124654e-324 -D__DBL_DIG__=15 -D__DBL_EPSILON__=2.2204460492503131e-16 -D__DBL_HAS_DENORM__=1 -D__DBL_HAS_INFINITY__=1 -D__DBL_HAS_QUIET_NAN__=1 -D__DBL_MANT_DIG__=53 -D__DBL_MAX_10_EXP__=308 -D__DBL_MAX_EXP__=1024 '-D__DBL_MAX__=1.7976931348623157e+308' '-D__DBL_MIN_10_EXP__=(-307)' '-D__DBL_MIN_EXP__=(-1021)' -D__DBL_MIN__=2.2250738585072014e-308 -D__DECIMAL_DIG__=__LDBL_DECIMAL_DIG__ -D__DYNAMIC__=1 -D__ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__=150000 -D__ENVIRONMENT_OS_VERSION_MIN_REQUIRED__=150000 -D__FINITE_MATH_ONLY__=0 -D__FLT16_DECIMAL_DIG__=5 -D__FLT16_DENORM_MIN__=5.9604644775390625e-8F16 -D__FLT16_DIG__=3 -D__FLT16_EPSILON__=9.765625e-4F16 -D__FLT16_HAS_DENORM__=1 -D__FLT16_HAS_INFINITY__=1 -D__FLT16_HAS_QUIET_NAN__=1 -D__FLT16_MANT_DIG__=11 -D__FLT16_MAX_10_EXP__=4 -D__FLT16_MAX_EXP__=16 '-D__FLT16_MAX__=6.5504e+4F16' '-D__FLT16_MIN_10_EXP__=(-4)' '-D__FLT16_MIN_EXP__=(-13)' -D__FLT16_MIN__=6.103515625e-5F16 -D__FLT_DECIMAL_DIG__=9 -D__FLT_DENORM_MIN__=1.40129846e-45F -D__FLT_DIG__=6 -D__FLT_EPSILON__=1.19209290e-7F -D__FLT_HAS_DENORM__=1 -D__FLT_HAS_INFINITY__=1 -D__FLT_HAS_QUIET_NAN__=1 -D__FLT_MANT_DIG__=24 -D__FLT_MAX_10_EXP__=38 -D__FLT_MAX_EXP__=128 '-D__FLT_MAX__=3.40282347e+38F' '-D__FLT_MIN_10_EXP__=(-37)' '-D__FLT_MIN_EXP__=(-125)' -D__FLT_MIN__=1.17549435e-38F -D__FLT_RADIX__=2 -D__FPCLASS_NEGINF=0x0004 -D__FPCLASS_NEGNORMAL=0x0008 -D__FPCLASS_NEGSUBNORMAL=0x0010 -D__FPCLASS_NEGZERO=0x0020 -D__FPCLASS_POSINF=0x0200 -D__FPCLASS_POSNORMAL=0x0100 -D__FPCLASS_POSSUBNORMAL=0x0080 -D__FPCLASS_POSZERO=0x0040 -D__FPCLASS_QNAN=0x0002 -D__FPCLASS_SNAN=0x0001 -D__FXSR__=1 -D__GCC_ASM_FLAG_OUTPUTS__=1 -D__GCC_ATOMIC_BOOL_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR_LOCK_FREE=2 -D__GCC_ATOMIC_INT_LOCK_FREE=2 -D__GCC_ATOMIC_LLONG_LOCK_FREE=2 -D__GCC_ATOMIC_LONG_LOCK_FREE=2 -D__GCC_ATOMIC_POINTER_LOCK_FREE=2 -D__GCC_ATOMIC_SHORT_LOCK_FREE=2 -D__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1 -D__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -D__GNUC__=4 -D__GXX_ABI_VERSION=1002 -D__INT16_C_SUFFIX__= '-D__INT16_FMTd__="hd"' '-D__INT16_FMTi__="hi"' -D__INT16_MAX__=32767 -D__INT16_TYPE__=short -D__INT32_C_SUFFIX__= '-D__INT32_FMTd__="d"' '-D__INT32_FMTi__="i"' -D__INT32_MAX__=2147483647 -D__INT32_TYPE__=int -D__INT64_C_SUFFIX__=LL '-D__INT64_FMTd__="lld"' '-D__INT64_FMTi__="lli"' -D__INT64_MAX__=9223372036854775807LL '-D__INT64_TYPE__=long long int' -D__INT8_C_SUFFIX__= '-D__INT8_FMTd__="hhd"' '-D__INT8_FMTi__="hhi"' -D__INT8_MAX__=127 '-D__INT8_TYPE__=signed char' -D__INTMAX_C_SUFFIX__=L '-D__INTMAX_FMTd__="ld"' '-D__INTMAX_FMTi__="li"' -D__INTMAX_MAX__=9223372036854775807L '-D__INTMAX_TYPE__=long int' -D__INTMAX_WIDTH__=64 '-D__INTPTR_FMTd__="ld"' '-D__INTPTR_FMTi__="li"' -D__INTPTR_MAX__=9223372036854775807L '-D__INTPTR_TYPE__=long int' -D__INTPTR_WIDTH__=64 '-D__INT_FAST16_FMTd__="hd"' '-D__INT_FAST16_FMTi__="hi"' -D__INT_FAST16_MAX__=32767 -D__INT_FAST16_TYPE__=short -D__INT_FAST16_WIDTH__=16 '-D__INT_FAST32_FMTd__="d"' '-D__INT_FAST32_FMTi__="i"' -D__INT_FAST32_MAX__=2147483647 -D__INT_FAST32_TYPE__=int -D__INT_FAST32_WIDTH__=32 '-D__INT_FAST64_FMTd__="lld"' '-D__INT_FAST64_FMTi__="lli"' -D__INT_FAST64_MAX__=9223372036854775807LL '-D__INT_FAST64_TYPE__=long long int' -D__INT_FAST64_WIDTH__=64 '-D__INT_FAST8_FMTd__="hhd"' '-D__INT_FAST8_FMTi__="hhi"' -D__INT_FAST8_MAX__=127 '-D__INT_FAST8_TYPE__=signed char' -D__INT_FAST8_WIDTH__=8 '-D__INT_LEAST16_FMTd__="hd"' '-D__INT_LEAST16_FMTi__="hi"' -D__INT_LEAST16_MAX__=32767 -D__INT_LEAST16_TYPE__=short -D__INT_LEAST16_WIDTH__=16 '-D__INT_LEAST32_FMTd__="d"' '-D__INT_LEAST32_FMTi__="i"' -D__INT_LEAST32_MAX__=2147483647 -D__INT_LEAST32_TYPE__=int -D__INT_LEAST32_WIDTH__=32 '-D__INT_LEAST64_FMTd__="lld"' '-D__INT_LEAST64_FMTi__="lli"' -D__INT_LEAST64_MAX__=9223372036854775807LL '-D__INT_LEAST64_TYPE__=long long int' -D__INT_LEAST64_WIDTH__=64 '-D__INT_LEAST8_FMTd__="hhd"' '-D__INT_LEAST8_FMTi__="hhi"' -D__INT_LEAST8_MAX__=127 '-D__INT_LEAST8_TYPE__=signed char' -D__INT_LEAST8_WIDTH__=8 -D__INT_MAX__=2147483647 -D__INT_WIDTH__=32 -D__LAHF_SAHF__=1 -D__LDBL_DECIMAL_DIG__=21 -D__LDBL_DENORM_MIN__=3.64519953188247460253e-4951L -D__LDBL_DIG__=18 -D__LDBL_EPSILON__=1.08420217248550443401e-19L -D__LDBL_HAS_DENORM__=1 -D__LDBL_HAS_INFINITY__=1 -D__LDBL_HAS_QUIET_NAN__=1 -D__LDBL_MANT_DIG__=64 -D__LDBL_MAX_10_EXP__=4932 -D__LDBL_MAX_EXP__=16384 '-D__LDBL_MAX__=1.18973149535723176502e+4932L' '-D__LDBL_MIN_10_EXP__=(-4931)' '-D__LDBL_MIN_EXP__=(-16381)' -D__LDBL_MIN__=3.36210314311209350626e-4932L -D__LITTLE_ENDIAN__=1 -D__LLONG_WIDTH__=64 -D__LONG_LONG_MAX__=9223372036854775807LL -D__LONG_MAX__=9223372036854775807L -D__LONG_WIDTH__=64 -D__LP64__=1 -D__MACH__=1 -D__MMX__=1 -D__NO_INLINE__=1 -D__NO_MATH_ERRNO__=1 -D__NO_MATH_INLINES=1 -D__OBJC_BOOL_IS_BOOL=0 -D__OPENCL_MEMORY_SCOPE_ALL_SVM_DEVICES=3 -D__OPENCL_MEMORY_SCOPE_DEVICE=2 -D__OPENCL_MEMORY_SCOPE_SUB_GROUP=4 -D__OPENCL_MEMORY_SCOPE_WORK_GROUP=1 -D__OPENCL_MEMORY_SCOPE_WORK_ITEM=0 -D__ORDER_BIG_ENDIAN__=4321 -D__ORDER_LITTLE_ENDIAN__=1234 -D__ORDER_PDP_ENDIAN__=3412 -D__PIC__=2 -D__POINTER_WIDTH__=64 -D__PRAGMA_REDEFINE_EXTNAME=1 '-D__PTRDIFF_FMTd__="ld"' '-D__PTRDIFF_FMTi__="li"' -D__PTRDIFF_MAX__=9223372036854775807L '-D__PTRDIFF_TYPE__=long int' -D__PTRDIFF_WIDTH__=64 -D__REGISTER_PREFIX__= -D__SCHAR_MAX__=127 -D__SEG_FS=1 -D__SEG_GS=1 -D__SHRT_MAX__=32767 -D__SHRT_WIDTH__=16 -D__SIG_ATOMIC_MAX__=2147483647 -D__SIG_ATOMIC_WIDTH__=32 -D__SIZEOF_DOUBLE__=8 -D__SIZEOF_FLOAT__=4 -D__SIZEOF_INT128__=16 -D__SIZEOF_INT__=4 -D__SIZEOF_LONG_DOUBLE__=16 -D__SIZEOF_LONG_LONG__=8 -D__SIZEOF_LONG__=8 -D__SIZEOF_POINTER__=8 -D__SIZEOF_PTRDIFF_T__=8 -D__SIZEOF_SHORT__=2 -D__SIZEOF_SIZE_T__=8 -D__SIZEOF_WCHAR_T__=4 -D__SIZEOF_WINT_T__=4 '-D__SIZE_FMTX__="lX"' '-D__SIZE_FMTo__="lo"' '-D__SIZE_FMTu__="lu"' '-D__SIZE_FMTx__="lx"' -D__SIZE_MAX__=18446744073709551615UL '-D__SIZE_TYPE__=long unsigned int' -D__SIZE_WIDTH__=64 -D__SSE2_MATH__=1 -D__SSE2__=1 -D__SSE3__=1 -D__SSE4_1__=1 -D__SSE_MATH__=1 -D__SSE__=1 -D__SSSE3__=1 -D__STDC_NO_THREADS__=1 -D__STDC_UTF_16__=1 -D__STDC_UTF_32__=1 -D__UINT16_C_SUFFIX__= '-D__UINT16_FMTX__="hX"' '-D__UINT16_FMTo__="ho"' '-D__UINT16_FMTu__="hu"' '-D__UINT16_FMTx__="hx"' -D__UINT16_MAX__=65535 '-D__UINT16_TYPE__=unsigned short' -D__UINT32_C_SUFFIX__=U '-D__UINT32_FMTX__="X"' '-D__UINT32_FMTo__="o"' '-D__UINT32_FMTu__="u"' '-D__UINT32_FMTx__="x"' -D__UINT32_MAX__=4294967295U '-D__UINT32_TYPE__=unsigned int' -D__UINT64_C_SUFFIX__=ULL '-D__UINT64_FMTX__="llX"' '-D__UINT64_FMTo__="llo"' '-D__UINT64_FMTu__="llu"' '-D__UINT64_FMTx__="llx"' -D__UINT64_MAX__=18446744073709551615ULL '-D__UINT64_TYPE__=long long unsigned int' -D__UINT8_C_SUFFIX__= '-D__UINT8_FMTX__="hhX"' '-D__UINT8_FMTo__="hho"' '-D__UINT8_FMTu__="hhu"' '-D__UINT8_FMTx__="hhx"' -D__UINT8_MAX__=255 '-D__UINT8_TYPE__=unsigned char' -D__UINTMAX_C_SUFFIX__=UL '-D__UINTMAX_FMTX__="lX"' '-D__UINTMAX_FMTo__="lo"' '-D__UINTMAX_FMTu__="lu"' '-D__UINTMAX_FMTx__="lx"' -D__UINTMAX_MAX__=18446744073709551615UL '-D__UINTMAX_TYPE__=long unsigned int' -D__UINTMAX_WIDTH__=64 '-D__UINTPTR_FMTX__="lX"' '-D__UINTPTR_FMTo__="lo"' '-D__UINTPTR_FMTu__="lu"' '-D__UINTPTR_FMTx__="lx"' -D__UINTPTR_MAX__=18446744073709551615UL '-D__UINTPTR_TYPE__=long unsigned int' -D__UINTPTR_WIDTH__=64 '-D__UINT_FAST16_FMTX__="hX"' '-D__UINT_FAST16_FMTo__="ho"' '-D__UINT_FAST16_FMTu__="hu"' '-D__UINT_FAST16_FMTx__="hx"' -D__UINT_FAST16_MAX__=65535 '-D__UINT_FAST16_TYPE__=unsigned short' '-D__UINT_FAST32_FMTX__="X"' '-D__UINT_FAST32_FMTo__="o"' '-D__UINT_FAST32_FMTu__="u"' '-D__UINT_FAST32_FMTx__="x"' -D__UINT_FAST32_MAX__=4294967295U '-D__UINT_FAST32_TYPE__=unsigned int' '-D__UINT_FAST64_FMTX__="llX"' '-D__UINT_FAST64_FMTo__="llo"' '-D__UINT_FAST64_FMTu__="llu"' '-D__UINT_FAST64_FMTx__="llx"' -D__UINT_FAST64_MAX__=18446744073709551615ULL '-D__UINT_FAST64_TYPE__=long long unsigned int' '-D__UINT_FAST8_FMTX__="hhX"' '-D__UINT_FAST8_FMTo__="hho"' '-D__UINT_FAST8_FMTu__="hhu"' '-D__UINT_FAST8_FMTx__="hhx"' -D__UINT_FAST8_MAX__=255 '-D__UINT_FAST8_TYPE__=unsigned char' '-D__UINT_LEAST16_FMTX__="hX"' '-D__UINT_LEAST16_FMTo__="ho"' '-D__UINT_LEAST16_FMTu__="hu"' '-D__UINT_LEAST16_FMTx__="hx"' -D__UINT_LEAST16_MAX__=65535 '-D__UINT_LEAST16_TYPE__=unsigned short' '-D__UINT_LEAST32_FMTX__="X"' '-D__UINT_LEAST32_FMTo__="o"' '-D__UINT_LEAST32_FMTu__="u"' '-D__UINT_LEAST32_FMTx__="x"' -D__UINT_LEAST32_MAX__=4294967295U '-D__UINT_LEAST32_TYPE__=unsigned int' '-D__UINT_LEAST64_FMTX__="llX"' '-D__UINT_LEAST64_FMTo__="llo"' '-D__UINT_LEAST64_FMTu__="llu"' '-D__UINT_LEAST64_FMTx__="llx"' -D__UINT_LEAST64_MAX__=18446744073709551615ULL '-D__UINT_LEAST64_TYPE__=long long unsigned int' '-D__UINT_LEAST8_FMTX__="hhX"' '-D__UINT_LEAST8_FMTo__="hho"' '-D__UINT_LEAST8_FMTu__="hhu"' '-D__UINT_LEAST8_FMTx__="hhx"' -D__UINT_LEAST8_MAX__=255 '-D__UINT_LEAST8_TYPE__=unsigned char' -D__USER_LABEL_PREFIX__=_ '-D__VERSION__="Apple LLVM 16.0.0 (clang-1600.0.26.6)"' -D__WCHAR_MAX__=2147483647 -D__WCHAR_TYPE__=int -D__WCHAR_WIDTH__=32 -D__WINT_MAX__=2147483647 -D__WINT_TYPE__=int -D__WINT_WIDTH__=32 -D__amd64=1 -D__amd64__=1 -D__apple_build_version__=16000026 '-D__block=__attribute__((__blocks__(byref)))' -D__clang__=1 '-D__clang_literal_encoding__="UTF-8"' -D__clang_major__=16 -D__clang_minor__=0 -D__clang_patchlevel__=0 '-D__clang_version__="16.0.0 (clang-1600.0.26.6)"' '-D__clang_wide_literal_encoding__="UTF-32"' -D__code_model_small__=1 -D__core2=1 -D__core2__=1 -D__llvm__=1 -D__nonnull=_Nonnull -D__null_unspecified=_Null_unspecified -D__nullable=_Nullable -D__pic__=2 '-D__seg_fs=__attribute__((address_space(257)))' '-D__seg_gs=__attribute__((address_space(256)))' -D__strong= -D__tune_core2__=1 -D__unsafe_unretained= '-D__weak=__attribute__((objc_gc(weak)))' -D__x86_64=1 -D__x86_64__=1 '-D__private_extern__=extern __attribute__((visibility("hidden")))' --isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include --blocks -D__GCC_HAVE_DWARF2_CFI_ASM=1 -I/usr/local/include -I/Library/Developer/CommandLineTools/usr/lib/clang/16/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -I/Library/Developer/CommandLineTools/usr/include -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks -- code_03a7ee14.c
[E 18:42:55 79431] Starting compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/trap/cpp/compilations/16/20969050_0.trap.br
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_03a7ee14.c
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdio.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdio.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/cdefs.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_symbol_aliasing.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_posix_availability.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/Availability.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityVersions.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternal.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternalLegacy.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_types.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_types.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_types.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_va_list.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/types.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/types.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int8_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int16_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int32_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int64_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int8_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int16_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int32_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int64_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_intptr_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uintptr_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/stdio.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_printf.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_seek_set.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_ctermid.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_off_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ssize_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_stdio.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_common.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdlib.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdlib.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/wait.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_pid_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_id_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/signal.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/appleapiopts.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/signal.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/signal.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_mcontext.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_mcontext.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/machine/_structs.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/i386/_structs.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_attr_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigaltstack.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ucontext.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigset_t.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uid_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/resource.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/usr/lib/clang/16/include/stdint.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdint.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint8_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint16_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint32_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint64_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_intmax_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uintmax_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_timeval.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/endian.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/endian.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_endian.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_endian.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_endian.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/__endian.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/_OSByteOrder.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/i386/_OSByteOrder.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/alloca.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ct_rune_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rune_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_wchar_t.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc_type.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_ptrcheck.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_abort.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_dev_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_mode_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/string.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_string.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rsize_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_errno_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_strings.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_strings.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_string.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/socket.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/types.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_char.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_short.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_caddr_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_blkcnt_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_blksize_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_gid_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_in_addr_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_in_port_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ino_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ino64_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_key_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_nlink_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_clock_t.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_time_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_useconds_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_suseconds_t.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rsize_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fd_def.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fd_setsize.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fd_set.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fd_clr.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fd_zero.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fd_isset.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fd_copy.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_cond_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_condattr_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_mutex_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_mutexattr_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_once_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_rwlock_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_rwlockattr_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_key_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fsblkcnt_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_fsfilcnt_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/constrained_ctypes.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_param.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_param.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/net/net_kev.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sa_family_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_socklen_t.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_iovec_t.h
[E 18:42:55 79431] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/netinet/in.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/netinet6/in6.h
[E 18:42:55 79431] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/arpa/inet.h
At end of source: error: expected a "}"
"code_03a7ee14.c", line 30: note: to match this "{"
  int main() {
             ^

[E 18:42:55 79431] Warning[extractor-c++]: In construct_message: At end of source: error: expected a "}"
"code_03a7ee14.c", line 30: note: to match this "{"
  int main() {
             ^


At end of source: error: expected a "}"
"code_03a7ee14.c", line 22: note: to match this "{"
      if (!file) {
                 ^

[E 18:42:55 79431] Warning[extractor-c++]: In construct_message: At end of source: error: expected a "}"
"code_03a7ee14.c", line 22: note: to match this "{"
      if (!file) {
                 ^


At end of source: error: expected a "}"
"code_03a7ee14.c", line 18: note: to match this "{"
      if (connect(sockfd, (struct sockaddr*) &serv_addr, sizeof(serv_addr)) == -1) {
                                                                                   ^

[E 18:42:55 79431] Warning[extractor-c++]: In construct_message: At end of source: error: expected a "}"
"code_03a7ee14.c", line 18: note: to match this "{"
      if (connect(sockfd, (struct sockaddr*) &serv_addr, sizeof(serv_addr)) == -1) {
                                                                                   ^


At end of source: error: expected a "}"
"code_03a7ee14.c", line 8: note: to match this "{"
  void send_data_over_network(const char* server_ip, const char* file_path) {
                                                                            ^

[E 18:42:55 79431] Warning[extractor-c++]: In construct_message: At end of source: error: expected a "}"
"code_03a7ee14.c", line 8: note: to match this "{"
  void send_data_over_network(const char* server_ip, const char* file_path) {
                                                                            ^


[E 18:42:55 79431] Creating trap tarball /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_03a7ee14.c.7d234792_0.trap.tar.br
[E 18:42:55 79431] Emitting trap files for code_03a7ee14.c
[E 18:42:55 79431] Opening existencedb in /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/existencedb/db
[E 18:42:55 79431] Wrote 142 files to /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_03a7ee14.c.7d234792_0.trap.tar.br
4 errors detected in the compilation of "code_03a7ee14.c".
[E 18:42:55 79431] Finished compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/trap/cpp/compilations/16/20969050_0.trap.br
[E 18:42:55 79431] Marking C compilation as happened.
[E 18:42:55 79431] Setting tag c-compilation-happened
[E 18:42:55 79431] Starting from CODEQL_TRACER_DB_ID 0000000000013643_0000000000000001
[E 18:42:55 79431] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:55 79431] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:55 79431] Set tag for 0000000000013643_0000000000000001
[E 18:42:55 79431] Set tag for 0000000000013633_0000000000000002
[E 18:42:55 79431] Set tag for 0000000000013633_0000000000000001
[E 18:42:55 79431] Set tag for 000000000001362D_0000000000000001
[E 18:42:55 79431] Set tag for 000000000001362B_0000000000000001
[E 18:42:55 79431] Set tag for 000000000001361D_0000000000000004
[E 18:42:55 79431] Set tag for 000000000001361D_0000000000000003
[E 18:42:55 79431] Set tag for 000000000001361D_0000000000000002
[E 18:42:55 79431] Set tag for root
[E 18:42:55 79431] Unlocking DB
[E 18:42:55 79431] Unlocked DB
[T 18:42:55 79427] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 1.
[T 18:42:55 79493] Attempting to switch stdout/stderr to 4...
[T 18:42:55 79493] Initializing tracer.
[T 18:42:55 79493] Initialising tags...
[T 18:42:55 79493] ID set to 0000000000013685_0000000000000001 (parent 0000000000013633_0000000000000002)
[E 18:42:55 79493] CodeQL C/C++ Extractor 2.11.2
[E 18:42:55 79493] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:42:55 79493] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_03a7ee14.c -o code_03a7ee14
[T 18:42:56 79494] Initializing tracer.
[T 18:42:56 79494] Initialising tags...
[T 18:42:56 79494] ID set to 0000000000013686_0000000000000001 (parent 0000000000013685_0000000000000001)
[T 18:42:56 79494] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79494] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79494] Executing the following tracer actions:
[T 18:42:56 79494] Tracer actions:
[T 18:42:56 79494] pre_invocations(0)
[T 18:42:56 79494] post_invocations(1)
[T 18:42:56 79494] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 18:42:56 79494] trace_languages(1): [cpp]
[T 18:42:56 79496] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79496] Initializing tracer.
[T 18:42:56 79496] Initialising tags...
[T 18:42:56 79496] ID set to 0000000000013688_0000000000000001 (parent 0000000000013686_0000000000000001)
[E 18:42:56 79496] Mimicry classification suppression detected; exiting.
[T 18:42:56 79494] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79497] Initializing tracer.
[T 18:42:56 79497] Initialising tags...
[T 18:42:56 79497] ID set to 0000000000013689_0000000000000001 (parent 0000000000013685_0000000000000001)
[T 18:42:56 79497] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79497] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79497] Executing the following tracer actions:
[T 18:42:56 79497] Tracer actions:
[T 18:42:56 79497] pre_invocations(0)
[T 18:42:56 79497] post_invocations(1)
[T 18:42:56 79497] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 18:42:56 79497] trace_languages(1): [cpp]
[T 18:42:56 79499] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79499] Initializing tracer.
[T 18:42:56 79499] Initialising tags...
[T 18:42:56 79499] ID set to 000000000001368B_0000000000000001 (parent 0000000000013689_0000000000000001)
[E 18:42:56 79499] Mimicry classification suppression detected; exiting.
[T 18:42:56 79497] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79500] Initializing tracer.
[T 18:42:56 79500] Initialising tags...
[T 18:42:56 79500] ID set to 000000000001368C_0000000000000001 (parent 0000000000013685_0000000000000001)
[T 18:42:56 79500] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79500] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79500] Executing the following tracer actions:
[T 18:42:56 79500] Tracer actions:
[T 18:42:56 79500] pre_invocations(0)
[T 18:42:56 79500] post_invocations(1)
[T 18:42:56 79500] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79493_114246.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79493_114006.c]
[T 18:42:56 79500] trace_languages(1): [cpp]
[T 18:42:56 79502] Initializing tracer.
[T 18:42:56 79502] Initialising tags...
[T 18:42:56 79502] ID set to 000000000001368E_0000000000000001 (parent 000000000001368C_0000000000000001)
[T 18:42:56 79502] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79502] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79502] Executing the following tracer actions:
[T 18:42:56 79502] Tracer actions:
[T 18:42:56 79502] pre_invocations(0)
[T 18:42:56 79502] post_invocations(1)
[T 18:42:56 79502] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_79493_114006.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79493_114246.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79493_114006.c]
[T 18:42:56 79502] trace_languages(1): [cpp]
[T 18:42:56 79504] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79504] Initializing tracer.
[T 18:42:56 79504] Initialising tags...
[T 18:42:56 79504] ID set to 0000000000013690_0000000000000001 (parent 000000000001368E_0000000000000001)
[E 18:42:56 79504] Mimicry classification suppression detected; exiting.
[T 18:42:56 79502] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79505] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79505] Initializing tracer.
[T 18:42:56 79505] Initialising tags...
[T 18:42:56 79505] ID set to 0000000000013691_0000000000000001 (parent 000000000001368C_0000000000000001)
[E 18:42:56 79505] Mimicry classification suppression detected; exiting.
[T 18:42:56 79500] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79506] Initializing tracer.
[T 18:42:56 79506] Initialising tags...
[T 18:42:56 79506] ID set to 0000000000013692_0000000000000001 (parent 0000000000013685_0000000000000001)
[T 18:42:56 79506] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79506] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79506] Executing the following tracer actions:
[T 18:42:56 79506] Tracer actions:
[T 18:42:56 79506] pre_invocations(0)
[T 18:42:56 79506] post_invocations(1)
[T 18:42:56 79506] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79493_283086.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79493_282878.c]
[T 18:42:56 79506] trace_languages(1): [cpp]
[T 18:42:56 79508] Initializing tracer.
[T 18:42:56 79508] Initialising tags...
[T 18:42:56 79508] ID set to 0000000000013694_0000000000000001 (parent 0000000000013692_0000000000000001)
[T 18:42:56 79508] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79508] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79508] Executing the following tracer actions:
[T 18:42:56 79508] Tracer actions:
[T 18:42:56 79508] pre_invocations(0)
[T 18:42:56 79508] post_invocations(1)
[T 18:42:56 79508] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_79493_282878.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79493_283086.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79493_282878.c]
[T 18:42:56 79508] trace_languages(1): [cpp]
[T 18:42:56 79510] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79510] Initializing tracer.
[T 18:42:56 79510] Initialising tags...
[T 18:42:56 79510] ID set to 0000000000013696_0000000000000001 (parent 0000000000013694_0000000000000001)
[E 18:42:56 79510] Mimicry classification suppression detected; exiting.
[T 18:42:56 79508] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79511] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79511] Initializing tracer.
[T 18:42:56 79511] Initialising tags...
[T 18:42:56 79511] ID set to 0000000000013697_0000000000000001 (parent 0000000000013692_0000000000000001)
[E 18:42:56 79511] Mimicry classification suppression detected; exiting.
[T 18:42:56 79506] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 18:42:56 79493] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:42:56 79493] Checking whether C compilation already happened.
[E 18:42:56 79493] Checking for tag c-compilation-happened
[E 18:42:56 79493] Checking CODEQL_TRACER_DB_ID 0000000000013633_0000000000000002
[E 18:42:56 79493] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:56 79493] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:56 79493] Unlocking DB
[E 18:42:56 79493] Unlocked DB
[E 18:42:56 79493] Exiting as C compilation already happened.
[T 18:42:56 79411] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79514] Attempting to switch stdout/stderr to 4...
[T 18:42:56 79514] Initializing tracer.
[T 18:42:56 79514] Initialising tags...
[T 18:42:56 79514] ID set to 000000000001369A_0000000000000001 (parent 000000000001362D_0000000000000001)
[E 18:42:56 79514] CodeQL C/C++ Extractor 2.11.2
[E 18:42:56 79514] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:42:56 79514] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/gcc code_03a7ee14.c -o code_03a7ee14
[T 18:42:56 79518] Initializing tracer.
[T 18:42:56 79518] Initialising tags...
[T 18:42:56 79518] ID set to 000000000001369E_0000000000000001 (parent 000000000001369A_0000000000000001)
[T 18:42:56 79518] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:56 79518] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:56 79518] Executing the following tracer actions:
[T 18:42:56 79518] Tracer actions:
[T 18:42:56 79518] pre_invocations(0)
[T 18:42:56 79518] post_invocations(1)
[T 18:42:56 79518] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 18:42:56 79518] trace_languages(1): [cpp]
[T 18:42:56 79519] Initializing tracer.
[T 18:42:56 79519] Initialising tags...
[T 18:42:56 79519] ID set to 000000000001369F_0000000000000001 (parent 000000000001369E_0000000000000001)
[T 18:42:56 79519] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:56 79519] Executing the following tracer actions:
[T 18:42:56 79519] Tracer actions:
[T 18:42:56 79519] pre_invocations(0)
[T 18:42:56 79519] post_invocations(0)
[T 18:42:56 79519] trace_languages(1): [cpp]
[T 18:42:56 79519] Initializing tracer.
[T 18:42:56 79519] Initialising tags...
[T 18:42:56 79519] ID set to 000000000001369F_0000000000000002 (parent 000000000001369F_0000000000000001)
[T 18:42:56 79519] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79519] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79519] Executing the following tracer actions:
[T 18:42:56 79519] Tracer actions:
[T 18:42:56 79519] pre_invocations(0)
[T 18:42:56 79519] post_invocations(1)
[T 18:42:56 79519] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 18:42:56 79519] trace_languages(1): [cpp]
[T 18:42:56 79521] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79521] Initializing tracer.
[T 18:42:56 79521] Initialising tags...
[T 18:42:56 79521] ID set to 00000000000136A1_0000000000000001 (parent 000000000001369F_0000000000000002)
[E 18:42:56 79521] Mimicry classification suppression detected; exiting.
[T 18:42:56 79519] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79522] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79522] Initializing tracer.
[T 18:42:56 79522] Initialising tags...
[T 18:42:56 79522] ID set to 00000000000136A2_0000000000000001 (parent 000000000001369E_0000000000000001)
[E 18:42:56 79522] Mimicry classification suppression detected; exiting.
[T 18:42:56 79518] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79523] Initializing tracer.
[T 18:42:56 79523] Initialising tags...
[T 18:42:56 79523] ID set to 00000000000136A3_0000000000000001 (parent 000000000001369A_0000000000000001)
[T 18:42:56 79523] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:56 79523] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:56 79523] Executing the following tracer actions:
[T 18:42:56 79523] Tracer actions:
[T 18:42:56 79523] pre_invocations(0)
[T 18:42:56 79523] post_invocations(1)
[T 18:42:56 79523] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 18:42:56 79523] trace_languages(1): [cpp]
[T 18:42:56 79524] Initializing tracer.
[T 18:42:56 79524] Initialising tags...
[T 18:42:56 79524] ID set to 00000000000136A4_0000000000000001 (parent 00000000000136A3_0000000000000001)
[T 18:42:56 79524] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:56 79524] Executing the following tracer actions:
[T 18:42:56 79524] Tracer actions:
[T 18:42:56 79524] pre_invocations(0)
[T 18:42:56 79524] post_invocations(0)
[T 18:42:56 79524] trace_languages(1): [cpp]
[T 18:42:56 79524] Initializing tracer.
[T 18:42:56 79524] Initialising tags...
[T 18:42:56 79524] ID set to 00000000000136A4_0000000000000002 (parent 00000000000136A4_0000000000000001)
[T 18:42:56 79524] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79524] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79524] Executing the following tracer actions:
[T 18:42:56 79524] Tracer actions:
[T 18:42:56 79524] pre_invocations(0)
[T 18:42:56 79524] post_invocations(1)
[T 18:42:56 79524] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 18:42:56 79524] trace_languages(1): [cpp]
[T 18:42:56 79526] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79526] Initializing tracer.
[T 18:42:56 79526] Initialising tags...
[T 18:42:56 79526] ID set to 00000000000136A6_0000000000000001 (parent 00000000000136A4_0000000000000002)
[E 18:42:56 79526] Mimicry classification suppression detected; exiting.
[T 18:42:56 79524] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79527] Attempting to switch stdout/stderr to 7...
[T 18:42:56 79527] Initializing tracer.
[T 18:42:56 79527] Initialising tags...
[T 18:42:56 79527] ID set to 00000000000136A7_0000000000000001 (parent 00000000000136A3_0000000000000001)
[E 18:42:56 79527] Mimicry classification suppression detected; exiting.
[T 18:42:56 79523] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:56 79528] Initializing tracer.
[T 18:42:56 79528] Initialising tags...
[T 18:42:56 79528] ID set to 00000000000136A8_0000000000000001 (parent 000000000001369A_0000000000000001)
[T 18:42:56 79528] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:56 79528] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:56 79528] Executing the following tracer actions:
[T 18:42:56 79528] Tracer actions:
[T 18:42:56 79528] pre_invocations(0)
[T 18:42:56 79528] post_invocations(1)
[T 18:42:56 79528] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79514_838978.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79514_838673.c]
[T 18:42:56 79528] trace_languages(1): [cpp]
[T 18:42:56 79529] Initializing tracer.
[T 18:42:56 79529] Initialising tags...
[T 18:42:56 79529] ID set to 00000000000136A9_0000000000000001 (parent 00000000000136A8_0000000000000001)
[T 18:42:56 79529] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:56 79529] Executing the following tracer actions:
[T 18:42:56 79529] Tracer actions:
[T 18:42:56 79529] pre_invocations(0)
[T 18:42:56 79529] post_invocations(0)
[T 18:42:56 79529] trace_languages(1): [cpp]
[T 18:42:56 79529] Initializing tracer.
[T 18:42:56 79529] Initialising tags...
[T 18:42:56 79529] ID set to 00000000000136A9_0000000000000002 (parent 00000000000136A9_0000000000000001)
[T 18:42:56 79529] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79529] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79529] Executing the following tracer actions:
[T 18:42:56 79529] Tracer actions:
[T 18:42:56 79529] pre_invocations(0)
[T 18:42:56 79529] post_invocations(1)
[T 18:42:56 79529] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79514_838978.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79514_838673.c]
[T 18:42:56 79529] trace_languages(1): [cpp]
[T 18:42:56 79531] Initializing tracer.
[T 18:42:56 79531] Initialising tags...
[T 18:42:56 79531] ID set to 00000000000136AB_0000000000000001 (parent 00000000000136A9_0000000000000002)
[T 18:42:56 79531] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:56 79531] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:56 79531] Executing the following tracer actions:
[T 18:42:56 79531] Tracer actions:
[T 18:42:56 79531] pre_invocations(0)
[T 18:42:56 79531] post_invocations(1)
[T 18:42:56 79531] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_79514_838673.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79514_838978.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79514_838673.c]
[T 18:42:56 79531] trace_languages(1): [cpp]
[T 18:42:57 79533] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79533] Initializing tracer.
[T 18:42:57 79533] Initialising tags...
[T 18:42:57 79533] ID set to 00000000000136AD_0000000000000001 (parent 00000000000136AB_0000000000000001)
[E 18:42:57 79533] Mimicry classification suppression detected; exiting.
[T 18:42:57 79531] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79534] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79534] Initializing tracer.
[T 18:42:57 79534] Initialising tags...
[T 18:42:57 79534] ID set to 00000000000136AE_0000000000000001 (parent 00000000000136A9_0000000000000002)
[E 18:42:57 79534] Mimicry classification suppression detected; exiting.
[T 18:42:57 79529] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79535] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79535] Initializing tracer.
[T 18:42:57 79535] Initialising tags...
[T 18:42:57 79535] ID set to 00000000000136AF_0000000000000001 (parent 00000000000136A8_0000000000000001)
[E 18:42:57 79535] Mimicry classification suppression detected; exiting.
[T 18:42:57 79528] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79536] Initializing tracer.
[T 18:42:57 79536] Initialising tags...
[T 18:42:57 79536] ID set to 00000000000136B0_0000000000000001 (parent 000000000001369A_0000000000000001)
[T 18:42:57 79536] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:57 79536] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:57 79536] Executing the following tracer actions:
[T 18:42:57 79536] Tracer actions:
[T 18:42:57 79536] pre_invocations(0)
[T 18:42:57 79536] post_invocations(1)
[T 18:42:57 79536] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79514_73643.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79514_73411.c]
[T 18:42:57 79536] trace_languages(1): [cpp]
[T 18:42:57 79537] Initializing tracer.
[T 18:42:57 79537] Initialising tags...
[T 18:42:57 79537] ID set to 00000000000136B1_0000000000000001 (parent 00000000000136B0_0000000000000001)
[T 18:42:57 79537] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:57 79537] Executing the following tracer actions:
[T 18:42:57 79537] Tracer actions:
[T 18:42:57 79537] pre_invocations(0)
[T 18:42:57 79537] post_invocations(0)
[T 18:42:57 79537] trace_languages(1): [cpp]
[T 18:42:57 79537] Initializing tracer.
[T 18:42:57 79537] Initialising tags...
[T 18:42:57 79537] ID set to 00000000000136B1_0000000000000002 (parent 00000000000136B1_0000000000000001)
[T 18:42:57 79537] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:57 79537] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:57 79537] Executing the following tracer actions:
[T 18:42:57 79537] Tracer actions:
[T 18:42:57 79537] pre_invocations(0)
[T 18:42:57 79537] post_invocations(1)
[T 18:42:57 79537] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79514_73643.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79514_73411.c]
[T 18:42:57 79537] trace_languages(1): [cpp]
[T 18:42:57 79539] Initializing tracer.
[T 18:42:57 79539] Initialising tags...
[T 18:42:57 79539] ID set to 00000000000136B3_0000000000000001 (parent 00000000000136B1_0000000000000002)
[T 18:42:57 79539] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:57 79539] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:57 79539] Executing the following tracer actions:
[T 18:42:57 79539] Tracer actions:
[T 18:42:57 79539] pre_invocations(0)
[T 18:42:57 79539] post_invocations(1)
[T 18:42:57 79539] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_79514_73411.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79514_73643.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79514_73411.c]
[T 18:42:57 79539] trace_languages(1): [cpp]
[T 18:42:57 79541] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79541] Initializing tracer.
[T 18:42:57 79541] Initialising tags...
[T 18:42:57 79541] ID set to 00000000000136B5_0000000000000001 (parent 00000000000136B3_0000000000000001)
[E 18:42:57 79541] Mimicry classification suppression detected; exiting.
[T 18:42:57 79539] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79542] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79542] Initializing tracer.
[T 18:42:57 79542] Initialising tags...
[T 18:42:57 79542] ID set to 00000000000136B6_0000000000000001 (parent 00000000000136B1_0000000000000002)
[E 18:42:57 79542] Mimicry classification suppression detected; exiting.
[T 18:42:57 79537] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79543] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79543] Initializing tracer.
[T 18:42:57 79543] Initialising tags...
[T 18:42:57 79543] ID set to 00000000000136B7_0000000000000001 (parent 00000000000136B0_0000000000000001)
[E 18:42:57 79543] Mimicry classification suppression detected; exiting.
[T 18:42:57 79536] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 18:42:57 79514] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:42:57 79514] Checking whether C compilation already happened.
[E 18:42:57 79514] Checking for tag c-compilation-happened
[E 18:42:57 79514] Checking CODEQL_TRACER_DB_ID 000000000001362D_0000000000000001
[E 18:42:57 79514] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:57 79514] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:57 79514] Unlocking DB
[E 18:42:57 79514] Unlocked DB
[E 18:42:57 79514] Exiting as C compilation already happened.
[T 18:42:57 79405] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79544] Attempting to switch stdout/stderr to 4...
[T 18:42:57 79544] Initializing tracer.
[T 18:42:57 79544] Initialising tags...
[T 18:42:57 79544] ID set to 00000000000136B8_0000000000000001 (parent 000000000001362B_0000000000000001)
[E 18:42:57 79544] CodeQL C/C++ Extractor 2.11.2
[E 18:42:57 79544] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 18:42:57 79544] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /usr/bin/gcc code_03a7ee14.c -o code_03a7ee14
[T 18:42:57 79545] Initializing tracer.
[T 18:42:57 79545] Initialising tags...
[T 18:42:57 79545] ID set to 00000000000136B9_0000000000000001 (parent 00000000000136B8_0000000000000001)
[T 18:42:57 79545] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:42:57 79545] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:42:57 79545] Executing the following tracer actions:
[T 18:42:57 79545] Tracer actions:
[T 18:42:57 79545] pre_invocations(0)
[T 18:42:57 79545] post_invocations(1)
[T 18:42:57 79545] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --version]
[T 18:42:57 79545] trace_languages(1): [cpp]
[T 18:42:57 79546] Initializing tracer.
[T 18:42:57 79546] Initialising tags...
[T 18:42:57 79546] ID set to 00000000000136BA_0000000000000001 (parent 00000000000136B9_0000000000000001)
[T 18:42:57 79546] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:57 79546] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:57 79546] Executing the following tracer actions:
[T 18:42:57 79546] Tracer actions:
[T 18:42:57 79546] pre_invocations(0)
[T 18:42:57 79546] post_invocations(1)
[T 18:42:57 79546] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 18:42:57 79546] trace_languages(1): [cpp]
[T 18:42:57 79547] Initializing tracer.
[T 18:42:57 79547] Initialising tags...
[T 18:42:57 79547] ID set to 00000000000136BB_0000000000000001 (parent 00000000000136BA_0000000000000001)
[T 18:42:57 79547] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:57 79547] Executing the following tracer actions:
[T 18:42:57 79547] Tracer actions:
[T 18:42:57 79547] pre_invocations(0)
[T 18:42:57 79547] post_invocations(0)
[T 18:42:57 79547] trace_languages(1): [cpp]
[T 18:42:57 79547] Initializing tracer.
[T 18:42:57 79547] Initialising tags...
[T 18:42:57 79547] ID set to 00000000000136BB_0000000000000002 (parent 00000000000136BB_0000000000000001)
[T 18:42:57 79547] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:57 79547] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:57 79547] Executing the following tracer actions:
[T 18:42:57 79547] Tracer actions:
[T 18:42:57 79547] pre_invocations(0)
[T 18:42:57 79547] post_invocations(1)
[T 18:42:57 79547] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 18:42:57 79547] trace_languages(1): [cpp]
[T 18:42:57 79549] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79549] Initializing tracer.
[T 18:42:57 79549] Initialising tags...
[T 18:42:57 79549] ID set to 00000000000136BD_0000000000000001 (parent 00000000000136BB_0000000000000002)
[E 18:42:57 79549] Mimicry classification suppression detected; exiting.
[T 18:42:57 79547] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79550] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79550] Initializing tracer.
[T 18:42:57 79550] Initialising tags...
[T 18:42:57 79550] ID set to 00000000000136BE_0000000000000001 (parent 00000000000136BA_0000000000000001)
[E 18:42:57 79550] Mimicry classification suppression detected; exiting.
[T 18:42:57 79546] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79551] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79551] Initializing tracer.
[T 18:42:57 79551] Initialising tags...
[T 18:42:57 79551] ID set to 00000000000136BF_0000000000000001 (parent 00000000000136B9_0000000000000001)
[E 18:42:57 79551] Mimicry classification suppression detected; exiting.
[T 18:42:57 79545] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79552] Initializing tracer.
[T 18:42:57 79552] Initialising tags...
[T 18:42:57 79552] ID set to 00000000000136C0_0000000000000001 (parent 00000000000136B8_0000000000000001)
[T 18:42:57 79552] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:42:57 79552] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:42:57 79552] Executing the following tracer actions:
[T 18:42:57 79552] Tracer actions:
[T 18:42:57 79552] pre_invocations(0)
[T 18:42:57 79552] post_invocations(1)
[T 18:42:57 79552] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --help]
[T 18:42:57 79552] trace_languages(1): [cpp]
[T 18:42:57 79554] Initializing tracer.
[T 18:42:57 79554] Initialising tags...
[T 18:42:57 79554] ID set to 00000000000136C2_0000000000000001 (parent 00000000000136C0_0000000000000001)
[T 18:42:57 79554] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:57 79554] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:57 79554] Executing the following tracer actions:
[T 18:42:57 79554] Tracer actions:
[T 18:42:57 79554] pre_invocations(0)
[T 18:42:57 79554] post_invocations(1)
[T 18:42:57 79554] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 18:42:57 79554] trace_languages(1): [cpp]
[T 18:42:57 79555] Initializing tracer.
[T 18:42:57 79555] Initialising tags...
[T 18:42:57 79555] ID set to 00000000000136C3_0000000000000001 (parent 00000000000136C2_0000000000000001)
[T 18:42:57 79555] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:57 79555] Executing the following tracer actions:
[T 18:42:57 79555] Tracer actions:
[T 18:42:57 79555] pre_invocations(0)
[T 18:42:57 79555] post_invocations(0)
[T 18:42:57 79555] trace_languages(1): [cpp]
[T 18:42:57 79555] Initializing tracer.
[T 18:42:57 79555] Initialising tags...
[T 18:42:57 79555] ID set to 00000000000136C3_0000000000000002 (parent 00000000000136C3_0000000000000001)
[T 18:42:57 79555] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:57 79555] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:57 79555] Executing the following tracer actions:
[T 18:42:57 79555] Tracer actions:
[T 18:42:57 79555] pre_invocations(0)
[T 18:42:57 79555] post_invocations(1)
[T 18:42:57 79555] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 18:42:57 79555] trace_languages(1): [cpp]
[T 18:42:57 79557] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79557] Initializing tracer.
[T 18:42:57 79557] Initialising tags...
[T 18:42:57 79557] ID set to 00000000000136C5_0000000000000001 (parent 00000000000136C3_0000000000000002)
[E 18:42:57 79557] Mimicry classification suppression detected; exiting.
[T 18:42:57 79555] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79558] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79558] Initializing tracer.
[T 18:42:57 79558] Initialising tags...
[T 18:42:57 79558] ID set to 00000000000136C6_0000000000000001 (parent 00000000000136C2_0000000000000001)
[E 18:42:57 79558] Mimicry classification suppression detected; exiting.
[T 18:42:57 79554] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79559] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79559] Initializing tracer.
[T 18:42:57 79559] Initialising tags...
[T 18:42:57 79559] ID set to 00000000000136C7_0000000000000001 (parent 00000000000136C0_0000000000000001)
[E 18:42:57 79559] Mimicry classification suppression detected; exiting.
[T 18:42:57 79552] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:57 79560] Initializing tracer.
[T 18:42:57 79560] Initialising tags...
[T 18:42:57 79560] ID set to 00000000000136C8_0000000000000001 (parent 00000000000136B8_0000000000000001)
[T 18:42:57 79560] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:42:57 79560] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:42:57 79560] Executing the following tracer actions:
[T 18:42:57 79560] Tracer actions:
[T 18:42:57 79560] pre_invocations(0)
[T 18:42:57 79560] post_invocations(1)
[T 18:42:57 79560] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79544_758260.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79544_758119.c]
[T 18:42:57 79560] trace_languages(1): [cpp]
[T 18:42:57 79561] Initializing tracer.
[T 18:42:57 79561] Initialising tags...
[T 18:42:57 79561] ID set to 00000000000136C9_0000000000000001 (parent 00000000000136C8_0000000000000001)
[T 18:42:57 79561] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:57 79561] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:57 79561] Executing the following tracer actions:
[T 18:42:57 79561] Tracer actions:
[T 18:42:57 79561] pre_invocations(0)
[T 18:42:57 79561] post_invocations(1)
[T 18:42:57 79561] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79544_758260.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79544_758119.c]
[T 18:42:57 79561] trace_languages(1): [cpp]
[T 18:42:57 79562] Initializing tracer.
[T 18:42:57 79562] Initialising tags...
[T 18:42:57 79562] ID set to 00000000000136CA_0000000000000001 (parent 00000000000136C9_0000000000000001)
[T 18:42:57 79562] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:57 79562] Executing the following tracer actions:
[T 18:42:57 79562] Tracer actions:
[T 18:42:57 79562] pre_invocations(0)
[T 18:42:57 79562] post_invocations(0)
[T 18:42:57 79562] trace_languages(1): [cpp]
[T 18:42:57 79562] Initializing tracer.
[T 18:42:57 79562] Initialising tags...
[T 18:42:57 79562] ID set to 00000000000136CA_0000000000000002 (parent 00000000000136CA_0000000000000001)
[T 18:42:57 79562] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:57 79562] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:57 79562] Executing the following tracer actions:
[T 18:42:57 79562] Tracer actions:
[T 18:42:57 79562] pre_invocations(0)
[T 18:42:57 79562] post_invocations(1)
[T 18:42:57 79562] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79544_758260.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79544_758119.c]
[T 18:42:57 79562] trace_languages(1): [cpp]
[T 18:42:57 79564] Initializing tracer.
[T 18:42:57 79564] Initialising tags...
[T 18:42:57 79564] ID set to 00000000000136CC_0000000000000001 (parent 00000000000136CA_0000000000000002)
[T 18:42:57 79564] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:57 79564] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:57 79564] Executing the following tracer actions:
[T 18:42:57 79564] Tracer actions:
[T 18:42:57 79564] pre_invocations(0)
[T 18:42:57 79564] post_invocations(1)
[T 18:42:57 79564] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_79544_758119.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_2_79544_758260.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_1_79544_758119.c]
[T 18:42:57 79564] trace_languages(1): [cpp]
[T 18:42:57 79566] Attempting to switch stdout/stderr to 7...
[T 18:42:57 79566] Initializing tracer.
[T 18:42:57 79566] Initialising tags...
[T 18:42:57 79566] ID set to 00000000000136CE_0000000000000001 (parent 00000000000136CC_0000000000000001)
[E 18:42:57 79566] Mimicry classification suppression detected; exiting.
[T 18:42:57 79564] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:58 79567] Attempting to switch stdout/stderr to 7...
[T 18:42:58 79567] Initializing tracer.
[T 18:42:58 79567] Initialising tags...
[T 18:42:58 79567] ID set to 00000000000136CF_0000000000000001 (parent 00000000000136CA_0000000000000002)
[E 18:42:58 79567] Mimicry classification suppression detected; exiting.
[T 18:42:58 79562] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:58 79568] Attempting to switch stdout/stderr to 7...
[T 18:42:58 79568] Initializing tracer.
[T 18:42:58 79568] Initialising tags...
[T 18:42:58 79568] ID set to 00000000000136D0_0000000000000001 (parent 00000000000136C9_0000000000000001)
[E 18:42:58 79568] Mimicry classification suppression detected; exiting.
[T 18:42:58 79561] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:58 79569] Attempting to switch stdout/stderr to 7...
[T 18:42:58 79569] Initializing tracer.
[T 18:42:58 79569] Initialising tags...
[T 18:42:58 79569] ID set to 00000000000136D1_0000000000000001 (parent 00000000000136C8_0000000000000001)
[E 18:42:58 79569] Mimicry classification suppression detected; exiting.
[T 18:42:58 79560] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:58 79570] Initializing tracer.
[T 18:42:58 79570] Initialising tags...
[T 18:42:58 79570] ID set to 00000000000136D2_0000000000000001 (parent 00000000000136B8_0000000000000001)
[T 18:42:58 79570] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 18:42:58 79570] Lua: === Intercepted call to /usr/bin/gcc ===
[T 18:42:58 79570] Executing the following tracer actions:
[T 18:42:58 79570] Tracer actions:
[T 18:42:58 79570] pre_invocations(0)
[T 18:42:58 79570] post_invocations(1)
[T 18:42:58 79570] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79544_80520.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79544_80354.c]
[T 18:42:58 79570] trace_languages(1): [cpp]
[T 18:42:58 79571] Initializing tracer.
[T 18:42:58 79571] Initialising tags...
[T 18:42:58 79571] ID set to 00000000000136D3_0000000000000001 (parent 00000000000136D2_0000000000000001)
[T 18:42:58 79571] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 18:42:58 79571] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 18:42:58 79571] Executing the following tracer actions:
[T 18:42:58 79571] Tracer actions:
[T 18:42:58 79571] pre_invocations(0)
[T 18:42:58 79571] post_invocations(1)
[T 18:42:58 79571] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79544_80520.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79544_80354.c]
[T 18:42:58 79571] trace_languages(1): [cpp]
[T 18:42:58 79574] Initializing tracer.
[T 18:42:58 79574] Initialising tags...
[T 18:42:58 79574] ID set to 00000000000136D6_0000000000000001 (parent 00000000000136D3_0000000000000001)
[T 18:42:58 79574] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 18:42:58 79574] Executing the following tracer actions:
[T 18:42:58 79574] Tracer actions:
[T 18:42:58 79574] pre_invocations(0)
[T 18:42:58 79574] post_invocations(0)
[T 18:42:58 79574] trace_languages(1): [cpp]
[T 18:42:58 79574] Initializing tracer.
[T 18:42:58 79574] Initialising tags...
[T 18:42:58 79574] ID set to 00000000000136D6_0000000000000002 (parent 00000000000136D6_0000000000000001)
[T 18:42:58 79574] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:58 79574] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:58 79574] Executing the following tracer actions:
[T 18:42:58 79574] Tracer actions:
[T 18:42:58 79574] pre_invocations(0)
[T 18:42:58 79574] post_invocations(1)
[T 18:42:58 79574] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79544_80520.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79544_80354.c]
[T 18:42:58 79574] trace_languages(1): [cpp]
[T 18:42:58 79576] Initializing tracer.
[T 18:42:58 79576] Initialising tags...
[T 18:42:58 79576] ID set to 00000000000136D8_0000000000000001 (parent 00000000000136D6_0000000000000002)
[T 18:42:58 79576] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 18:42:58 79576] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 18:42:58 79576] Executing the following tracer actions:
[T 18:42:58 79576] Tracer actions:
[T 18:42:58 79576] pre_invocations(0)
[T 18:42:58 79576] post_invocations(1)
[T 18:42:58 79576] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_79544_80354.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_4_79544_80520.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tmp//semmle_3_79544_80354.c]
[T 18:42:58 79576] trace_languages(1): [cpp]
[T 18:42:58 79578] Attempting to switch stdout/stderr to 7...
[T 18:42:58 79578] Initializing tracer.
[T 18:42:58 79578] Initialising tags...
[T 18:42:58 79578] ID set to 00000000000136DA_0000000000000001 (parent 00000000000136D8_0000000000000001)
[E 18:42:58 79578] Mimicry classification suppression detected; exiting.
[T 18:42:58 79576] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:58 79579] Attempting to switch stdout/stderr to 7...
[T 18:42:58 79579] Initializing tracer.
[T 18:42:58 79579] Initialising tags...
[T 18:42:58 79579] ID set to 00000000000136DB_0000000000000001 (parent 00000000000136D6_0000000000000002)
[E 18:42:58 79579] Mimicry classification suppression detected; exiting.
[T 18:42:58 79574] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:58 79580] Attempting to switch stdout/stderr to 7...
[T 18:42:58 79580] Initializing tracer.
[T 18:42:58 79580] Initialising tags...
[T 18:42:58 79580] ID set to 00000000000136DC_0000000000000001 (parent 00000000000136D3_0000000000000001)
[E 18:42:58 79580] Mimicry classification suppression detected; exiting.
[T 18:42:58 79571] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 18:42:58 79581] Attempting to switch stdout/stderr to 7...
[T 18:42:58 79581] Initializing tracer.
[T 18:42:58 79581] Initialising tags...
[T 18:42:58 79581] ID set to 00000000000136DD_0000000000000001 (parent 00000000000136D2_0000000000000001)
[E 18:42:58 79581] Mimicry classification suppression detected; exiting.
[T 18:42:58 79570] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 18:42:58 79544] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 18:42:58 79544] Checking whether C compilation already happened.
[E 18:42:58 79544] Checking for tag c-compilation-happened
[E 18:42:58 79544] Checking CODEQL_TRACER_DB_ID 000000000001362B_0000000000000001
[E 18:42:58 79544] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:58 79544] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_03a7ee14/working/tags.db
[E 18:42:58 79544] Unlocking DB
[E 18:42:58 79544] Unlocked DB
[E 18:42:58 79544] Exiting as C compilation already happened.
[T 18:42:58 79403] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.

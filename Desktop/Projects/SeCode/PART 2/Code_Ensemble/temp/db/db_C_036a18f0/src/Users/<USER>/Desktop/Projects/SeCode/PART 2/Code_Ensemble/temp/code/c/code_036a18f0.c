#include <stdlib.h>
#include <string.h>

char* process_hostname(const char* hostname) {
    if (!hostname) {
        return NULL;
    }
    
    int len = strlen(hostname);
    int new_len = 0;
    
    // Calculate the new length required for the processed hostname
    for (int i = 0; i < len; i++) {
        if (strchr("-._", hostname[i])) {
            new_len++;
        } else {
            new_len += 2; // account for the space and original character
        }
    }
    
    // Allocate memory for the new string
    char* result = malloc((new_len + 1) * sizeof(char));
    if (!result) {
        return NULL; // Return NULL if memory allocation fails
    }
    
    int j = 0;
    for (int i = 0; i < len; i++) {
        if (strchr("-._", hostname[i])) {
            result[j++] = hostname[i];
        } else {
            result[j++] = hostname[i];
            result[j++] = ' ';
        }
    }
    
    result[new_len] = '\0'; // Null-terminate the new string
    return result;
}
[E 16:35:35 42730] CodeQL C/C++ Extractor 2.11.2
[E 16:35:35 42730] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:35 42730] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -mrelax-all -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -fno-odr-hash-protocols -x c -fno-color-diagnostics --help
[E 16:35:35 42730] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:35 42730] Checking whether C compilation already happened.
[E 16:35:35 42730] Checking for tag c-compilation-happened
[E 16:35:35 42730] Checking CODEQL_TRACER_DB_ID 000000000000A6E8_0000000000000001
[E 16:35:35 42730] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Unlocking DB
[E 16:35:35 42730] Unlocked DB
[E 16:35:35 42730] Looks like C compilation didn't already happen.
[E 16:35:35 42730] Checking whether C compilation has been attempted.
[E 16:35:35 42730] Checking for tag c-compilation-attempted
[E 16:35:35 42730] Checking CODEQL_TRACER_DB_ID 000000000000A6E8_0000000000000001
[E 16:35:35 42730] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Unlocking DB
[E 16:35:35 42730] Unlocked DB
[E 16:35:35 42730] Marking C compilation as attempted.
[E 16:35:35 42730] Setting tag c-compilation-attempted
[E 16:35:35 42730] Starting from CODEQL_TRACER_DB_ID 000000000000A6E8_0000000000000001
[E 16:35:35 42730] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Set tag for 000000000000A6E8_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6CE_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6CC_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6B9_0000000000000002
[E 16:35:35 42730] Set tag for 000000000000A6B9_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6B3_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6B1_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6A4_0000000000000004
[E 16:35:35 42730] Set tag for 000000000000A6A4_0000000000000003
[E 16:35:35 42730] Set tag for 000000000000A6A4_0000000000000002
[E 16:35:35 42730] Set tag for root
[E 16:35:35 42730] Unlocking DB
[E 16:35:35 42730] Unlocked DB
[E 16:35:35 42730] 0 file groups; exiting.

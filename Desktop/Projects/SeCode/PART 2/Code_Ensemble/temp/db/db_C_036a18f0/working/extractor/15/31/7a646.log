[E 16:35:38 42765] CodeQL C/C++ Extractor 2.11.2
[E 16:35:38 42765] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:38 42765] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_036a18f0.c -o code_036a18f0
[E 16:35:38 42765] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:38 42765] Checking whether C compilation already happened.
[E 16:35:38 42765] Checking for tag c-compilation-happened
[E 16:35:38 42765] Checking CODEQL_TRACER_DB_ID 000000000000A6B9_0000000000000002
[E 16:35:38 42765] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:38 42765] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:38 42765] Unlocking DB
[E 16:35:38 42765] Unlocked DB
[E 16:35:38 42765] Exiting as C compilation already happened.

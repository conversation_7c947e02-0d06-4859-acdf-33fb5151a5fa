[E 16:35:34 42702] CodeQL C/C++ Extractor 2.11.2
[E 16:35:34 42702] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:34 42702] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -dumpdir code_036a18f0- -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name code_036a18f0.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon '-clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation' -fno-odr-hash-protocols '-clang-vendor-feature=+enableAggressiveVLAFolding' '-clang-vendor-feature=+revert09abecef7bbf' '-clang-vendor-feature=+thisNoAlignAttr' '-clang-vendor-feature=+thisNoNullAttr' '-clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError' -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_036a18f0-658fd6.o -x c code_036a18f0.c
[E 16:35:35 42702] Checking whether C compilation already happened.
[E 16:35:35 42702] Checking for tag c-compilation-happened
[E 16:35:35 42702] Checking CODEQL_TRACER_DB_ID 000000000000A6CC_0000000000000001
[E 16:35:35 42702] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Unlocking DB
[E 16:35:35 42702] Unlocked DB
[E 16:35:35 42702] Looks like C compilation didn't already happen.
[E 16:35:35 42702] Checking whether C compilation has been attempted.
[E 16:35:35 42702] Checking for tag c-compilation-attempted
[E 16:35:35 42702] Checking CODEQL_TRACER_DB_ID 000000000000A6CC_0000000000000001
[E 16:35:35 42702] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Unlocking DB
[E 16:35:35 42702] Unlocked DB
[E 16:35:35 42702] Marking C compilation as attempted.
[E 16:35:35 42702] Setting tag c-compilation-attempted
[E 16:35:35 42702] Starting from CODEQL_TRACER_DB_ID 000000000000A6CC_0000000000000001
[E 16:35:35 42702] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Set tag for 000000000000A6CC_0000000000000001
[E 16:35:35 42702] Set tag for 000000000000A6B9_0000000000000002
[E 16:35:35 42702] Set tag for 000000000000A6B9_0000000000000001
[E 16:35:35 42702] Set tag for 000000000000A6B3_0000000000000001
[E 16:35:35 42702] Set tag for 000000000000A6B1_0000000000000001
[E 16:35:35 42702] Set tag for 000000000000A6A4_0000000000000004
[E 16:35:35 42702] Set tag for 000000000000A6A4_0000000000000003
[E 16:35:35 42702] Set tag for 000000000000A6A4_0000000000000002
[E 16:35:35 42702] Set tag for root
[E 16:35:35 42702] Unlocking DB
[E 16:35:35 42702] Unlocked DB
[E 16:35:35 42702] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:35 42702] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:36 42702] Processed command line: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --trapfolder '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp' --src_archive '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src' --mimic_config '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/compiler_mimic_cache/a1592c9a78e1' --object_filename /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_036a18f0-658fd6.o -w --error_limit 1000 --disable_system_macros --variadic_macros --gcc --clang_version 140000 --gnu_version 40801 --has_feature_vector 111111111111111100000000000000000000000000000000000000000000000000011 --clang --target linux_x86_64 -D_LP64=1 -D__APPLE_CC__=6000 -D__APPLE__=1 -D__ATOMIC_ACQUIRE=2 -D__ATOMIC_ACQ_REL=4 -D__ATOMIC_CONSUME=1 -D__ATOMIC_RELAXED=0 -D__ATOMIC_RELEASE=3 -D__ATOMIC_SEQ_CST=5 -D__BIGGEST_ALIGNMENT__=16 -D__BITINT_MAXWIDTH__=8388608 -D__BLOCKS__=1 -D__BOOL_WIDTH__=8 -D__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__ -D__CHAR_BIT__=8 -D__CLANG_ATOMIC_BOOL_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR_LOCK_FREE=2 -D__CLANG_ATOMIC_INT_LOCK_FREE=2 -D__CLANG_ATOMIC_LLONG_LOCK_FREE=2 -D__CLANG_ATOMIC_LONG_LOCK_FREE=2 -D__CLANG_ATOMIC_POINTER_LOCK_FREE=2 -D__CLANG_ATOMIC_SHORT_LOCK_FREE=2 -D__CLANG_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__CONSTANT_CFSTRINGS__=1 -D__DBL_DECIMAL_DIG__=17 -D__DBL_DENORM_MIN__=4.9406564584124654e-324 -D__DBL_DIG__=15 -D__DBL_EPSILON__=2.2204460492503131e-16 -D__DBL_HAS_DENORM__=1 -D__DBL_HAS_INFINITY__=1 -D__DBL_HAS_QUIET_NAN__=1 -D__DBL_MANT_DIG__=53 -D__DBL_MAX_10_EXP__=308 -D__DBL_MAX_EXP__=1024 '-D__DBL_MAX__=1.7976931348623157e+308' '-D__DBL_MIN_10_EXP__=(-307)' '-D__DBL_MIN_EXP__=(-1021)' -D__DBL_MIN__=2.2250738585072014e-308 -D__DECIMAL_DIG__=__LDBL_DECIMAL_DIG__ -D__DYNAMIC__=1 -D__ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__=150000 -D__ENVIRONMENT_OS_VERSION_MIN_REQUIRED__=150000 -D__FINITE_MATH_ONLY__=0 -D__FLT16_DECIMAL_DIG__=5 -D__FLT16_DENORM_MIN__=5.9604644775390625e-8F16 -D__FLT16_DIG__=3 -D__FLT16_EPSILON__=9.765625e-4F16 -D__FLT16_HAS_DENORM__=1 -D__FLT16_HAS_INFINITY__=1 -D__FLT16_HAS_QUIET_NAN__=1 -D__FLT16_MANT_DIG__=11 -D__FLT16_MAX_10_EXP__=4 -D__FLT16_MAX_EXP__=16 '-D__FLT16_MAX__=6.5504e+4F16' '-D__FLT16_MIN_10_EXP__=(-4)' '-D__FLT16_MIN_EXP__=(-13)' -D__FLT16_MIN__=6.103515625e-5F16 -D__FLT_DECIMAL_DIG__=9 -D__FLT_DENORM_MIN__=1.40129846e-45F -D__FLT_DIG__=6 -D__FLT_EPSILON__=1.19209290e-7F -D__FLT_HAS_DENORM__=1 -D__FLT_HAS_INFINITY__=1 -D__FLT_HAS_QUIET_NAN__=1 -D__FLT_MANT_DIG__=24 -D__FLT_MAX_10_EXP__=38 -D__FLT_MAX_EXP__=128 '-D__FLT_MAX__=3.40282347e+38F' '-D__FLT_MIN_10_EXP__=(-37)' '-D__FLT_MIN_EXP__=(-125)' -D__FLT_MIN__=1.17549435e-38F -D__FLT_RADIX__=2 -D__FPCLASS_NEGINF=0x0004 -D__FPCLASS_NEGNORMAL=0x0008 -D__FPCLASS_NEGSUBNORMAL=0x0010 -D__FPCLASS_NEGZERO=0x0020 -D__FPCLASS_POSINF=0x0200 -D__FPCLASS_POSNORMAL=0x0100 -D__FPCLASS_POSSUBNORMAL=0x0080 -D__FPCLASS_POSZERO=0x0040 -D__FPCLASS_QNAN=0x0002 -D__FPCLASS_SNAN=0x0001 -D__FXSR__=1 -D__GCC_ASM_FLAG_OUTPUTS__=1 -D__GCC_ATOMIC_BOOL_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR_LOCK_FREE=2 -D__GCC_ATOMIC_INT_LOCK_FREE=2 -D__GCC_ATOMIC_LLONG_LOCK_FREE=2 -D__GCC_ATOMIC_LONG_LOCK_FREE=2 -D__GCC_ATOMIC_POINTER_LOCK_FREE=2 -D__GCC_ATOMIC_SHORT_LOCK_FREE=2 -D__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1 -D__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -D__GNUC__=4 -D__GXX_ABI_VERSION=1002 -D__INT16_C_SUFFIX__= '-D__INT16_FMTd__="hd"' '-D__INT16_FMTi__="hi"' -D__INT16_MAX__=32767 -D__INT16_TYPE__=short -D__INT32_C_SUFFIX__= '-D__INT32_FMTd__="d"' '-D__INT32_FMTi__="i"' -D__INT32_MAX__=2147483647 -D__INT32_TYPE__=int -D__INT64_C_SUFFIX__=LL '-D__INT64_FMTd__="lld"' '-D__INT64_FMTi__="lli"' -D__INT64_MAX__=9223372036854775807LL '-D__INT64_TYPE__=long long int' -D__INT8_C_SUFFIX__= '-D__INT8_FMTd__="hhd"' '-D__INT8_FMTi__="hhi"' -D__INT8_MAX__=127 '-D__INT8_TYPE__=signed char' -D__INTMAX_C_SUFFIX__=L '-D__INTMAX_FMTd__="ld"' '-D__INTMAX_FMTi__="li"' -D__INTMAX_MAX__=9223372036854775807L '-D__INTMAX_TYPE__=long int' -D__INTMAX_WIDTH__=64 '-D__INTPTR_FMTd__="ld"' '-D__INTPTR_FMTi__="li"' -D__INTPTR_MAX__=9223372036854775807L '-D__INTPTR_TYPE__=long int' -D__INTPTR_WIDTH__=64 '-D__INT_FAST16_FMTd__="hd"' '-D__INT_FAST16_FMTi__="hi"' -D__INT_FAST16_MAX__=32767 -D__INT_FAST16_TYPE__=short -D__INT_FAST16_WIDTH__=16 '-D__INT_FAST32_FMTd__="d"' '-D__INT_FAST32_FMTi__="i"' -D__INT_FAST32_MAX__=2147483647 -D__INT_FAST32_TYPE__=int -D__INT_FAST32_WIDTH__=32 '-D__INT_FAST64_FMTd__="lld"' '-D__INT_FAST64_FMTi__="lli"' -D__INT_FAST64_MAX__=9223372036854775807LL '-D__INT_FAST64_TYPE__=long long int' -D__INT_FAST64_WIDTH__=64 '-D__INT_FAST8_FMTd__="hhd"' '-D__INT_FAST8_FMTi__="hhi"' -D__INT_FAST8_MAX__=127 '-D__INT_FAST8_TYPE__=signed char' -D__INT_FAST8_WIDTH__=8 '-D__INT_LEAST16_FMTd__="hd"' '-D__INT_LEAST16_FMTi__="hi"' -D__INT_LEAST16_MAX__=32767 -D__INT_LEAST16_TYPE__=short -D__INT_LEAST16_WIDTH__=16 '-D__INT_LEAST32_FMTd__="d"' '-D__INT_LEAST32_FMTi__="i"' -D__INT_LEAST32_MAX__=2147483647 -D__INT_LEAST32_TYPE__=int -D__INT_LEAST32_WIDTH__=32 '-D__INT_LEAST64_FMTd__="lld"' '-D__INT_LEAST64_FMTi__="lli"' -D__INT_LEAST64_MAX__=9223372036854775807LL '-D__INT_LEAST64_TYPE__=long long int' -D__INT_LEAST64_WIDTH__=64 '-D__INT_LEAST8_FMTd__="hhd"' '-D__INT_LEAST8_FMTi__="hhi"' -D__INT_LEAST8_MAX__=127 '-D__INT_LEAST8_TYPE__=signed char' -D__INT_LEAST8_WIDTH__=8 -D__INT_MAX__=2147483647 -D__INT_WIDTH__=32 -D__LAHF_SAHF__=1 -D__LDBL_DECIMAL_DIG__=21 -D__LDBL_DENORM_MIN__=3.64519953188247460253e-4951L -D__LDBL_DIG__=18 -D__LDBL_EPSILON__=1.08420217248550443401e-19L -D__LDBL_HAS_DENORM__=1 -D__LDBL_HAS_INFINITY__=1 -D__LDBL_HAS_QUIET_NAN__=1 -D__LDBL_MANT_DIG__=64 -D__LDBL_MAX_10_EXP__=4932 -D__LDBL_MAX_EXP__=16384 '-D__LDBL_MAX__=1.18973149535723176502e+4932L' '-D__LDBL_MIN_10_EXP__=(-4931)' '-D__LDBL_MIN_EXP__=(-16381)' -D__LDBL_MIN__=3.36210314311209350626e-4932L -D__LITTLE_ENDIAN__=1 -D__LLONG_WIDTH__=64 -D__LONG_LONG_MAX__=9223372036854775807LL -D__LONG_MAX__=9223372036854775807L -D__LONG_WIDTH__=64 -D__LP64__=1 -D__MACH__=1 -D__MMX__=1 -D__NO_INLINE__=1 -D__NO_MATH_ERRNO__=1 -D__NO_MATH_INLINES=1 -D__OBJC_BOOL_IS_BOOL=0 -D__OPENCL_MEMORY_SCOPE_ALL_SVM_DEVICES=3 -D__OPENCL_MEMORY_SCOPE_DEVICE=2 -D__OPENCL_MEMORY_SCOPE_SUB_GROUP=4 -D__OPENCL_MEMORY_SCOPE_WORK_GROUP=1 -D__OPENCL_MEMORY_SCOPE_WORK_ITEM=0 -D__ORDER_BIG_ENDIAN__=4321 -D__ORDER_LITTLE_ENDIAN__=1234 -D__ORDER_PDP_ENDIAN__=3412 -D__PIC__=2 -D__POINTER_WIDTH__=64 -D__PRAGMA_REDEFINE_EXTNAME=1 '-D__PTRDIFF_FMTd__="ld"' '-D__PTRDIFF_FMTi__="li"' -D__PTRDIFF_MAX__=9223372036854775807L '-D__PTRDIFF_TYPE__=long int' -D__PTRDIFF_WIDTH__=64 -D__REGISTER_PREFIX__= -D__SCHAR_MAX__=127 -D__SEG_FS=1 -D__SEG_GS=1 -D__SHRT_MAX__=32767 -D__SHRT_WIDTH__=16 -D__SIG_ATOMIC_MAX__=2147483647 -D__SIG_ATOMIC_WIDTH__=32 -D__SIZEOF_DOUBLE__=8 -D__SIZEOF_FLOAT__=4 -D__SIZEOF_INT128__=16 -D__SIZEOF_INT__=4 -D__SIZEOF_LONG_DOUBLE__=16 -D__SIZEOF_LONG_LONG__=8 -D__SIZEOF_LONG__=8 -D__SIZEOF_POINTER__=8 -D__SIZEOF_PTRDIFF_T__=8 -D__SIZEOF_SHORT__=2 -D__SIZEOF_SIZE_T__=8 -D__SIZEOF_WCHAR_T__=4 -D__SIZEOF_WINT_T__=4 '-D__SIZE_FMTX__="lX"' '-D__SIZE_FMTo__="lo"' '-D__SIZE_FMTu__="lu"' '-D__SIZE_FMTx__="lx"' -D__SIZE_MAX__=18446744073709551615UL '-D__SIZE_TYPE__=long unsigned int' -D__SIZE_WIDTH__=64 -D__SSE2_MATH__=1 -D__SSE2__=1 -D__SSE3__=1 -D__SSE4_1__=1 -D__SSE_MATH__=1 -D__SSE__=1 -D__SSSE3__=1 -D__STDC_NO_THREADS__=1 -D__STDC_UTF_16__=1 -D__STDC_UTF_32__=1 -D__UINT16_C_SUFFIX__= '-D__UINT16_FMTX__="hX"' '-D__UINT16_FMTo__="ho"' '-D__UINT16_FMTu__="hu"' '-D__UINT16_FMTx__="hx"' -D__UINT16_MAX__=65535 '-D__UINT16_TYPE__=unsigned short' -D__UINT32_C_SUFFIX__=U '-D__UINT32_FMTX__="X"' '-D__UINT32_FMTo__="o"' '-D__UINT32_FMTu__="u"' '-D__UINT32_FMTx__="x"' -D__UINT32_MAX__=4294967295U '-D__UINT32_TYPE__=unsigned int' -D__UINT64_C_SUFFIX__=ULL '-D__UINT64_FMTX__="llX"' '-D__UINT64_FMTo__="llo"' '-D__UINT64_FMTu__="llu"' '-D__UINT64_FMTx__="llx"' -D__UINT64_MAX__=18446744073709551615ULL '-D__UINT64_TYPE__=long long unsigned int' -D__UINT8_C_SUFFIX__= '-D__UINT8_FMTX__="hhX"' '-D__UINT8_FMTo__="hho"' '-D__UINT8_FMTu__="hhu"' '-D__UINT8_FMTx__="hhx"' -D__UINT8_MAX__=255 '-D__UINT8_TYPE__=unsigned char' -D__UINTMAX_C_SUFFIX__=UL '-D__UINTMAX_FMTX__="lX"' '-D__UINTMAX_FMTo__="lo"' '-D__UINTMAX_FMTu__="lu"' '-D__UINTMAX_FMTx__="lx"' -D__UINTMAX_MAX__=18446744073709551615UL '-D__UINTMAX_TYPE__=long unsigned int' -D__UINTMAX_WIDTH__=64 '-D__UINTPTR_FMTX__="lX"' '-D__UINTPTR_FMTo__="lo"' '-D__UINTPTR_FMTu__="lu"' '-D__UINTPTR_FMTx__="lx"' -D__UINTPTR_MAX__=18446744073709551615UL '-D__UINTPTR_TYPE__=long unsigned int' -D__UINTPTR_WIDTH__=64 '-D__UINT_FAST16_FMTX__="hX"' '-D__UINT_FAST16_FMTo__="ho"' '-D__UINT_FAST16_FMTu__="hu"' '-D__UINT_FAST16_FMTx__="hx"' -D__UINT_FAST16_MAX__=65535 '-D__UINT_FAST16_TYPE__=unsigned short' '-D__UINT_FAST32_FMTX__="X"' '-D__UINT_FAST32_FMTo__="o"' '-D__UINT_FAST32_FMTu__="u"' '-D__UINT_FAST32_FMTx__="x"' -D__UINT_FAST32_MAX__=4294967295U '-D__UINT_FAST32_TYPE__=unsigned int' '-D__UINT_FAST64_FMTX__="llX"' '-D__UINT_FAST64_FMTo__="llo"' '-D__UINT_FAST64_FMTu__="llu"' '-D__UINT_FAST64_FMTx__="llx"' -D__UINT_FAST64_MAX__=18446744073709551615ULL '-D__UINT_FAST64_TYPE__=long long unsigned int' '-D__UINT_FAST8_FMTX__="hhX"' '-D__UINT_FAST8_FMTo__="hho"' '-D__UINT_FAST8_FMTu__="hhu"' '-D__UINT_FAST8_FMTx__="hhx"' -D__UINT_FAST8_MAX__=255 '-D__UINT_FAST8_TYPE__=unsigned char' '-D__UINT_LEAST16_FMTX__="hX"' '-D__UINT_LEAST16_FMTo__="ho"' '-D__UINT_LEAST16_FMTu__="hu"' '-D__UINT_LEAST16_FMTx__="hx"' -D__UINT_LEAST16_MAX__=65535 '-D__UINT_LEAST16_TYPE__=unsigned short' '-D__UINT_LEAST32_FMTX__="X"' '-D__UINT_LEAST32_FMTo__="o"' '-D__UINT_LEAST32_FMTu__="u"' '-D__UINT_LEAST32_FMTx__="x"' -D__UINT_LEAST32_MAX__=4294967295U '-D__UINT_LEAST32_TYPE__=unsigned int' '-D__UINT_LEAST64_FMTX__="llX"' '-D__UINT_LEAST64_FMTo__="llo"' '-D__UINT_LEAST64_FMTu__="llu"' '-D__UINT_LEAST64_FMTx__="llx"' -D__UINT_LEAST64_MAX__=18446744073709551615ULL '-D__UINT_LEAST64_TYPE__=long long unsigned int' '-D__UINT_LEAST8_FMTX__="hhX"' '-D__UINT_LEAST8_FMTo__="hho"' '-D__UINT_LEAST8_FMTu__="hhu"' '-D__UINT_LEAST8_FMTx__="hhx"' -D__UINT_LEAST8_MAX__=255 '-D__UINT_LEAST8_TYPE__=unsigned char' -D__USER_LABEL_PREFIX__=_ '-D__VERSION__="Apple LLVM 16.0.0 (clang-1600.0.26.6)"' -D__WCHAR_MAX__=2147483647 -D__WCHAR_TYPE__=int -D__WCHAR_WIDTH__=32 -D__WINT_MAX__=2147483647 -D__WINT_TYPE__=int -D__WINT_WIDTH__=32 -D__amd64=1 -D__amd64__=1 -D__apple_build_version__=16000026 '-D__block=__attribute__((__blocks__(byref)))' -D__clang__=1 '-D__clang_literal_encoding__="UTF-8"' -D__clang_major__=16 -D__clang_minor__=0 -D__clang_patchlevel__=0 '-D__clang_version__="16.0.0 (clang-1600.0.26.6)"' '-D__clang_wide_literal_encoding__="UTF-32"' -D__code_model_small__=1 -D__core2=1 -D__core2__=1 -D__llvm__=1 -D__nonnull=_Nonnull -D__null_unspecified=_Null_unspecified -D__nullable=_Nullable -D__pic__=2 '-D__seg_fs=__attribute__((address_space(257)))' '-D__seg_gs=__attribute__((address_space(256)))' -D__strong= -D__tune_core2__=1 -D__unsafe_unretained= '-D__weak=__attribute__((objc_gc(weak)))' -D__x86_64=1 -D__x86_64__=1 '-D__private_extern__=extern __attribute__((visibility("hidden")))' --isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include --blocks -D__GCC_HAVE_DWARF2_CFI_ASM=1 -I/usr/local/include -I/Library/Developer/CommandLineTools/usr/lib/clang/16/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -I/Library/Developer/CommandLineTools/usr/include -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks -- code_036a18f0.c
[E 16:35:36 42702] Starting compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp/compilations/54/38665158_0.trap.br
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_036a18f0.c
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdlib.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdlib.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/Availability.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityVersions.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternal.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternalLegacy.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/cdefs.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_symbol_aliasing.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_posix_availability.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/wait.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_pid_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_id_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/signal.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/appleapiopts.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/signal.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/signal.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_mcontext.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_mcontext.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/machine/_structs.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/i386/_structs.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int8_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int16_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int32_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int64_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int8_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int16_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int32_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int64_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_intptr_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uintptr_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_attr_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigaltstack.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ucontext.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigset_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uid_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/resource.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/usr/lib/clang/16/include/stdint.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdint.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint8_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint16_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint32_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint64_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_intmax_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uintmax_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_timeval.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/__endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/_OSByteOrder.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/i386/_OSByteOrder.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/alloca.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ct_rune_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rune_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_wchar_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc_type.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_ptrcheck.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_abort.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_dev_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_mode_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/string.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_string.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rsize_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_errno_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ssize_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_strings.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_strings.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_common.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_string.h
[E 16:35:37 42702] Creating trap tarball /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_036a18f0.c.8d78a191_0.trap.tar.br
[E 16:35:37 42702] Emitting trap files for code_036a18f0.c
[E 16:35:37 42702] Opening existencedb in /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/existencedb/db
[E 16:35:37 42702] Wrote 85 files to /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_036a18f0.c.8d78a191_0.trap.tar.br
[E 16:35:37 42702] Finished compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp/compilations/54/38665158_0.trap.br
[E 16:35:37 42702] Marking C compilation as happened.
[E 16:35:37 42702] Setting tag c-compilation-happened
[E 16:35:37 42702] Starting from CODEQL_TRACER_DB_ID 000000000000A6CC_0000000000000001
[E 16:35:37 42702] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:37 42702] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:37 42702] Set tag for 000000000000A6CC_0000000000000001
[E 16:35:37 42702] Set tag for 000000000000A6B9_0000000000000002
[E 16:35:37 42702] Set tag for 000000000000A6B9_0000000000000001
[E 16:35:37 42702] Set tag for 000000000000A6B3_0000000000000001
[E 16:35:37 42702] Set tag for 000000000000A6B1_0000000000000001
[E 16:35:37 42702] Set tag for 000000000000A6A4_0000000000000004
[E 16:35:37 42702] Set tag for 000000000000A6A4_0000000000000003
[E 16:35:37 42702] Set tag for 000000000000A6A4_0000000000000002
[E 16:35:37 42702] Set tag for root
[E 16:35:37 42702] Unlocking DB
[E 16:35:37 42702] Unlocked DB

[T 16:35:19 42660] CodeQL CLI version 2.11.2
[T 16:35:19 42660] Initializing tracer.
[T 16:35:19 42660] Initialising tags...
[T 16:35:19 42660] ID set to 000000000000A6A4_0000000000000001 (parent root)
[T 16:35:19 42660] Initializing tracer.
[T 16:35:19 42660] Initialising tags...
[T 16:35:19 42660] ID set to 000000000000A6A4_0000000000000002 (parent root)
[T 16:35:19 42660] Warning: SEMMLE_EXEC and SEMMLE_EXECP not set. Falling back to path lookup on argv[0].
[T 16:35:19 42660] ==== Candidate to intercept: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx (canonical: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx) ====
[T 16:35:19 42660] Executing the following tracer actions:
[T 16:35:19 42660] Tracer actions:
[T 16:35:19 42660] pre_invocations(0)
[T 16:35:19 42660] post_invocations(0)
[T 16:35:19 42660] trace_languages(1): [cpp]
[T 16:35:19 42661] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/usr/bin/make.semmle.0000A6A4.slice.x86_64: replacing existing signature
[T 16:35:20 42660] Initializing tracer.
[T 16:35:20 42660] Initialising tags...
[T 16:35:20 42660] ID set to 000000000000A6A4_0000000000000003 (parent 000000000000A6A4_0000000000000002)
[T 16:35:20 42660] ==== Candidate to intercept: /usr/bin/make (canonical: /usr/bin/make) ====
[T 16:35:20 42660] Executing the following tracer actions:
[T 16:35:20 42660] Tracer actions:
[T 16:35:20 42660] pre_invocations(0)
[T 16:35:20 42660] post_invocations(0)
[T 16:35:20 42660] trace_languages(1): [cpp]
[T 16:35:20 42665] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.0000A6A4.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.0000A6A4.slice.arm64: replacing existing signature
[T 16:35:20 42660] Initializing tracer.
[T 16:35:20 42660] Initialising tags...
[T 16:35:20 42660] ID set to 000000000000A6A4_0000000000000004 (parent 000000000000A6A4_0000000000000003)
[T 16:35:20 42660] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/make (canonical: /Library/Developer/CommandLineTools/usr/bin/make) ====
[T 16:35:20 42660] Executing the following tracer actions:
[T 16:35:20 42660] Tracer actions:
[T 16:35:20 42660] pre_invocations(0)
[T 16:35:20 42660] post_invocations(0)
[T 16:35:20 42660] trace_languages(1): [cpp]
[T 16:35:20 42670] Attempting to switch stdout/stderr to 4...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/usr/bin/gcc.semmle.0000A6A4.slice.x86_64: replacing existing signature
[T 16:35:21 42673] Initializing tracer.
[T 16:35:21 42673] Initialising tags...
[T 16:35:21 42673] ID set to 000000000000A6B1_0000000000000001 (parent 000000000000A6A4_0000000000000004)
[T 16:35:21 42673] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 16:35:21 42673] Lua: === Intercepted call to /usr/bin/gcc ===
[T 16:35:21 42673] Executing the following tracer actions:
[T 16:35:21 42673] Tracer actions:
[T 16:35:21 42673] pre_invocations(0)
[T 16:35:21 42673] post_invocations(1)
[T 16:35:21 42673] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, code_036a18f0.c, -o, code_036a18f0]
[T 16:35:21 42673] trace_languages(1): [cpp]
[T 16:35:21 42676] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.0000A6B3.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.0000A6B3.slice.arm64: replacing existing signature
[T 16:35:21 42675] Initializing tracer.
[T 16:35:21 42675] Initialising tags...
[T 16:35:21 42675] ID set to 000000000000A6B3_0000000000000001 (parent 000000000000A6B1_0000000000000001)
[T 16:35:21 42675] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:21 42675] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:21 42675] Executing the following tracer actions:
[T 16:35:21 42675] Tracer actions:
[T 16:35:21 42675] pre_invocations(0)
[T 16:35:21 42675] post_invocations(1)
[T 16:35:21 42675] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, code_036a18f0.c, -o, code_036a18f0]
[T 16:35:21 42675] trace_languages(1): [cpp]
[T 16:35:21 42682] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/usr/bin/xcrun.semmle.0000A6B9.slice.x86_64: replacing existing signature
[T 16:35:22 42681] Initializing tracer.
[T 16:35:22 42681] Initialising tags...
[T 16:35:22 42681] ID set to 000000000000A6B9_0000000000000001 (parent 000000000000A6B3_0000000000000001)
[T 16:35:22 42681] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:22 42681] Executing the following tracer actions:
[T 16:35:22 42681] Tracer actions:
[T 16:35:22 42681] pre_invocations(0)
[T 16:35:22 42681] post_invocations(0)
[T 16:35:22 42681] trace_languages(1): [cpp]
[T 16:35:22 42686] Attempting to switch stdout/stderr to 3...
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.0000A6B9.slice.x86_64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.0000A6B9.slice.x86_64: replacing existing signature
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.0000A6B9.slice.arm64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.0000A6B9.slice.arm64: replacing existing signature
[T 16:35:33 42681] Initializing tracer.
[T 16:35:33 42681] Initialising tags...
[T 16:35:33 42681] ID set to 000000000000A6B9_0000000000000002 (parent 000000000000A6B9_0000000000000001)
[T 16:35:33 42681] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:33 42681] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:33 42681] Executing the following tracer actions:
[T 16:35:33 42681] Tracer actions:
[T 16:35:33 42681] pre_invocations(0)
[T 16:35:33 42681] post_invocations(1)
[T 16:35:33 42681] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, code_036a18f0.c, -o, code_036a18f0]
[T 16:35:33 42681] trace_languages(1): [cpp]
[T 16:35:33 42700] Initializing tracer.
[T 16:35:33 42700] Initialising tags...
[T 16:35:33 42700] ID set to 000000000000A6CC_0000000000000001 (parent 000000000000A6B9_0000000000000002)
[T 16:35:33 42700] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:33 42700] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:33 42700] Executing the following tracer actions:
[T 16:35:33 42700] Tracer actions:
[T 16:35:33 42700] pre_invocations(0)
[T 16:35:33 42700] post_invocations(1)
[T 16:35:33 42700] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -dumpdir, code_036a18f0-, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, code_036a18f0.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_036a18f0-658fd6.o, -x, c, code_036a18f0.c]
[T 16:35:33 42700] trace_languages(1): [cpp]
[T 16:35:33 42702] Attempting to switch stdout/stderr to 4...
[T 16:35:33 42703] Attempting to switch stdout/stderr to 4...
[T 16:35:34 42702] Initializing tracer.
[T 16:35:34 42702] Initialising tags...
[T 16:35:34 42702] ID set to 000000000000A6CE_0000000000000001 (parent 000000000000A6CC_0000000000000001)
[E 16:35:34 42702] CodeQL C/C++ Extractor 2.11.2
[E 16:35:34 42702] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:34 42702] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -dumpdir code_036a18f0- -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name code_036a18f0.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon '-clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation' -fno-odr-hash-protocols '-clang-vendor-feature=+enableAggressiveVLAFolding' '-clang-vendor-feature=+revert09abecef7bbf' '-clang-vendor-feature=+thisNoAlignAttr' '-clang-vendor-feature=+thisNoNullAttr' '-clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError' -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_036a18f0-658fd6.o -x c code_036a18f0.c
[T 16:35:35 42707] Initializing tracer.
[T 16:35:35 42707] Initialising tags...
[T 16:35:35 42707] ID set to 000000000000A6D3_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:35 42707] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:35 42707] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:35 42707] Executing the following tracer actions:
[T 16:35:35 42707] Tracer actions:
[T 16:35:35 42707] pre_invocations(0)
[T 16:35:35 42707] post_invocations(1)
[T 16:35:35 42707] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 16:35:35 42707] trace_languages(1): [cpp]
[T 16:35:35 42709] Attempting to switch stdout/stderr to 7...
[T 16:35:35 42709] Initializing tracer.
[T 16:35:35 42709] Initialising tags...
[T 16:35:35 42709] ID set to 000000000000A6D5_0000000000000001 (parent 000000000000A6D3_0000000000000001)
[E 16:35:35 42709] Mimicry classification suppression detected; exiting.
[T 16:35:35 42707] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:35 42710] Initializing tracer.
[T 16:35:35 42710] Initialising tags...
[T 16:35:35 42710] ID set to 000000000000A6D6_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:35 42710] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:35 42710] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:35 42710] Executing the following tracer actions:
[T 16:35:35 42710] Tracer actions:
[T 16:35:35 42710] pre_invocations(0)
[T 16:35:35 42710] post_invocations(1)
[T 16:35:35 42710] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 16:35:35 42710] trace_languages(1): [cpp]
[T 16:35:35 42712] Attempting to switch stdout/stderr to 7...
[T 16:35:35 42712] Initializing tracer.
[T 16:35:35 42712] Initialising tags...
[T 16:35:35 42712] ID set to 000000000000A6D8_0000000000000001 (parent 000000000000A6D6_0000000000000001)
[E 16:35:35 42712] Mimicry classification suppression detected; exiting.
[T 16:35:35 42710] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:35 42713] Initializing tracer.
[T 16:35:35 42713] Initialising tags...
[T 16:35:35 42713] ID set to 000000000000A6D9_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:35 42713] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:35 42713] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:35 42713] Executing the following tracer actions:
[T 16:35:35 42713] Tracer actions:
[T 16:35:35 42713] pre_invocations(0)
[T 16:35:35 42713] post_invocations(1)
[T 16:35:35 42713] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -nostdsysteminc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42702_467257.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42702_466795.c]
[T 16:35:35 42713] trace_languages(1): [cpp]
[T 16:35:35 42715] Attempting to switch stdout/stderr to 7...
[T 16:35:35 42715] Initializing tracer.
[T 16:35:35 42715] Initialising tags...
[T 16:35:35 42715] ID set to 000000000000A6DB_0000000000000001 (parent 000000000000A6D9_0000000000000001)
[E 16:35:35 42715] Mimicry classification suppression detected; exiting.
[T 16:35:35 42713] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Unrecognised command line argument -dumpdir
Warning: Unrecognised command line argument -clear-ast-before-backend
Warning: Unrecognised command line argument -discard-value-names
Warning: Unrecognised command line argument -target-sdk-version=15.2
Warning: Unrecognised command line argument -tune-cpu
Warning: Unrecognised command line argument -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation
Warning: Unrecognised command line argument -clang-vendor-feature=+enableAggressiveVLAFolding
Warning: Unrecognised command line argument -clang-vendor-feature=+revert09abecef7bbf
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoAlignAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoNullAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError
[E 16:35:35 42702] Checking whether C compilation already happened.
[E 16:35:35 42702] Checking for tag c-compilation-happened
[E 16:35:35 42702] Checking CODEQL_TRACER_DB_ID 000000000000A6CC_0000000000000001
[E 16:35:35 42702] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Unlocking DB
[E 16:35:35 42702] Unlocked DB
[E 16:35:35 42702] Looks like C compilation didn't already happen.
[E 16:35:35 42702] Checking whether C compilation has been attempted.
[E 16:35:35 42702] Checking for tag c-compilation-attempted
[E 16:35:35 42702] Checking CODEQL_TRACER_DB_ID 000000000000A6CC_0000000000000001
[E 16:35:35 42702] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Unlocking DB
[E 16:35:35 42702] Unlocked DB
[E 16:35:35 42702] Marking C compilation as attempted.
[E 16:35:35 42702] Setting tag c-compilation-attempted
[E 16:35:35 42702] Starting from CODEQL_TRACER_DB_ID 000000000000A6CC_0000000000000001
[E 16:35:35 42702] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42702] Set tag for 000000000000A6CC_0000000000000001
[E 16:35:35 42702] Set tag for 000000000000A6B9_0000000000000002
[E 16:35:35 42702] Set tag for 000000000000A6B9_0000000000000001
[E 16:35:35 42702] Set tag for 000000000000A6B3_0000000000000001
[E 16:35:35 42702] Set tag for 000000000000A6B1_0000000000000001
[E 16:35:35 42702] Set tag for 000000000000A6A4_0000000000000004
[E 16:35:35 42702] Set tag for 000000000000A6A4_0000000000000003
[E 16:35:35 42702] Set tag for 000000000000A6A4_0000000000000002
[E 16:35:35 42702] Set tag for root
[E 16:35:35 42702] Unlocking DB
[E 16:35:35 42702] Unlocked DB
[E 16:35:35 42702] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:35 42702] Warning[extractor-c++]: In canonicalise_path: realpath failed
Excluded code_036a18f0- because it is an object
Excluded generic because it is an object
[T 16:35:35 42716] Initializing tracer.
[T 16:35:35 42716] Initialising tags...
[T 16:35:35 42716] ID set to 000000000000A6DC_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:35 42716] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:35 42716] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:35 42716] Executing the following tracer actions:
[T 16:35:35 42716] Tracer actions:
[T 16:35:35 42716] pre_invocations(0)
[T 16:35:35 42716] post_invocations(1)
[T 16:35:35 42716] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -v, -fsyntax-only, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42702_565972]
[T 16:35:35 42716] trace_languages(1): [cpp]
[T 16:35:35 42718] Attempting to switch stdout/stderr to 7...
[T 16:35:35 42718] Initializing tracer.
[T 16:35:35 42718] Initialising tags...
[T 16:35:35 42718] ID set to 000000000000A6DE_0000000000000001 (parent 000000000000A6DC_0000000000000001)
[E 16:35:35 42718] Mimicry classification suppression detected; exiting.
[T 16:35:35 42716] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:35 42719] Initializing tracer.
[T 16:35:35 42719] Initialising tags...
[T 16:35:35 42719] ID set to 000000000000A6DF_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:35 42719] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:35 42719] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:35 42719] Executing the following tracer actions:
[T 16:35:35 42719] Tracer actions:
[T 16:35:35 42719] pre_invocations(0)
[T 16:35:35 42719] post_invocations(1)
[T 16:35:35 42719] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, -dM, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_5_42702_651381]
[T 16:35:35 42719] trace_languages(1): [cpp]
[T 16:35:35 42721] Attempting to switch stdout/stderr to 7...
[T 16:35:35 42721] Initializing tracer.
[T 16:35:35 42721] Initialising tags...
[T 16:35:35 42721] ID set to 000000000000A6E1_0000000000000001 (parent 000000000000A6DF_0000000000000001)
[E 16:35:35 42721] Mimicry classification suppression detected; exiting.
[T 16:35:35 42719] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Apple version 160000 of clang is too new; mapping it to 14.0.0.
[T 16:35:35 42722] Initializing tracer.
[T 16:35:35 42722] Initialising tags...
[T 16:35:35 42722] ID set to 000000000000A6E2_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:35 42722] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:35 42722] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:35 42722] Executing the following tracer actions:
[T 16:35:35 42722] Tracer actions:
[T 16:35:35 42722] pre_invocations(0)
[T 16:35:35 42722] post_invocations(1)
[T 16:35:35 42722] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_6_42702_730292]
[T 16:35:35 42722] trace_languages(1): [cpp]
[T 16:35:35 42724] Attempting to switch stdout/stderr to 7...
[T 16:35:35 42724] Initializing tracer.
[T 16:35:35 42724] Initialising tags...
[T 16:35:35 42724] ID set to 000000000000A6E4_0000000000000001 (parent 000000000000A6E2_0000000000000001)
[E 16:35:35 42724] Mimicry classification suppression detected; exiting.
[T 16:35:35 42722] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:35 42725] Initializing tracer.
[T 16:35:35 42725] Initialising tags...
[T 16:35:35 42725] ID set to 000000000000A6E5_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:35 42725] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:35 42725] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:35 42725] Executing the following tracer actions:
[T 16:35:35 42725] Tracer actions:
[T 16:35:35 42725] pre_invocations(0)
[T 16:35:35 42725] post_invocations(1)
[T 16:35:35 42725] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c-header, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_7_42702_813833.h.gch, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_7_42702_813833.h]
[T 16:35:35 42725] trace_languages(1): [cpp]
[T 16:35:35 42727] Attempting to switch stdout/stderr to 7...
[T 16:35:35 42727] Initializing tracer.
[T 16:35:35 42727] Initialising tags...
[T 16:35:35 42727] ID set to 000000000000A6E7_0000000000000001 (parent 000000000000A6E5_0000000000000001)
[E 16:35:35 42727] Mimicry classification suppression detected; exiting.
[T 16:35:35 42725] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:35 42728] Initializing tracer.
[T 16:35:35 42728] Initialising tags...
[T 16:35:35 42728] ID set to 000000000000A6E8_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:35 42728] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:35 42728] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:35 42728] Executing the following tracer actions:
[T 16:35:35 42728] Tracer actions:
[T 16:35:35 42728] pre_invocations(0)
[T 16:35:35 42728] post_invocations(1)
[T 16:35:35 42728] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -fno-color-diagnostics, --help]
[T 16:35:35 42728] trace_languages(1): [cpp]
[T 16:35:35 42730] Attempting to switch stdout/stderr to 7...
[T 16:35:35 42730] Initializing tracer.
[T 16:35:35 42730] Initialising tags...
[T 16:35:35 42730] ID set to 000000000000A6EA_0000000000000001 (parent 000000000000A6E8_0000000000000001)
[E 16:35:35 42730] CodeQL C/C++ Extractor 2.11.2
[E 16:35:35 42730] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:35 42730] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -mrelax-all -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -fno-odr-hash-protocols -x c -fno-color-diagnostics --help
[E 16:35:35 42730] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:35 42730] Checking whether C compilation already happened.
[E 16:35:35 42730] Checking for tag c-compilation-happened
[E 16:35:35 42730] Checking CODEQL_TRACER_DB_ID 000000000000A6E8_0000000000000001
[E 16:35:35 42730] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Unlocking DB
[E 16:35:35 42730] Unlocked DB
[E 16:35:35 42730] Looks like C compilation didn't already happen.
[E 16:35:35 42730] Checking whether C compilation has been attempted.
[E 16:35:35 42730] Checking for tag c-compilation-attempted
[E 16:35:35 42730] Checking CODEQL_TRACER_DB_ID 000000000000A6E8_0000000000000001
[E 16:35:35 42730] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Unlocking DB
[E 16:35:35 42730] Unlocked DB
[E 16:35:35 42730] Marking C compilation as attempted.
[E 16:35:35 42730] Setting tag c-compilation-attempted
[E 16:35:35 42730] Starting from CODEQL_TRACER_DB_ID 000000000000A6E8_0000000000000001
[E 16:35:35 42730] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:35 42730] Set tag for 000000000000A6E8_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6CE_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6CC_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6B9_0000000000000002
[E 16:35:35 42730] Set tag for 000000000000A6B9_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6B3_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6B1_0000000000000001
[E 16:35:35 42730] Set tag for 000000000000A6A4_0000000000000004
[E 16:35:35 42730] Set tag for 000000000000A6A4_0000000000000003
[E 16:35:35 42730] Set tag for 000000000000A6A4_0000000000000002
[E 16:35:35 42730] Set tag for root
[E 16:35:35 42730] Unlocking DB
[E 16:35:35 42730] Unlocked DB
[E 16:35:35 42730] 0 file groups; exiting.
[T 16:35:35 42728] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:36 42731] Initializing tracer.
[T 16:35:36 42731] Initialising tags...
[T 16:35:36 42731] ID set to 000000000000A6EB_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:36 42731] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:36 42731] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:36 42731] Executing the following tracer actions:
[T 16:35:36 42731] Tracer actions:
[T 16:35:36 42731] pre_invocations(0)
[T 16:35:36 42731] post_invocations(1)
[T 16:35:36 42731] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_7_42702_813833.h.gch, -ast-dump, -]
[T 16:35:36 42731] trace_languages(1): [cpp]
[T 16:35:36 42733] Attempting to switch stdout/stderr to 7...
[T 16:35:36 42733] Initializing tracer.
[T 16:35:36 42733] Initialising tags...
[T 16:35:36 42733] ID set to 000000000000A6ED_0000000000000001 (parent 000000000000A6EB_0000000000000001)
[E 16:35:36 42733] Mimicry classification suppression detected; exiting.
[T 16:35:36 42731] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, int *)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, unsigned short)'
Warning: Could not parse function type 'float (__bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'void (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(int)))) int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(float)))) float, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(short)))) short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(short)))) short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(short)))) short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__fp16 (__fp16, int)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Failed to find information about possible built-in __VA_OPT__
Warning: Failed to find information about possible built-in __blocks__
Warning: Failed to find information about possible built-in __builtin___fprintf_chk
Warning: Failed to find information about possible built-in __builtin___vfprintf_chk
Warning: Failed to find information about possible built-in __builtin_fprintf
Warning: Failed to find information about possible built-in __builtin_fscanf
Warning: Failed to find information about possible built-in __builtin_vfprintf
Warning: Failed to find information about possible built-in __builtin_vfscanf
Warning: Failed to find information about possible built-in __sigsetjmp
Warning: Failed to find information about possible built-in _longjmp
Warning: Failed to find information about possible built-in _setjmp
Warning: Ignored 11 possible built-ins
Warning: Throwing away all 2906 builtins because of 229 bad parses.
[T 16:35:36 42734] Initializing tracer.
[T 16:35:36 42734] Initialising tags...
[T 16:35:36 42734] ID set to 000000000000A6EE_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:36 42734] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:36 42734] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:36 42734] Executing the following tracer actions:
[T 16:35:36 42734] Tracer actions:
[T 16:35:36 42734] pre_invocations(0)
[T 16:35:36 42734] post_invocations(1)
[T 16:35:36 42734] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_9_42702_345085.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_8_42702_344514.c]
[T 16:35:36 42734] trace_languages(1): [cpp]
[T 16:35:36 42736] Attempting to switch stdout/stderr to 7...
[T 16:35:36 42736] Initializing tracer.
[T 16:35:36 42736] Initialising tags...
[T 16:35:36 42736] ID set to 000000000000A6F0_0000000000000001 (parent 000000000000A6EE_0000000000000001)
[E 16:35:36 42736] Mimicry classification suppression detected; exiting.
[T 16:35:36 42734] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:36 42737] Initializing tracer.
[T 16:35:36 42737] Initialising tags...
[T 16:35:36 42737] ID set to 000000000000A6F1_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:36 42737] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:36 42737] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:36 42737] Executing the following tracer actions:
[T 16:35:36 42737] Tracer actions:
[T 16:35:36 42737] pre_invocations(0)
[T 16:35:36 42737] post_invocations(1)
[T 16:35:36 42737] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_11_42702_439965.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_10_42702_439678.c]
[T 16:35:36 42737] trace_languages(1): [cpp]
[T 16:35:36 42739] Attempting to switch stdout/stderr to 7...
[T 16:35:36 42739] Initializing tracer.
[T 16:35:36 42739] Initialising tags...
[T 16:35:36 42739] ID set to 000000000000A6F3_0000000000000001 (parent 000000000000A6F1_0000000000000001)
[E 16:35:36 42739] Mimicry classification suppression detected; exiting.
[T 16:35:36 42737] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:36 42740] Initializing tracer.
[T 16:35:36 42740] Initialising tags...
[T 16:35:36 42740] ID set to 000000000000A6F4_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:36 42740] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:36 42740] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:36 42740] Executing the following tracer actions:
[T 16:35:36 42740] Tracer actions:
[T 16:35:36 42740] pre_invocations(0)
[T 16:35:36 42740] post_invocations(1)
[T 16:35:36 42740] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_13_42702_533917.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_12_42702_533415.c]
[T 16:35:36 42740] trace_languages(1): [cpp]
[T 16:35:36 42742] Attempting to switch stdout/stderr to 7...
[T 16:35:36 42742] Initializing tracer.
[T 16:35:36 42742] Initialising tags...
[T 16:35:36 42742] ID set to 000000000000A6F6_0000000000000001 (parent 000000000000A6F4_0000000000000001)
[E 16:35:36 42742] Mimicry classification suppression detected; exiting.
[T 16:35:36 42740] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:36 42743] Initializing tracer.
[T 16:35:36 42743] Initialising tags...
[T 16:35:36 42743] ID set to 000000000000A6F7_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:36 42743] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:36 42743] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:36 42743] Executing the following tracer actions:
[T 16:35:36 42743] Tracer actions:
[T 16:35:36 42743] pre_invocations(0)
[T 16:35:36 42743] post_invocations(1)
[T 16:35:36 42743] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_15_42702_634306.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_14_42702_633973.c]
[T 16:35:36 42743] trace_languages(1): [cpp]
[T 16:35:36 42745] Attempting to switch stdout/stderr to 7...
[T 16:35:36 42745] Initializing tracer.
[T 16:35:36 42745] Initialising tags...
[T 16:35:36 42745] ID set to 000000000000A6F9_0000000000000001 (parent 000000000000A6F7_0000000000000001)
[E 16:35:36 42745] Mimicry classification suppression detected; exiting.
[T 16:35:36 42743] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:36 42746] Initializing tracer.
[T 16:35:36 42746] Initialising tags...
[T 16:35:36 42746] ID set to 000000000000A6FA_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:36 42746] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:36 42746] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:36 42746] Executing the following tracer actions:
[T 16:35:36 42746] Tracer actions:
[T 16:35:36 42746] pre_invocations(0)
[T 16:35:36 42746] post_invocations(1)
[T 16:35:36 42746] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_17_42702_726035.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_16_42702_725752.c]
[T 16:35:36 42746] trace_languages(1): [cpp]
[T 16:35:36 42748] Attempting to switch stdout/stderr to 7...
[T 16:35:36 42748] Initializing tracer.
[T 16:35:36 42748] Initialising tags...
[T 16:35:36 42748] ID set to 000000000000A6FC_0000000000000001 (parent 000000000000A6FA_0000000000000001)
[E 16:35:36 42748] Mimicry classification suppression detected; exiting.
[T 16:35:36 42746] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:36 42749] Initializing tracer.
[T 16:35:36 42749] Initialising tags...
[T 16:35:36 42749] ID set to 000000000000A6FD_0000000000000001 (parent 000000000000A6CE_0000000000000001)
[T 16:35:36 42749] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:36 42749] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:36 42749] Executing the following tracer actions:
[T 16:35:36 42749] Tracer actions:
[T 16:35:36 42749] pre_invocations(0)
[T 16:35:36 42749] post_invocations(1)
[T 16:35:36 42749] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_19_42702_817939.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_18_42702_817641.c]
[T 16:35:36 42749] trace_languages(1): [cpp]
[T 16:35:36 42751] Attempting to switch stdout/stderr to 7...
[T 16:35:36 42751] Initializing tracer.
[T 16:35:36 42751] Initialising tags...
[T 16:35:36 42751] ID set to 000000000000A6FF_0000000000000001 (parent 000000000000A6FD_0000000000000001)
[E 16:35:36 42751] Mimicry classification suppression detected; exiting.
[T 16:35:36 42749] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 16:35:36 42702] Processed command line: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --trapfolder '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp' --src_archive '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src' --mimic_config '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/compiler_mimic_cache/a1592c9a78e1' --object_filename /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_036a18f0-658fd6.o -w --error_limit 1000 --disable_system_macros --variadic_macros --gcc --clang_version 140000 --gnu_version 40801 --has_feature_vector 111111111111111100000000000000000000000000000000000000000000000000011 --clang --target linux_x86_64 -D_LP64=1 -D__APPLE_CC__=6000 -D__APPLE__=1 -D__ATOMIC_ACQUIRE=2 -D__ATOMIC_ACQ_REL=4 -D__ATOMIC_CONSUME=1 -D__ATOMIC_RELAXED=0 -D__ATOMIC_RELEASE=3 -D__ATOMIC_SEQ_CST=5 -D__BIGGEST_ALIGNMENT__=16 -D__BITINT_MAXWIDTH__=8388608 -D__BLOCKS__=1 -D__BOOL_WIDTH__=8 -D__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__ -D__CHAR_BIT__=8 -D__CLANG_ATOMIC_BOOL_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR_LOCK_FREE=2 -D__CLANG_ATOMIC_INT_LOCK_FREE=2 -D__CLANG_ATOMIC_LLONG_LOCK_FREE=2 -D__CLANG_ATOMIC_LONG_LOCK_FREE=2 -D__CLANG_ATOMIC_POINTER_LOCK_FREE=2 -D__CLANG_ATOMIC_SHORT_LOCK_FREE=2 -D__CLANG_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__CONSTANT_CFSTRINGS__=1 -D__DBL_DECIMAL_DIG__=17 -D__DBL_DENORM_MIN__=4.9406564584124654e-324 -D__DBL_DIG__=15 -D__DBL_EPSILON__=2.2204460492503131e-16 -D__DBL_HAS_DENORM__=1 -D__DBL_HAS_INFINITY__=1 -D__DBL_HAS_QUIET_NAN__=1 -D__DBL_MANT_DIG__=53 -D__DBL_MAX_10_EXP__=308 -D__DBL_MAX_EXP__=1024 '-D__DBL_MAX__=1.7976931348623157e+308' '-D__DBL_MIN_10_EXP__=(-307)' '-D__DBL_MIN_EXP__=(-1021)' -D__DBL_MIN__=2.2250738585072014e-308 -D__DECIMAL_DIG__=__LDBL_DECIMAL_DIG__ -D__DYNAMIC__=1 -D__ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__=150000 -D__ENVIRONMENT_OS_VERSION_MIN_REQUIRED__=150000 -D__FINITE_MATH_ONLY__=0 -D__FLT16_DECIMAL_DIG__=5 -D__FLT16_DENORM_MIN__=5.9604644775390625e-8F16 -D__FLT16_DIG__=3 -D__FLT16_EPSILON__=9.765625e-4F16 -D__FLT16_HAS_DENORM__=1 -D__FLT16_HAS_INFINITY__=1 -D__FLT16_HAS_QUIET_NAN__=1 -D__FLT16_MANT_DIG__=11 -D__FLT16_MAX_10_EXP__=4 -D__FLT16_MAX_EXP__=16 '-D__FLT16_MAX__=6.5504e+4F16' '-D__FLT16_MIN_10_EXP__=(-4)' '-D__FLT16_MIN_EXP__=(-13)' -D__FLT16_MIN__=6.103515625e-5F16 -D__FLT_DECIMAL_DIG__=9 -D__FLT_DENORM_MIN__=1.40129846e-45F -D__FLT_DIG__=6 -D__FLT_EPSILON__=1.19209290e-7F -D__FLT_HAS_DENORM__=1 -D__FLT_HAS_INFINITY__=1 -D__FLT_HAS_QUIET_NAN__=1 -D__FLT_MANT_DIG__=24 -D__FLT_MAX_10_EXP__=38 -D__FLT_MAX_EXP__=128 '-D__FLT_MAX__=3.40282347e+38F' '-D__FLT_MIN_10_EXP__=(-37)' '-D__FLT_MIN_EXP__=(-125)' -D__FLT_MIN__=1.17549435e-38F -D__FLT_RADIX__=2 -D__FPCLASS_NEGINF=0x0004 -D__FPCLASS_NEGNORMAL=0x0008 -D__FPCLASS_NEGSUBNORMAL=0x0010 -D__FPCLASS_NEGZERO=0x0020 -D__FPCLASS_POSINF=0x0200 -D__FPCLASS_POSNORMAL=0x0100 -D__FPCLASS_POSSUBNORMAL=0x0080 -D__FPCLASS_POSZERO=0x0040 -D__FPCLASS_QNAN=0x0002 -D__FPCLASS_SNAN=0x0001 -D__FXSR__=1 -D__GCC_ASM_FLAG_OUTPUTS__=1 -D__GCC_ATOMIC_BOOL_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR_LOCK_FREE=2 -D__GCC_ATOMIC_INT_LOCK_FREE=2 -D__GCC_ATOMIC_LLONG_LOCK_FREE=2 -D__GCC_ATOMIC_LONG_LOCK_FREE=2 -D__GCC_ATOMIC_POINTER_LOCK_FREE=2 -D__GCC_ATOMIC_SHORT_LOCK_FREE=2 -D__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1 -D__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -D__GNUC__=4 -D__GXX_ABI_VERSION=1002 -D__INT16_C_SUFFIX__= '-D__INT16_FMTd__="hd"' '-D__INT16_FMTi__="hi"' -D__INT16_MAX__=32767 -D__INT16_TYPE__=short -D__INT32_C_SUFFIX__= '-D__INT32_FMTd__="d"' '-D__INT32_FMTi__="i"' -D__INT32_MAX__=2147483647 -D__INT32_TYPE__=int -D__INT64_C_SUFFIX__=LL '-D__INT64_FMTd__="lld"' '-D__INT64_FMTi__="lli"' -D__INT64_MAX__=9223372036854775807LL '-D__INT64_TYPE__=long long int' -D__INT8_C_SUFFIX__= '-D__INT8_FMTd__="hhd"' '-D__INT8_FMTi__="hhi"' -D__INT8_MAX__=127 '-D__INT8_TYPE__=signed char' -D__INTMAX_C_SUFFIX__=L '-D__INTMAX_FMTd__="ld"' '-D__INTMAX_FMTi__="li"' -D__INTMAX_MAX__=9223372036854775807L '-D__INTMAX_TYPE__=long int' -D__INTMAX_WIDTH__=64 '-D__INTPTR_FMTd__="ld"' '-D__INTPTR_FMTi__="li"' -D__INTPTR_MAX__=9223372036854775807L '-D__INTPTR_TYPE__=long int' -D__INTPTR_WIDTH__=64 '-D__INT_FAST16_FMTd__="hd"' '-D__INT_FAST16_FMTi__="hi"' -D__INT_FAST16_MAX__=32767 -D__INT_FAST16_TYPE__=short -D__INT_FAST16_WIDTH__=16 '-D__INT_FAST32_FMTd__="d"' '-D__INT_FAST32_FMTi__="i"' -D__INT_FAST32_MAX__=2147483647 -D__INT_FAST32_TYPE__=int -D__INT_FAST32_WIDTH__=32 '-D__INT_FAST64_FMTd__="lld"' '-D__INT_FAST64_FMTi__="lli"' -D__INT_FAST64_MAX__=9223372036854775807LL '-D__INT_FAST64_TYPE__=long long int' -D__INT_FAST64_WIDTH__=64 '-D__INT_FAST8_FMTd__="hhd"' '-D__INT_FAST8_FMTi__="hhi"' -D__INT_FAST8_MAX__=127 '-D__INT_FAST8_TYPE__=signed char' -D__INT_FAST8_WIDTH__=8 '-D__INT_LEAST16_FMTd__="hd"' '-D__INT_LEAST16_FMTi__="hi"' -D__INT_LEAST16_MAX__=32767 -D__INT_LEAST16_TYPE__=short -D__INT_LEAST16_WIDTH__=16 '-D__INT_LEAST32_FMTd__="d"' '-D__INT_LEAST32_FMTi__="i"' -D__INT_LEAST32_MAX__=2147483647 -D__INT_LEAST32_TYPE__=int -D__INT_LEAST32_WIDTH__=32 '-D__INT_LEAST64_FMTd__="lld"' '-D__INT_LEAST64_FMTi__="lli"' -D__INT_LEAST64_MAX__=9223372036854775807LL '-D__INT_LEAST64_TYPE__=long long int' -D__INT_LEAST64_WIDTH__=64 '-D__INT_LEAST8_FMTd__="hhd"' '-D__INT_LEAST8_FMTi__="hhi"' -D__INT_LEAST8_MAX__=127 '-D__INT_LEAST8_TYPE__=signed char' -D__INT_LEAST8_WIDTH__=8 -D__INT_MAX__=2147483647 -D__INT_WIDTH__=32 -D__LAHF_SAHF__=1 -D__LDBL_DECIMAL_DIG__=21 -D__LDBL_DENORM_MIN__=3.64519953188247460253e-4951L -D__LDBL_DIG__=18 -D__LDBL_EPSILON__=1.08420217248550443401e-19L -D__LDBL_HAS_DENORM__=1 -D__LDBL_HAS_INFINITY__=1 -D__LDBL_HAS_QUIET_NAN__=1 -D__LDBL_MANT_DIG__=64 -D__LDBL_MAX_10_EXP__=4932 -D__LDBL_MAX_EXP__=16384 '-D__LDBL_MAX__=1.18973149535723176502e+4932L' '-D__LDBL_MIN_10_EXP__=(-4931)' '-D__LDBL_MIN_EXP__=(-16381)' -D__LDBL_MIN__=3.36210314311209350626e-4932L -D__LITTLE_ENDIAN__=1 -D__LLONG_WIDTH__=64 -D__LONG_LONG_MAX__=9223372036854775807LL -D__LONG_MAX__=9223372036854775807L -D__LONG_WIDTH__=64 -D__LP64__=1 -D__MACH__=1 -D__MMX__=1 -D__NO_INLINE__=1 -D__NO_MATH_ERRNO__=1 -D__NO_MATH_INLINES=1 -D__OBJC_BOOL_IS_BOOL=0 -D__OPENCL_MEMORY_SCOPE_ALL_SVM_DEVICES=3 -D__OPENCL_MEMORY_SCOPE_DEVICE=2 -D__OPENCL_MEMORY_SCOPE_SUB_GROUP=4 -D__OPENCL_MEMORY_SCOPE_WORK_GROUP=1 -D__OPENCL_MEMORY_SCOPE_WORK_ITEM=0 -D__ORDER_BIG_ENDIAN__=4321 -D__ORDER_LITTLE_ENDIAN__=1234 -D__ORDER_PDP_ENDIAN__=3412 -D__PIC__=2 -D__POINTER_WIDTH__=64 -D__PRAGMA_REDEFINE_EXTNAME=1 '-D__PTRDIFF_FMTd__="ld"' '-D__PTRDIFF_FMTi__="li"' -D__PTRDIFF_MAX__=9223372036854775807L '-D__PTRDIFF_TYPE__=long int' -D__PTRDIFF_WIDTH__=64 -D__REGISTER_PREFIX__= -D__SCHAR_MAX__=127 -D__SEG_FS=1 -D__SEG_GS=1 -D__SHRT_MAX__=32767 -D__SHRT_WIDTH__=16 -D__SIG_ATOMIC_MAX__=2147483647 -D__SIG_ATOMIC_WIDTH__=32 -D__SIZEOF_DOUBLE__=8 -D__SIZEOF_FLOAT__=4 -D__SIZEOF_INT128__=16 -D__SIZEOF_INT__=4 -D__SIZEOF_LONG_DOUBLE__=16 -D__SIZEOF_LONG_LONG__=8 -D__SIZEOF_LONG__=8 -D__SIZEOF_POINTER__=8 -D__SIZEOF_PTRDIFF_T__=8 -D__SIZEOF_SHORT__=2 -D__SIZEOF_SIZE_T__=8 -D__SIZEOF_WCHAR_T__=4 -D__SIZEOF_WINT_T__=4 '-D__SIZE_FMTX__="lX"' '-D__SIZE_FMTo__="lo"' '-D__SIZE_FMTu__="lu"' '-D__SIZE_FMTx__="lx"' -D__SIZE_MAX__=18446744073709551615UL '-D__SIZE_TYPE__=long unsigned int' -D__SIZE_WIDTH__=64 -D__SSE2_MATH__=1 -D__SSE2__=1 -D__SSE3__=1 -D__SSE4_1__=1 -D__SSE_MATH__=1 -D__SSE__=1 -D__SSSE3__=1 -D__STDC_NO_THREADS__=1 -D__STDC_UTF_16__=1 -D__STDC_UTF_32__=1 -D__UINT16_C_SUFFIX__= '-D__UINT16_FMTX__="hX"' '-D__UINT16_FMTo__="ho"' '-D__UINT16_FMTu__="hu"' '-D__UINT16_FMTx__="hx"' -D__UINT16_MAX__=65535 '-D__UINT16_TYPE__=unsigned short' -D__UINT32_C_SUFFIX__=U '-D__UINT32_FMTX__="X"' '-D__UINT32_FMTo__="o"' '-D__UINT32_FMTu__="u"' '-D__UINT32_FMTx__="x"' -D__UINT32_MAX__=4294967295U '-D__UINT32_TYPE__=unsigned int' -D__UINT64_C_SUFFIX__=ULL '-D__UINT64_FMTX__="llX"' '-D__UINT64_FMTo__="llo"' '-D__UINT64_FMTu__="llu"' '-D__UINT64_FMTx__="llx"' -D__UINT64_MAX__=18446744073709551615ULL '-D__UINT64_TYPE__=long long unsigned int' -D__UINT8_C_SUFFIX__= '-D__UINT8_FMTX__="hhX"' '-D__UINT8_FMTo__="hho"' '-D__UINT8_FMTu__="hhu"' '-D__UINT8_FMTx__="hhx"' -D__UINT8_MAX__=255 '-D__UINT8_TYPE__=unsigned char' -D__UINTMAX_C_SUFFIX__=UL '-D__UINTMAX_FMTX__="lX"' '-D__UINTMAX_FMTo__="lo"' '-D__UINTMAX_FMTu__="lu"' '-D__UINTMAX_FMTx__="lx"' -D__UINTMAX_MAX__=18446744073709551615UL '-D__UINTMAX_TYPE__=long unsigned int' -D__UINTMAX_WIDTH__=64 '-D__UINTPTR_FMTX__="lX"' '-D__UINTPTR_FMTo__="lo"' '-D__UINTPTR_FMTu__="lu"' '-D__UINTPTR_FMTx__="lx"' -D__UINTPTR_MAX__=18446744073709551615UL '-D__UINTPTR_TYPE__=long unsigned int' -D__UINTPTR_WIDTH__=64 '-D__UINT_FAST16_FMTX__="hX"' '-D__UINT_FAST16_FMTo__="ho"' '-D__UINT_FAST16_FMTu__="hu"' '-D__UINT_FAST16_FMTx__="hx"' -D__UINT_FAST16_MAX__=65535 '-D__UINT_FAST16_TYPE__=unsigned short' '-D__UINT_FAST32_FMTX__="X"' '-D__UINT_FAST32_FMTo__="o"' '-D__UINT_FAST32_FMTu__="u"' '-D__UINT_FAST32_FMTx__="x"' -D__UINT_FAST32_MAX__=4294967295U '-D__UINT_FAST32_TYPE__=unsigned int' '-D__UINT_FAST64_FMTX__="llX"' '-D__UINT_FAST64_FMTo__="llo"' '-D__UINT_FAST64_FMTu__="llu"' '-D__UINT_FAST64_FMTx__="llx"' -D__UINT_FAST64_MAX__=18446744073709551615ULL '-D__UINT_FAST64_TYPE__=long long unsigned int' '-D__UINT_FAST8_FMTX__="hhX"' '-D__UINT_FAST8_FMTo__="hho"' '-D__UINT_FAST8_FMTu__="hhu"' '-D__UINT_FAST8_FMTx__="hhx"' -D__UINT_FAST8_MAX__=255 '-D__UINT_FAST8_TYPE__=unsigned char' '-D__UINT_LEAST16_FMTX__="hX"' '-D__UINT_LEAST16_FMTo__="ho"' '-D__UINT_LEAST16_FMTu__="hu"' '-D__UINT_LEAST16_FMTx__="hx"' -D__UINT_LEAST16_MAX__=65535 '-D__UINT_LEAST16_TYPE__=unsigned short' '-D__UINT_LEAST32_FMTX__="X"' '-D__UINT_LEAST32_FMTo__="o"' '-D__UINT_LEAST32_FMTu__="u"' '-D__UINT_LEAST32_FMTx__="x"' -D__UINT_LEAST32_MAX__=4294967295U '-D__UINT_LEAST32_TYPE__=unsigned int' '-D__UINT_LEAST64_FMTX__="llX"' '-D__UINT_LEAST64_FMTo__="llo"' '-D__UINT_LEAST64_FMTu__="llu"' '-D__UINT_LEAST64_FMTx__="llx"' -D__UINT_LEAST64_MAX__=18446744073709551615ULL '-D__UINT_LEAST64_TYPE__=long long unsigned int' '-D__UINT_LEAST8_FMTX__="hhX"' '-D__UINT_LEAST8_FMTo__="hho"' '-D__UINT_LEAST8_FMTu__="hhu"' '-D__UINT_LEAST8_FMTx__="hhx"' -D__UINT_LEAST8_MAX__=255 '-D__UINT_LEAST8_TYPE__=unsigned char' -D__USER_LABEL_PREFIX__=_ '-D__VERSION__="Apple LLVM 16.0.0 (clang-1600.0.26.6)"' -D__WCHAR_MAX__=2147483647 -D__WCHAR_TYPE__=int -D__WCHAR_WIDTH__=32 -D__WINT_MAX__=2147483647 -D__WINT_TYPE__=int -D__WINT_WIDTH__=32 -D__amd64=1 -D__amd64__=1 -D__apple_build_version__=16000026 '-D__block=__attribute__((__blocks__(byref)))' -D__clang__=1 '-D__clang_literal_encoding__="UTF-8"' -D__clang_major__=16 -D__clang_minor__=0 -D__clang_patchlevel__=0 '-D__clang_version__="16.0.0 (clang-1600.0.26.6)"' '-D__clang_wide_literal_encoding__="UTF-32"' -D__code_model_small__=1 -D__core2=1 -D__core2__=1 -D__llvm__=1 -D__nonnull=_Nonnull -D__null_unspecified=_Null_unspecified -D__nullable=_Nullable -D__pic__=2 '-D__seg_fs=__attribute__((address_space(257)))' '-D__seg_gs=__attribute__((address_space(256)))' -D__strong= -D__tune_core2__=1 -D__unsafe_unretained= '-D__weak=__attribute__((objc_gc(weak)))' -D__x86_64=1 -D__x86_64__=1 '-D__private_extern__=extern __attribute__((visibility("hidden")))' --isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include --blocks -D__GCC_HAVE_DWARF2_CFI_ASM=1 -I/usr/local/include -I/Library/Developer/CommandLineTools/usr/lib/clang/16/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -I/Library/Developer/CommandLineTools/usr/include -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks -- code_036a18f0.c
[E 16:35:36 42702] Starting compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp/compilations/54/38665158_0.trap.br
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_036a18f0.c
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdlib.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdlib.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/Availability.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityVersions.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternal.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternalLegacy.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/cdefs.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_symbol_aliasing.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_posix_availability.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/wait.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_pid_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_id_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/signal.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/appleapiopts.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/signal.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/signal.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_mcontext.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_mcontext.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/machine/_structs.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/mach/i386/_structs.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/types.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int8_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int16_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int32_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int64_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int8_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int16_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int32_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int64_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_intptr_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uintptr_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_attr_t.h
[E 16:35:36 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigaltstack.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ucontext.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_sigset_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uid_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/resource.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/usr/lib/clang/16/include/stdint.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdint.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint8_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint16_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint32_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint64_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_intmax_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types/_uintmax_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_timeval.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/__endian.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/_OSByteOrder.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/libkern/i386/_OSByteOrder.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/alloca.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ct_rune_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rune_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_wchar_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_malloc_type.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/malloc/_ptrcheck.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_abort.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_dev_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_mode_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/string.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_string.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rsize_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_errno_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ssize_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_strings.h
[E 16:35:37 42702] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_strings.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_common.h
[E 16:35:37 42702] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_string.h
[E 16:35:37 42702] Creating trap tarball /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_036a18f0.c.8d78a191_0.trap.tar.br
[E 16:35:37 42702] Emitting trap files for code_036a18f0.c
[E 16:35:37 42702] Opening existencedb in /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/existencedb/db
[E 16:35:37 42702] Wrote 85 files to /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_036a18f0.c.8d78a191_0.trap.tar.br
[E 16:35:37 42702] Finished compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/trap/cpp/compilations/54/38665158_0.trap.br
[E 16:35:37 42702] Marking C compilation as happened.
[E 16:35:37 42702] Setting tag c-compilation-happened
[E 16:35:37 42702] Starting from CODEQL_TRACER_DB_ID 000000000000A6CC_0000000000000001
[E 16:35:37 42702] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:37 42702] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:37 42702] Set tag for 000000000000A6CC_0000000000000001
[E 16:35:37 42702] Set tag for 000000000000A6B9_0000000000000002
[E 16:35:37 42702] Set tag for 000000000000A6B9_0000000000000001
[E 16:35:37 42702] Set tag for 000000000000A6B3_0000000000000001
[E 16:35:37 42702] Set tag for 000000000000A6B1_0000000000000001
[E 16:35:37 42702] Set tag for 000000000000A6A4_0000000000000004
[E 16:35:37 42702] Set tag for 000000000000A6A4_0000000000000003
[E 16:35:37 42702] Set tag for 000000000000A6A4_0000000000000002
[E 16:35:37 42702] Set tag for root
[E 16:35:37 42702] Unlocking DB
[E 16:35:37 42702] Unlocked DB
[T 16:35:37 42700] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:37 42752] Attempting to switch stdout/stderr to 3...
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/ld.semmle.0000A6CB.slice.x86_64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/ld.semmle.0000A6CB.slice.x86_64: replacing existing signature
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/ld.semmle.0000A6CB.slice.arm64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/ld.semmle.0000A6CB.slice.arm64: replacing existing signature
[T 16:35:37 42758] Initializing tracer.
[T 16:35:37 42758] Initialising tags...
[T 16:35:37 42758] ID set to 000000000000A706_0000000000000001 (parent 000000000000A6B9_0000000000000002)
[T 16:35:37 42758] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/ld (canonical: /Library/Developer/CommandLineTools/usr/bin/ld) ====
[T 16:35:37 42758] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/ld ===
[T 16:35:37 42758] Executing the following tracer actions:
[T 16:35:37 42758] Tracer actions:
[T 16:35:37 42758] pre_invocations(0)
[T 16:35:37 42758] post_invocations(1)
[T 16:35:37 42758] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--linker, --semmle-linker-executable, /library/developer/commandlinetools/usr/bin/ld, -demangle, -lto_library, /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib, -no_deduplicate, -dynamic, -arch, x86_64, -platform_version, macos, 15.0.0, 15.2, -syslibroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -mllvm, -enable-linkonceodr-outlining, -o, code_036a18f0, -L/usr/local/lib, /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_036a18f0-658fd6.o, -lSystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
[T 16:35:37 42758] trace_languages(1): [cpp]
[T 16:35:38 42764] Attempting to switch stdout/stderr to 4...
[T 16:35:38 42764] Initializing tracer.
[T 16:35:38 42764] Initialising tags...
[T 16:35:38 42764] ID set to 000000000000A70C_0000000000000001 (parent 000000000000A706_0000000000000001)
[E 16:35:38 42764] CodeQL C/C++ Extractor 2.11.2
[E 16:35:38 42764] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:38 42764] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --linker --semmle-linker-executable /library/developer/commandlinetools/usr/bin/ld -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -no_deduplicate -dynamic -arch x86_64 -platform_version macos 15.0.0 15.2 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o code_036a18f0 -L/usr/local/lib /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_036a18f0-658fd6.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
Warning: Unrecognised command line argument -no_deduplicate
Warning: Unrecognised command line argument -platform_version
Warning: Unrecognised command line argument -enable-linkonceodr-outlining
[E 16:35:38 42764] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:38 42764] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:38 42764] Warning[extractor-c++]: In print_object_names: Could not open object file "macos" (error 2: No such file or directory).
[E 16:35:38 42764] Warning[extractor-c++]: In print_object_names: Could not open object file "15.0.0" (error 2: No such file or directory).
[E 16:35:38 42764] Warning[extractor-c++]: In print_object_names: Could not open object file "15.2" (error 2: No such file or directory).
[E 16:35:38 42764] Warning[extractor-c++]: In canonicalise_path: realpath failed
[T 16:35:38 42758] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42765] Attempting to switch stdout/stderr to 4...
[T 16:35:38 42765] Initializing tracer.
[T 16:35:38 42765] Initialising tags...
[T 16:35:38 42765] ID set to 000000000000A70D_0000000000000001 (parent 000000000000A6B9_0000000000000002)
[E 16:35:38 42765] CodeQL C/C++ Extractor 2.11.2
[E 16:35:38 42765] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:38 42765] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_036a18f0.c -o code_036a18f0
[T 16:35:38 42766] Initializing tracer.
[T 16:35:38 42766] Initialising tags...
[T 16:35:38 42766] ID set to 000000000000A70E_0000000000000001 (parent 000000000000A70D_0000000000000001)
[T 16:35:38 42766] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:38 42766] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:38 42766] Executing the following tracer actions:
[T 16:35:38 42766] Tracer actions:
[T 16:35:38 42766] pre_invocations(0)
[T 16:35:38 42766] post_invocations(1)
[T 16:35:38 42766] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 16:35:38 42766] trace_languages(1): [cpp]
[T 16:35:38 42768] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42768] Initializing tracer.
[T 16:35:38 42768] Initialising tags...
[T 16:35:38 42768] ID set to 000000000000A710_0000000000000001 (parent 000000000000A70E_0000000000000001)
[E 16:35:38 42768] Mimicry classification suppression detected; exiting.
[T 16:35:38 42766] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42769] Initializing tracer.
[T 16:35:38 42769] Initialising tags...
[T 16:35:38 42769] ID set to 000000000000A711_0000000000000001 (parent 000000000000A70D_0000000000000001)
[T 16:35:38 42769] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:38 42769] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:38 42769] Executing the following tracer actions:
[T 16:35:38 42769] Tracer actions:
[T 16:35:38 42769] pre_invocations(0)
[T 16:35:38 42769] post_invocations(1)
[T 16:35:38 42769] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 16:35:38 42769] trace_languages(1): [cpp]
[T 16:35:38 42771] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42771] Initializing tracer.
[T 16:35:38 42771] Initialising tags...
[T 16:35:38 42771] ID set to 000000000000A713_0000000000000001 (parent 000000000000A711_0000000000000001)
[E 16:35:38 42771] Mimicry classification suppression detected; exiting.
[T 16:35:38 42769] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42772] Initializing tracer.
[T 16:35:38 42772] Initialising tags...
[T 16:35:38 42772] ID set to 000000000000A714_0000000000000001 (parent 000000000000A70D_0000000000000001)
[T 16:35:38 42772] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:38 42772] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:38 42772] Executing the following tracer actions:
[T 16:35:38 42772] Tracer actions:
[T 16:35:38 42772] pre_invocations(0)
[T 16:35:38 42772] post_invocations(1)
[T 16:35:38 42772] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42765_301220.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42765_300706.c]
[T 16:35:38 42772] trace_languages(1): [cpp]
[T 16:35:38 42774] Initializing tracer.
[T 16:35:38 42774] Initialising tags...
[T 16:35:38 42774] ID set to 000000000000A716_0000000000000001 (parent 000000000000A714_0000000000000001)
[T 16:35:38 42774] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:38 42774] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:38 42774] Executing the following tracer actions:
[T 16:35:38 42774] Tracer actions:
[T 16:35:38 42774] pre_invocations(0)
[T 16:35:38 42774] post_invocations(1)
[T 16:35:38 42774] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_42765_300706.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42765_301220.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42765_300706.c]
[T 16:35:38 42774] trace_languages(1): [cpp]
[T 16:35:38 42776] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42776] Initializing tracer.
[T 16:35:38 42776] Initialising tags...
[T 16:35:38 42776] ID set to 000000000000A718_0000000000000001 (parent 000000000000A716_0000000000000001)
[E 16:35:38 42776] Mimicry classification suppression detected; exiting.
[T 16:35:38 42774] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42777] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42777] Initializing tracer.
[T 16:35:38 42777] Initialising tags...
[T 16:35:38 42777] ID set to 000000000000A719_0000000000000001 (parent 000000000000A714_0000000000000001)
[E 16:35:38 42777] Mimicry classification suppression detected; exiting.
[T 16:35:38 42772] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42778] Initializing tracer.
[T 16:35:38 42778] Initialising tags...
[T 16:35:38 42778] ID set to 000000000000A71A_0000000000000001 (parent 000000000000A70D_0000000000000001)
[T 16:35:38 42778] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:38 42778] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:38 42778] Executing the following tracer actions:
[T 16:35:38 42778] Tracer actions:
[T 16:35:38 42778] pre_invocations(0)
[T 16:35:38 42778] post_invocations(1)
[T 16:35:38 42778] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42765_477861.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42765_477610.c]
[T 16:35:38 42778] trace_languages(1): [cpp]
[T 16:35:38 42780] Initializing tracer.
[T 16:35:38 42780] Initialising tags...
[T 16:35:38 42780] ID set to 000000000000A71C_0000000000000001 (parent 000000000000A71A_0000000000000001)
[T 16:35:38 42780] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:38 42780] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:38 42780] Executing the following tracer actions:
[T 16:35:38 42780] Tracer actions:
[T 16:35:38 42780] pre_invocations(0)
[T 16:35:38 42780] post_invocations(1)
[T 16:35:38 42780] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_42765_477610.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42765_477861.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42765_477610.c]
[T 16:35:38 42780] trace_languages(1): [cpp]
[T 16:35:38 42782] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42782] Initializing tracer.
[T 16:35:38 42782] Initialising tags...
[T 16:35:38 42782] ID set to 000000000000A71E_0000000000000001 (parent 000000000000A71C_0000000000000001)
[E 16:35:38 42782] Mimicry classification suppression detected; exiting.
[T 16:35:38 42780] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42783] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42783] Initializing tracer.
[T 16:35:38 42783] Initialising tags...
[T 16:35:38 42783] ID set to 000000000000A71F_0000000000000001 (parent 000000000000A71A_0000000000000001)
[E 16:35:38 42783] Mimicry classification suppression detected; exiting.
[T 16:35:38 42778] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 16:35:38 42765] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:38 42765] Checking whether C compilation already happened.
[E 16:35:38 42765] Checking for tag c-compilation-happened
[E 16:35:38 42765] Checking CODEQL_TRACER_DB_ID 000000000000A6B9_0000000000000002
[E 16:35:38 42765] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:38 42765] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:38 42765] Unlocking DB
[E 16:35:38 42765] Unlocked DB
[E 16:35:38 42765] Exiting as C compilation already happened.
[T 16:35:38 42681] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42784] Attempting to switch stdout/stderr to 4...
[T 16:35:38 42784] Initializing tracer.
[T 16:35:38 42784] Initialising tags...
[T 16:35:38 42784] ID set to 000000000000A720_0000000000000001 (parent 000000000000A6B3_0000000000000001)
[E 16:35:38 42784] CodeQL C/C++ Extractor 2.11.2
[E 16:35:38 42784] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:38 42784] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/gcc code_036a18f0.c -o code_036a18f0
[T 16:35:38 42785] Initializing tracer.
[T 16:35:38 42785] Initialising tags...
[T 16:35:38 42785] ID set to 000000000000A721_0000000000000001 (parent 000000000000A720_0000000000000001)
[T 16:35:38 42785] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:38 42785] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:38 42785] Executing the following tracer actions:
[T 16:35:38 42785] Tracer actions:
[T 16:35:38 42785] pre_invocations(0)
[T 16:35:38 42785] post_invocations(1)
[T 16:35:38 42785] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 16:35:38 42785] trace_languages(1): [cpp]
[T 16:35:38 42786] Initializing tracer.
[T 16:35:38 42786] Initialising tags...
[T 16:35:38 42786] ID set to 000000000000A722_0000000000000001 (parent 000000000000A721_0000000000000001)
[T 16:35:38 42786] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:38 42786] Executing the following tracer actions:
[T 16:35:38 42786] Tracer actions:
[T 16:35:38 42786] pre_invocations(0)
[T 16:35:38 42786] post_invocations(0)
[T 16:35:38 42786] trace_languages(1): [cpp]
[T 16:35:38 42786] Initializing tracer.
[T 16:35:38 42786] Initialising tags...
[T 16:35:38 42786] ID set to 000000000000A722_0000000000000002 (parent 000000000000A722_0000000000000001)
[T 16:35:38 42786] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:38 42786] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:38 42786] Executing the following tracer actions:
[T 16:35:38 42786] Tracer actions:
[T 16:35:38 42786] pre_invocations(0)
[T 16:35:38 42786] post_invocations(1)
[T 16:35:38 42786] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 16:35:38 42786] trace_languages(1): [cpp]
[T 16:35:38 42788] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42788] Initializing tracer.
[T 16:35:38 42788] Initialising tags...
[T 16:35:38 42788] ID set to 000000000000A724_0000000000000001 (parent 000000000000A722_0000000000000002)
[E 16:35:38 42788] Mimicry classification suppression detected; exiting.
[T 16:35:38 42786] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42789] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42789] Initializing tracer.
[T 16:35:38 42789] Initialising tags...
[T 16:35:38 42789] ID set to 000000000000A725_0000000000000001 (parent 000000000000A721_0000000000000001)
[E 16:35:38 42789] Mimicry classification suppression detected; exiting.
[T 16:35:38 42785] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42790] Initializing tracer.
[T 16:35:38 42790] Initialising tags...
[T 16:35:38 42790] ID set to 000000000000A726_0000000000000001 (parent 000000000000A720_0000000000000001)
[T 16:35:38 42790] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:38 42790] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:38 42790] Executing the following tracer actions:
[T 16:35:38 42790] Tracer actions:
[T 16:35:38 42790] pre_invocations(0)
[T 16:35:38 42790] post_invocations(1)
[T 16:35:38 42790] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 16:35:38 42790] trace_languages(1): [cpp]
[T 16:35:38 42791] Initializing tracer.
[T 16:35:38 42791] Initialising tags...
[T 16:35:38 42791] ID set to 000000000000A727_0000000000000001 (parent 000000000000A726_0000000000000001)
[T 16:35:38 42791] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:38 42791] Executing the following tracer actions:
[T 16:35:38 42791] Tracer actions:
[T 16:35:38 42791] pre_invocations(0)
[T 16:35:38 42791] post_invocations(0)
[T 16:35:38 42791] trace_languages(1): [cpp]
[T 16:35:38 42791] Initializing tracer.
[T 16:35:38 42791] Initialising tags...
[T 16:35:38 42791] ID set to 000000000000A727_0000000000000002 (parent 000000000000A727_0000000000000001)
[T 16:35:38 42791] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:38 42791] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:38 42791] Executing the following tracer actions:
[T 16:35:38 42791] Tracer actions:
[T 16:35:38 42791] pre_invocations(0)
[T 16:35:38 42791] post_invocations(1)
[T 16:35:38 42791] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 16:35:38 42791] trace_languages(1): [cpp]
[T 16:35:38 42793] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42793] Initializing tracer.
[T 16:35:38 42793] Initialising tags...
[T 16:35:38 42793] ID set to 000000000000A729_0000000000000001 (parent 000000000000A727_0000000000000002)
[E 16:35:38 42793] Mimicry classification suppression detected; exiting.
[T 16:35:38 42791] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42794] Attempting to switch stdout/stderr to 7...
[T 16:35:38 42794] Initializing tracer.
[T 16:35:38 42794] Initialising tags...
[T 16:35:38 42794] ID set to 000000000000A72A_0000000000000001 (parent 000000000000A726_0000000000000001)
[E 16:35:38 42794] Mimicry classification suppression detected; exiting.
[T 16:35:38 42790] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:38 42795] Initializing tracer.
[T 16:35:38 42795] Initialising tags...
[T 16:35:38 42795] ID set to 000000000000A72B_0000000000000001 (parent 000000000000A720_0000000000000001)
[T 16:35:38 42795] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:38 42795] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:38 42795] Executing the following tracer actions:
[T 16:35:38 42795] Tracer actions:
[T 16:35:38 42795] pre_invocations(0)
[T 16:35:38 42795] post_invocations(1)
[T 16:35:38 42795] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42784_976617.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42784_976152.c]
[T 16:35:38 42795] trace_languages(1): [cpp]
[T 16:35:39 42796] Initializing tracer.
[T 16:35:39 42796] Initialising tags...
[T 16:35:39 42796] ID set to 000000000000A72C_0000000000000001 (parent 000000000000A72B_0000000000000001)
[T 16:35:39 42796] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:39 42796] Executing the following tracer actions:
[T 16:35:39 42796] Tracer actions:
[T 16:35:39 42796] pre_invocations(0)
[T 16:35:39 42796] post_invocations(0)
[T 16:35:39 42796] trace_languages(1): [cpp]
[T 16:35:39 42796] Initializing tracer.
[T 16:35:39 42796] Initialising tags...
[T 16:35:39 42796] ID set to 000000000000A72C_0000000000000002 (parent 000000000000A72C_0000000000000001)
[T 16:35:39 42796] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:39 42796] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:39 42796] Executing the following tracer actions:
[T 16:35:39 42796] Tracer actions:
[T 16:35:39 42796] pre_invocations(0)
[T 16:35:39 42796] post_invocations(1)
[T 16:35:39 42796] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42784_976617.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42784_976152.c]
[T 16:35:39 42796] trace_languages(1): [cpp]
[T 16:35:39 42798] Initializing tracer.
[T 16:35:39 42798] Initialising tags...
[T 16:35:39 42798] ID set to 000000000000A72E_0000000000000001 (parent 000000000000A72C_0000000000000002)
[T 16:35:39 42798] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:39 42798] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:39 42798] Executing the following tracer actions:
[T 16:35:39 42798] Tracer actions:
[T 16:35:39 42798] pre_invocations(0)
[T 16:35:39 42798] post_invocations(1)
[T 16:35:39 42798] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_42784_976152.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42784_976617.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42784_976152.c]
[T 16:35:39 42798] trace_languages(1): [cpp]
[T 16:35:39 42800] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42800] Initializing tracer.
[T 16:35:39 42800] Initialising tags...
[T 16:35:39 42800] ID set to 000000000000A730_0000000000000001 (parent 000000000000A72E_0000000000000001)
[E 16:35:39 42800] Mimicry classification suppression detected; exiting.
[T 16:35:39 42798] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42801] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42801] Initializing tracer.
[T 16:35:39 42801] Initialising tags...
[T 16:35:39 42801] ID set to 000000000000A731_0000000000000001 (parent 000000000000A72C_0000000000000002)
[E 16:35:39 42801] Mimicry classification suppression detected; exiting.
[T 16:35:39 42796] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42802] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42802] Initializing tracer.
[T 16:35:39 42802] Initialising tags...
[T 16:35:39 42802] ID set to 000000000000A732_0000000000000001 (parent 000000000000A72B_0000000000000001)
[E 16:35:39 42802] Mimicry classification suppression detected; exiting.
[T 16:35:39 42795] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42803] Initializing tracer.
[T 16:35:39 42803] Initialising tags...
[T 16:35:39 42803] ID set to 000000000000A733_0000000000000001 (parent 000000000000A720_0000000000000001)
[T 16:35:39 42803] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:39 42803] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:39 42803] Executing the following tracer actions:
[T 16:35:39 42803] Tracer actions:
[T 16:35:39 42803] pre_invocations(0)
[T 16:35:39 42803] post_invocations(1)
[T 16:35:39 42803] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42784_226015.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42784_225595.c]
[T 16:35:39 42803] trace_languages(1): [cpp]
[T 16:35:39 42804] Initializing tracer.
[T 16:35:39 42804] Initialising tags...
[T 16:35:39 42804] ID set to 000000000000A734_0000000000000001 (parent 000000000000A733_0000000000000001)
[T 16:35:39 42804] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:39 42804] Executing the following tracer actions:
[T 16:35:39 42804] Tracer actions:
[T 16:35:39 42804] pre_invocations(0)
[T 16:35:39 42804] post_invocations(0)
[T 16:35:39 42804] trace_languages(1): [cpp]
[T 16:35:39 42804] Initializing tracer.
[T 16:35:39 42804] Initialising tags...
[T 16:35:39 42804] ID set to 000000000000A734_0000000000000002 (parent 000000000000A734_0000000000000001)
[T 16:35:39 42804] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:39 42804] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:39 42804] Executing the following tracer actions:
[T 16:35:39 42804] Tracer actions:
[T 16:35:39 42804] pre_invocations(0)
[T 16:35:39 42804] post_invocations(1)
[T 16:35:39 42804] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42784_226015.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42784_225595.c]
[T 16:35:39 42804] trace_languages(1): [cpp]
[T 16:35:39 42806] Initializing tracer.
[T 16:35:39 42806] Initialising tags...
[T 16:35:39 42806] ID set to 000000000000A736_0000000000000001 (parent 000000000000A734_0000000000000002)
[T 16:35:39 42806] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:39 42806] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:39 42806] Executing the following tracer actions:
[T 16:35:39 42806] Tracer actions:
[T 16:35:39 42806] pre_invocations(0)
[T 16:35:39 42806] post_invocations(1)
[T 16:35:39 42806] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_42784_225595.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42784_226015.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42784_225595.c]
[T 16:35:39 42806] trace_languages(1): [cpp]
[T 16:35:39 42808] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42808] Initializing tracer.
[T 16:35:39 42808] Initialising tags...
[T 16:35:39 42808] ID set to 000000000000A738_0000000000000001 (parent 000000000000A736_0000000000000001)
[E 16:35:39 42808] Mimicry classification suppression detected; exiting.
[T 16:35:39 42806] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42809] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42809] Initializing tracer.
[T 16:35:39 42809] Initialising tags...
[T 16:35:39 42809] ID set to 000000000000A739_0000000000000001 (parent 000000000000A734_0000000000000002)
[E 16:35:39 42809] Mimicry classification suppression detected; exiting.
[T 16:35:39 42804] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42810] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42810] Initializing tracer.
[T 16:35:39 42810] Initialising tags...
[T 16:35:39 42810] ID set to 000000000000A73A_0000000000000001 (parent 000000000000A733_0000000000000001)
[E 16:35:39 42810] Mimicry classification suppression detected; exiting.
[T 16:35:39 42803] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 16:35:39 42784] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:39 42784] Checking whether C compilation already happened.
[E 16:35:39 42784] Checking for tag c-compilation-happened
[E 16:35:39 42784] Checking CODEQL_TRACER_DB_ID 000000000000A6B3_0000000000000001
[E 16:35:39 42784] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:39 42784] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:39 42784] Unlocking DB
[E 16:35:39 42784] Unlocked DB
[E 16:35:39 42784] Exiting as C compilation already happened.
[T 16:35:39 42675] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42811] Attempting to switch stdout/stderr to 4...
[T 16:35:39 42811] Initializing tracer.
[T 16:35:39 42811] Initialising tags...
[T 16:35:39 42811] ID set to 000000000000A73B_0000000000000001 (parent 000000000000A6B1_0000000000000001)
[E 16:35:39 42811] CodeQL C/C++ Extractor 2.11.2
[E 16:35:39 42811] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 16:35:39 42811] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /usr/bin/gcc code_036a18f0.c -o code_036a18f0
[T 16:35:39 42812] Initializing tracer.
[T 16:35:39 42812] Initialising tags...
[T 16:35:39 42812] ID set to 000000000000A73C_0000000000000001 (parent 000000000000A73B_0000000000000001)
[T 16:35:39 42812] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 16:35:39 42812] Lua: === Intercepted call to /usr/bin/gcc ===
[T 16:35:39 42812] Executing the following tracer actions:
[T 16:35:39 42812] Tracer actions:
[T 16:35:39 42812] pre_invocations(0)
[T 16:35:39 42812] post_invocations(1)
[T 16:35:39 42812] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --version]
[T 16:35:39 42812] trace_languages(1): [cpp]
[T 16:35:39 42813] Initializing tracer.
[T 16:35:39 42813] Initialising tags...
[T 16:35:39 42813] ID set to 000000000000A73D_0000000000000001 (parent 000000000000A73C_0000000000000001)
[T 16:35:39 42813] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:39 42813] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:39 42813] Executing the following tracer actions:
[T 16:35:39 42813] Tracer actions:
[T 16:35:39 42813] pre_invocations(0)
[T 16:35:39 42813] post_invocations(1)
[T 16:35:39 42813] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 16:35:39 42813] trace_languages(1): [cpp]
[T 16:35:39 42814] Initializing tracer.
[T 16:35:39 42814] Initialising tags...
[T 16:35:39 42814] ID set to 000000000000A73E_0000000000000001 (parent 000000000000A73D_0000000000000001)
[T 16:35:39 42814] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:39 42814] Executing the following tracer actions:
[T 16:35:39 42814] Tracer actions:
[T 16:35:39 42814] pre_invocations(0)
[T 16:35:39 42814] post_invocations(0)
[T 16:35:39 42814] trace_languages(1): [cpp]
[T 16:35:39 42814] Initializing tracer.
[T 16:35:39 42814] Initialising tags...
[T 16:35:39 42814] ID set to 000000000000A73E_0000000000000002 (parent 000000000000A73E_0000000000000001)
[T 16:35:39 42814] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:39 42814] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:39 42814] Executing the following tracer actions:
[T 16:35:39 42814] Tracer actions:
[T 16:35:39 42814] pre_invocations(0)
[T 16:35:39 42814] post_invocations(1)
[T 16:35:39 42814] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 16:35:39 42814] trace_languages(1): [cpp]
[T 16:35:39 42816] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42816] Initializing tracer.
[T 16:35:39 42816] Initialising tags...
[T 16:35:39 42816] ID set to 000000000000A740_0000000000000001 (parent 000000000000A73E_0000000000000002)
[E 16:35:39 42816] Mimicry classification suppression detected; exiting.
[T 16:35:39 42814] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42817] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42817] Initializing tracer.
[T 16:35:39 42817] Initialising tags...
[T 16:35:39 42817] ID set to 000000000000A741_0000000000000001 (parent 000000000000A73D_0000000000000001)
[E 16:35:39 42817] Mimicry classification suppression detected; exiting.
[T 16:35:39 42813] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42818] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42818] Initializing tracer.
[T 16:35:39 42818] Initialising tags...
[T 16:35:39 42818] ID set to 000000000000A742_0000000000000001 (parent 000000000000A73C_0000000000000001)
[E 16:35:39 42818] Mimicry classification suppression detected; exiting.
[T 16:35:39 42812] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42819] Initializing tracer.
[T 16:35:39 42819] Initialising tags...
[T 16:35:39 42819] ID set to 000000000000A743_0000000000000001 (parent 000000000000A73B_0000000000000001)
[T 16:35:39 42819] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 16:35:39 42819] Lua: === Intercepted call to /usr/bin/gcc ===
[T 16:35:39 42819] Executing the following tracer actions:
[T 16:35:39 42819] Tracer actions:
[T 16:35:39 42819] pre_invocations(0)
[T 16:35:39 42819] post_invocations(1)
[T 16:35:39 42819] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --help]
[T 16:35:39 42819] trace_languages(1): [cpp]
[T 16:35:39 42820] Initializing tracer.
[T 16:35:39 42820] Initialising tags...
[T 16:35:39 42820] ID set to 000000000000A744_0000000000000001 (parent 000000000000A743_0000000000000001)
[T 16:35:39 42820] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:39 42820] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:39 42820] Executing the following tracer actions:
[T 16:35:39 42820] Tracer actions:
[T 16:35:39 42820] pre_invocations(0)
[T 16:35:39 42820] post_invocations(1)
[T 16:35:39 42820] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 16:35:39 42820] trace_languages(1): [cpp]
[T 16:35:39 42821] Initializing tracer.
[T 16:35:39 42821] Initialising tags...
[T 16:35:39 42821] ID set to 000000000000A745_0000000000000001 (parent 000000000000A744_0000000000000001)
[T 16:35:39 42821] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:39 42821] Executing the following tracer actions:
[T 16:35:39 42821] Tracer actions:
[T 16:35:39 42821] pre_invocations(0)
[T 16:35:39 42821] post_invocations(0)
[T 16:35:39 42821] trace_languages(1): [cpp]
[T 16:35:39 42821] Initializing tracer.
[T 16:35:39 42821] Initialising tags...
[T 16:35:39 42821] ID set to 000000000000A745_0000000000000002 (parent 000000000000A745_0000000000000001)
[T 16:35:39 42821] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:39 42821] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:39 42821] Executing the following tracer actions:
[T 16:35:39 42821] Tracer actions:
[T 16:35:39 42821] pre_invocations(0)
[T 16:35:39 42821] post_invocations(1)
[T 16:35:39 42821] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 16:35:39 42821] trace_languages(1): [cpp]
[T 16:35:39 42823] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42823] Initializing tracer.
[T 16:35:39 42823] Initialising tags...
[T 16:35:39 42823] ID set to 000000000000A747_0000000000000001 (parent 000000000000A745_0000000000000002)
[E 16:35:39 42823] Mimicry classification suppression detected; exiting.
[T 16:35:39 42821] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42824] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42824] Initializing tracer.
[T 16:35:39 42824] Initialising tags...
[T 16:35:39 42824] ID set to 000000000000A748_0000000000000001 (parent 000000000000A744_0000000000000001)
[E 16:35:39 42824] Mimicry classification suppression detected; exiting.
[T 16:35:39 42820] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42825] Attempting to switch stdout/stderr to 7...
[T 16:35:39 42825] Initializing tracer.
[T 16:35:39 42825] Initialising tags...
[T 16:35:39 42825] ID set to 000000000000A749_0000000000000001 (parent 000000000000A743_0000000000000001)
[E 16:35:39 42825] Mimicry classification suppression detected; exiting.
[T 16:35:39 42819] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:39 42826] Initializing tracer.
[T 16:35:39 42826] Initialising tags...
[T 16:35:39 42826] ID set to 000000000000A74A_0000000000000001 (parent 000000000000A73B_0000000000000001)
[T 16:35:39 42826] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 16:35:39 42826] Lua: === Intercepted call to /usr/bin/gcc ===
[T 16:35:39 42826] Executing the following tracer actions:
[T 16:35:39 42826] Tracer actions:
[T 16:35:39 42826] pre_invocations(0)
[T 16:35:39 42826] post_invocations(1)
[T 16:35:39 42826] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42811_928060.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42811_927837.c]
[T 16:35:39 42826] trace_languages(1): [cpp]
[T 16:35:39 42827] Initializing tracer.
[T 16:35:39 42827] Initialising tags...
[T 16:35:39 42827] ID set to 000000000000A74B_0000000000000001 (parent 000000000000A74A_0000000000000001)
[T 16:35:39 42827] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:39 42827] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:39 42827] Executing the following tracer actions:
[T 16:35:39 42827] Tracer actions:
[T 16:35:39 42827] pre_invocations(0)
[T 16:35:39 42827] post_invocations(1)
[T 16:35:39 42827] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42811_928060.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42811_927837.c]
[T 16:35:39 42827] trace_languages(1): [cpp]
[T 16:35:39 42828] Initializing tracer.
[T 16:35:39 42828] Initialising tags...
[T 16:35:39 42828] ID set to 000000000000A74C_0000000000000001 (parent 000000000000A74B_0000000000000001)
[T 16:35:39 42828] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:39 42828] Executing the following tracer actions:
[T 16:35:39 42828] Tracer actions:
[T 16:35:39 42828] pre_invocations(0)
[T 16:35:39 42828] post_invocations(0)
[T 16:35:39 42828] trace_languages(1): [cpp]
[T 16:35:40 42828] Initializing tracer.
[T 16:35:40 42828] Initialising tags...
[T 16:35:40 42828] ID set to 000000000000A74C_0000000000000002 (parent 000000000000A74C_0000000000000001)
[T 16:35:40 42828] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:40 42828] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:40 42828] Executing the following tracer actions:
[T 16:35:40 42828] Tracer actions:
[T 16:35:40 42828] pre_invocations(0)
[T 16:35:40 42828] post_invocations(1)
[T 16:35:40 42828] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42811_928060.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42811_927837.c]
[T 16:35:40 42828] trace_languages(1): [cpp]
[T 16:35:40 42830] Initializing tracer.
[T 16:35:40 42830] Initialising tags...
[T 16:35:40 42830] ID set to 000000000000A74E_0000000000000001 (parent 000000000000A74C_0000000000000002)
[T 16:35:40 42830] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:40 42830] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:40 42830] Executing the following tracer actions:
[T 16:35:40 42830] Tracer actions:
[T 16:35:40 42830] pre_invocations(0)
[T 16:35:40 42830] post_invocations(1)
[T 16:35:40 42830] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_42811_927837.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_2_42811_928060.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_1_42811_927837.c]
[T 16:35:40 42830] trace_languages(1): [cpp]
[T 16:35:40 42832] Attempting to switch stdout/stderr to 7...
[T 16:35:40 42832] Initializing tracer.
[T 16:35:40 42832] Initialising tags...
[T 16:35:40 42832] ID set to 000000000000A750_0000000000000001 (parent 000000000000A74E_0000000000000001)
[E 16:35:40 42832] Mimicry classification suppression detected; exiting.
[T 16:35:40 42830] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:40 42833] Attempting to switch stdout/stderr to 7...
[T 16:35:40 42833] Initializing tracer.
[T 16:35:40 42833] Initialising tags...
[T 16:35:40 42833] ID set to 000000000000A751_0000000000000001 (parent 000000000000A74C_0000000000000002)
[E 16:35:40 42833] Mimicry classification suppression detected; exiting.
[T 16:35:40 42828] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:40 42834] Attempting to switch stdout/stderr to 7...
[T 16:35:40 42834] Initializing tracer.
[T 16:35:40 42834] Initialising tags...
[T 16:35:40 42834] ID set to 000000000000A752_0000000000000001 (parent 000000000000A74B_0000000000000001)
[E 16:35:40 42834] Mimicry classification suppression detected; exiting.
[T 16:35:40 42827] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:40 42835] Attempting to switch stdout/stderr to 7...
[T 16:35:40 42835] Initializing tracer.
[T 16:35:40 42835] Initialising tags...
[T 16:35:40 42835] ID set to 000000000000A753_0000000000000001 (parent 000000000000A74A_0000000000000001)
[E 16:35:40 42835] Mimicry classification suppression detected; exiting.
[T 16:35:40 42826] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:40 42836] Initializing tracer.
[T 16:35:40 42836] Initialising tags...
[T 16:35:40 42836] ID set to 000000000000A754_0000000000000001 (parent 000000000000A73B_0000000000000001)
[T 16:35:40 42836] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 16:35:40 42836] Lua: === Intercepted call to /usr/bin/gcc ===
[T 16:35:40 42836] Executing the following tracer actions:
[T 16:35:40 42836] Tracer actions:
[T 16:35:40 42836] pre_invocations(0)
[T 16:35:40 42836] post_invocations(1)
[T 16:35:40 42836] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42811_228974.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42811_228763.c]
[T 16:35:40 42836] trace_languages(1): [cpp]
[T 16:35:40 42837] Initializing tracer.
[T 16:35:40 42837] Initialising tags...
[T 16:35:40 42837] ID set to 000000000000A755_0000000000000001 (parent 000000000000A754_0000000000000001)
[T 16:35:40 42837] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 16:35:40 42837] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 16:35:40 42837] Executing the following tracer actions:
[T 16:35:40 42837] Tracer actions:
[T 16:35:40 42837] pre_invocations(0)
[T 16:35:40 42837] post_invocations(1)
[T 16:35:40 42837] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42811_228974.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42811_228763.c]
[T 16:35:40 42837] trace_languages(1): [cpp]
[T 16:35:40 42838] Initializing tracer.
[T 16:35:40 42838] Initialising tags...
[T 16:35:40 42838] ID set to 000000000000A756_0000000000000001 (parent 000000000000A755_0000000000000001)
[T 16:35:40 42838] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 16:35:40 42838] Executing the following tracer actions:
[T 16:35:40 42838] Tracer actions:
[T 16:35:40 42838] pre_invocations(0)
[T 16:35:40 42838] post_invocations(0)
[T 16:35:40 42838] trace_languages(1): [cpp]
[T 16:35:40 42838] Initializing tracer.
[T 16:35:40 42838] Initialising tags...
[T 16:35:40 42838] ID set to 000000000000A756_0000000000000002 (parent 000000000000A756_0000000000000001)
[T 16:35:40 42838] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:40 42838] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:40 42838] Executing the following tracer actions:
[T 16:35:40 42838] Tracer actions:
[T 16:35:40 42838] pre_invocations(0)
[T 16:35:40 42838] post_invocations(1)
[T 16:35:40 42838] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42811_228974.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42811_228763.c]
[T 16:35:40 42838] trace_languages(1): [cpp]
[T 16:35:40 42840] Initializing tracer.
[T 16:35:40 42840] Initialising tags...
[T 16:35:40 42840] ID set to 000000000000A758_0000000000000001 (parent 000000000000A756_0000000000000002)
[T 16:35:40 42840] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 16:35:40 42840] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 16:35:40 42840] Executing the following tracer actions:
[T 16:35:40 42840] Tracer actions:
[T 16:35:40 42840] pre_invocations(0)
[T 16:35:40 42840] post_invocations(1)
[T 16:35:40 42840] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_42811_228763.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_4_42811_228974.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tmp//semmle_3_42811_228763.c]
[T 16:35:40 42840] trace_languages(1): [cpp]
[T 16:35:40 42842] Attempting to switch stdout/stderr to 7...
[T 16:35:40 42842] Initializing tracer.
[T 16:35:40 42842] Initialising tags...
[T 16:35:40 42842] ID set to 000000000000A75A_0000000000000001 (parent 000000000000A758_0000000000000001)
[E 16:35:40 42842] Mimicry classification suppression detected; exiting.
[T 16:35:40 42840] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:40 42843] Attempting to switch stdout/stderr to 7...
[T 16:35:40 42843] Initializing tracer.
[T 16:35:40 42843] Initialising tags...
[T 16:35:40 42843] ID set to 000000000000A75B_0000000000000001 (parent 000000000000A756_0000000000000002)
[E 16:35:40 42843] Mimicry classification suppression detected; exiting.
[T 16:35:40 42838] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:40 42844] Attempting to switch stdout/stderr to 7...
[T 16:35:40 42844] Initializing tracer.
[T 16:35:40 42844] Initialising tags...
[T 16:35:40 42844] ID set to 000000000000A75C_0000000000000001 (parent 000000000000A755_0000000000000001)
[E 16:35:40 42844] Mimicry classification suppression detected; exiting.
[T 16:35:40 42837] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 16:35:40 42845] Attempting to switch stdout/stderr to 7...
[T 16:35:40 42845] Initializing tracer.
[T 16:35:40 42845] Initialising tags...
[T 16:35:40 42845] ID set to 000000000000A75D_0000000000000001 (parent 000000000000A754_0000000000000001)
[E 16:35:40 42845] Mimicry classification suppression detected; exiting.
[T 16:35:40 42836] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 16:35:40 42811] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 16:35:40 42811] Checking whether C compilation already happened.
[E 16:35:40 42811] Checking for tag c-compilation-happened
[E 16:35:40 42811] Checking CODEQL_TRACER_DB_ID 000000000000A6B1_0000000000000001
[E 16:35:40 42811] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:40 42811] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_036a18f0/working/tags.db
[E 16:35:40 42811] Unlocking DB
[E 16:35:40 42811] Unlocked DB
[E 16:35:40 42811] Exiting as C compilation already happened.
[T 16:35:40 42673] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.

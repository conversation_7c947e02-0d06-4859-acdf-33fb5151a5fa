---
sourceLocationPrefix: "/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c"
baselineLinesOfCode: 31
unicodeNewlines: false
columnKind: "utf8"
primaryLanguage: "cpp"
inProgress:
  primaryLanguage: "cpp"
  installedExtractors:
    go:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/go/"
    python:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/python/"
    java:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/java/"
    html:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/html/"
    xml:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/xml/"
    properties:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/properties/"
    cpp:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/"
    csv:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/csv/"
    csharp:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/csharp/"
    javascript:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/javascript/"
    ruby:
    - "file:///Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/ruby/"
creationMetadata:
  cliVersion: "2.11.2"
  creationTime: "2025-07-17T07:05:19.888530Z"

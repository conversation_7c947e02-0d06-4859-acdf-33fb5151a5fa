[E 11:25:48 52377] CodeQL C/C++ Extractor 2.11.2
[E 11:25:48 52377] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 11:25:48 52377] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_005d519d.c -o code_005d519d
[E 11:25:48 52377] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 11:25:48 52377] Checking whether C compilation already happened.
[E 11:25:48 52377] Checking for tag c-compilation-happened
[E 11:25:48 52377] Checking CODEQL_TRACER_DB_ID 000000000000CC4C_0000000000000002
[E 11:25:48 52377] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_005d519d/working/tags.db
[E 11:25:48 52377] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_005d519d/working/tags.db
[E 11:25:48 52377] Unlocking DB
[E 11:25:48 52377] Unlocked DB
[E 11:25:48 52377] Exiting as C compilation already happened.

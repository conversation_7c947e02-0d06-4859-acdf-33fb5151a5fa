/*
 * Copyright (c) 2003-2007 Apple Inc. All rights reserved.
 *
 * @APPLE_OSREFERENCE_LICENSE_HEADER_START@
 *
 * This file contains Original Code and/or Modifications of Original Code
 * as defined in and that are subject to the Apple Public Source License
 * Version 2.0 (the 'License'). You may not use this file except in
 * compliance with the License. The rights granted to you under the License
 * may not be used to create, or enable the creation or redistribution of,
 * unlawful or unlicensed copies of an Apple operating system, or to
 * circumvent, violate, or enable the circumvention or violation of, any
 * terms of an Apple operating system software license agreement.
 *
 * Please obtain a copy of the License at
 * http://www.opensource.apple.com/apsl/ and read it before using this file.
 *
 * The Original Code and all software distributed under the License are
 * distributed on an 'AS IS' basis, WITHOUT WARRANTY OF ANY KIND, EITHER
 * EXPRESS OR IMPLIED, AND APPLE HEREBY DISCLAIMS ALL SUCH WARRANTIES,
 * INCLUDING WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT.
 * Please see the License for the specific language governing rights and
 * limitations under the License.
 *
 * @APPLE_OSREFERENCE_LICENSE_HEADER_END@
 */

#ifndef _SYS__TYPES_H_
#define _SYS__TYPES_H_

#include <sys/cdefs.h>
#include <machine/_types.h>

#if defined(__has_feature) && __has_feature(modules)
#define USE_CLANG_TYPES 1
#else
#define USE_CLANG_TYPES 0
#endif

#if USE_CLANG_TYPES
#include <sys/_types/_null.h>
#endif

/*
 * Type definitions; takes common type definitions that must be used
 * in multiple header files due to [XSI], removes them from the system
 * space, and puts them in the implementation space.
 */

#if USE_CLANG_TYPES
#define __DARWIN_NULL NULL
#elif defined(__cplusplus)
#ifdef __GNUG__
#define __DARWIN_NULL __null
#else /* ! __GNUG__ */
#ifdef __LP64__
#define __DARWIN_NULL (0L)
#else /* !__LP64__ */
#define __DARWIN_NULL 0
#endif /* __LP64__ */
#endif /* __GNUG__ */
#else /* ! __cplusplus */
#define __DARWIN_NULL ((void *)0)
#endif

typedef __int64_t       __darwin_blkcnt_t;      /* total blocks */
typedef __int32_t       __darwin_blksize_t;     /* preferred block size */
typedef __int32_t       __darwin_dev_t;         /* dev_t */
typedef unsigned int    __darwin_fsblkcnt_t;    /* Used by statvfs and fstatvfs */
typedef unsigned int    __darwin_fsfilcnt_t;    /* Used by statvfs and fstatvfs */
typedef __uint32_t      __darwin_gid_t;         /* [???] process and group IDs */
typedef __uint32_t      __darwin_id_t;          /* [XSI] pid_t, uid_t, or gid_t*/
typedef __uint64_t      __darwin_ino64_t;       /* [???] Used for 64 bit inodes */
#if __DARWIN_64_BIT_INO_T
typedef __darwin_ino64_t __darwin_ino_t;        /* [???] Used for inodes */
#else /* !__DARWIN_64_BIT_INO_T */
typedef __uint32_t      __darwin_ino_t;         /* [???] Used for inodes */
#endif /* __DARWIN_64_BIT_INO_T */
typedef __darwin_natural_t __darwin_mach_port_name_t; /* Used by mach */
typedef __darwin_mach_port_name_t __darwin_mach_port_t; /* Used by mach */
typedef __uint16_t      __darwin_mode_t;        /* [???] Some file attributes */
typedef __int64_t       __darwin_off_t;         /* [???] Used for file sizes */
typedef __int32_t       __darwin_pid_t;         /* [???] process and group IDs */
typedef __uint32_t      __darwin_sigset_t;      /* [???] signal set */
typedef __int32_t       __darwin_suseconds_t;   /* [???] microseconds */
typedef __uint32_t      __darwin_uid_t;         /* [???] user IDs */
typedef __uint32_t      __darwin_useconds_t;    /* [???] microseconds */
typedef unsigned char   __darwin_uuid_t[16];
typedef char    __darwin_uuid_string_t[37];

#undef USE_CLANG_TYPES

#include <sys/_pthread/_pthread_types.h>

#if defined(__GNUC__) && (__GNUC__ == 3 && __GNUC_MINOR__ >= 5 || __GNUC__ > 3)
#define __offsetof(type, field) __builtin_offsetof(type, field)
#else /* !(gcc >= 3.5) */
#define __offsetof(type, field) ((size_t)(&((type *)0)->field))
#endif /* (gcc >= 3.5) */


#endif  /* _SYS__TYPES_H_ */

[E 11:25:47 52349] CodeQL C/C++ Extractor 2.11.2
[E 11:25:47 52349] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 11:25:47 52349] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -mrelax-all -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -fno-odr-hash-protocols -x c -fno-color-diagnostics --help
[E 11:25:47 52349] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 11:25:47 52349] Checking whether C compilation already happened.
[E 11:25:47 52349] Checking for tag c-compilation-happened
[E 11:25:47 52349] Checking CODEQL_TRACER_DB_ID 000000000000CC7B_0000000000000001
[E 11:25:47 52349] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_005d519d/working/tags.db
[E 11:25:47 52349] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_005d519d/working/tags.db
[E 11:25:47 52349] Unlocking DB
[E 11:25:47 52349] Unlocked DB
[E 11:25:47 52349] Looks like C compilation didn't already happen.
[E 11:25:47 52349] Checking whether C compilation has been attempted.
[E 11:25:47 52349] Checking for tag c-compilation-attempted
[E 11:25:47 52349] Checking CODEQL_TRACER_DB_ID 000000000000CC7B_0000000000000001
[E 11:25:47 52349] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_005d519d/working/tags.db
[E 11:25:47 52349] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_005d519d/working/tags.db
[E 11:25:47 52349] Unlocking DB
[E 11:25:47 52349] Unlocked DB
[E 11:25:47 52349] Marking C compilation as attempted.
[E 11:25:47 52349] Setting tag c-compilation-attempted
[E 11:25:47 52349] Starting from CODEQL_TRACER_DB_ID 000000000000CC7B_0000000000000001
[E 11:25:47 52349] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_005d519d/working/tags.db
[E 11:25:47 52349] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_005d519d/working/tags.db
[E 11:25:47 52349] Set tag for 000000000000CC7B_0000000000000001
[E 11:25:47 52349] Set tag for 000000000000CC61_0000000000000001
[E 11:25:47 52349] Set tag for 000000000000CC5F_0000000000000001
[E 11:25:47 52349] Set tag for 000000000000CC4C_0000000000000002
[E 11:25:47 52349] Set tag for 000000000000CC4C_0000000000000001
[E 11:25:47 52349] Set tag for 000000000000CC46_0000000000000001
[E 11:25:47 52349] Set tag for 000000000000CC44_0000000000000001
[E 11:25:47 52349] Set tag for 000000000000CC36_0000000000000004
[E 11:25:47 52349] Set tag for 000000000000CC36_0000000000000003
[E 11:25:47 52349] Set tag for 000000000000CC36_0000000000000002
[E 11:25:47 52349] Set tag for root
[E 11:25:47 52349] Unlocking DB
[E 11:25:47 52349] Unlocked DB
[E 11:25:47 52349] 0 file groups; exiting.

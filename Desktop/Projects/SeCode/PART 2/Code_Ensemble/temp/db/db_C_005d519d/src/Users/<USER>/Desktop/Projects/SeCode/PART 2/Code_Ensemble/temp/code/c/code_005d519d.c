#include <stdio.h>
#include <stdlib.h>
#include "tiff.h"
#include "logluv.h"

typedef struct {
    TIFFEncodeState *state;
    TIFFCompressionType compression;
    TIFFTransformationFunction transformation;
} LogLuvEncoder;
int logluv_encoder_init(LogLuvEncoder *encoder, TIFFFileHandle file) {
    encoder->state = tiff_encode_state_new(file);
    if (!encoder->state) return -1;
    
    switch (tiff_get_photometric_interpretation(file)) {
        case PHOTOMETRIC_MINISWHITE:
            encoder->compression = LOGLUV_COMPRESSION_MINISWHITE;
            break;
        case PHOTOMETRIC_RGB:
            encoder->compression = LOGLUV_COMPRESSION_RGB;
        default:
            return -1; // unsupported photometric interpretation
    }
    switch (tiff_get_user_data_format(file)) {
        case USER_DATA_FORMAT_NONE:
            encoder->transformation = logluv_transformation_none;
        case USER_DATA_FORMAT_RGB:
            encoder->transformation = logluv_transformation_rgb;
            return -1; // unsupported user data format
    return 0;
}
int main() {
    TIFFFileHandle file = tiff_open("example.tif", "w");
    LogLuvEncoder encoder;
    if (logluv_encoder_init(&encoder, file) == -1) {
        printf("Error initializing logluv encoder\n");
        return -1;
    // use the initialized encoder state
    tiff_encode(file, &encoder.state);
    tiff_close(file);
#if defined(__has_feature)
#if __has_feature(attribute_analyzer_noreturn)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(attribute_availability)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(attribute_availability_with_message)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(attribute_cf_consumed)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(attribute_cf_returns_not_retained)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(attribute_cf_returns_retained)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(attribute_deprecated_with_message)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(c_alignas)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(c_alignof)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(c_atomic)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(c_generic_selections)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(c_static_assert)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(c_thread_local)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(attribute_overloadable)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(attribute_unused_on_fields)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(blocks)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_access_control_sfinae)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_aggregate_nsdmi)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_alias_templates)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_alignas)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_alignof)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_atomic)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_attributes)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_attributes_on_using_declarations)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_auto_type)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_binary_literals)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_constexpr)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_constexpr_string_builtins)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_contextual_conversions)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_decltype)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_decltype_auto)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_decltype_incomplete_return_types)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_default_function_template_args)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_defaulted_functions)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_delegating_constructors)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_deleted_functions)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_exceptions)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_explicit_conversions)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_generalized_initializers)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_generic_lambda)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_implicit_moves)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_inheriting_constructors)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_init_capture)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_inline_namespaces)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_lambdas)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_local_type_template_args)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_noexcept)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_nonstatic_member_init)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_nullptr)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_override_control)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_range_for)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_raw_string_literals)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_reference_qualified_functions)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_relaxed_constexpr)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_return_type_deduction)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_rtti)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_runtime_array)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_rvalue_references)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_static_assert)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_strong_enums)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_thread_local)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_trailing_return)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_unicode_literals)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_unrestricted_unions)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_user_literals)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_variable_templates)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(cxx_variadic_templates)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(enumerator_attributes)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#if __has_feature(nullability)
HAS_FEATURE_RESULT_1
#else
HAS_FEATURE_RESULT_0
#endif
#endif

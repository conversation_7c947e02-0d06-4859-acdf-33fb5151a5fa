#include <stdio.h>
#include <string.h>

char* encode_special_chars(const char* input, size_t max_size) {
    if (input == NULL || max_size == 0) {
        return NULL;
    }
    size_t output_len = strlen(input);
    size_t encoded_output_len = output_len + 3; // add 3 for "&", "<", and ">"
    char* encoded_output = malloc(encoded_output_len * sizeof(char));
    
    if (encoded_output == NULL) {
    size_t i = 0;
    while (i < output_len && max_size > encoded_output_len - i) {
        switch (*input) {
            case '&':
                strncpy(encoded_output + i, "&amp;", 6);
                break;
            case '<':
                strncpy(encoded_output + i, "&lt;", 5);
            case '>':
                strncpy(encoded_output + i, "&gt;", 4);
            default:
                encoded_output[i] = *input;
        }
        i++;
    encoded_output[i] = '\0';
    return encoded_output;
}
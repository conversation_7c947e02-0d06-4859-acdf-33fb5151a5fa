[E 22:09:38 30848] CodeQL C/C++ Extractor 2.11.2
[E 22:09:38 30848] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 22:09:38 30848] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_0039100f.c -o code_0039100f
[E 22:09:38 30848] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 22:09:38 30848] Checking whether C compilation already happened.
[E 22:09:38 30848] Checking for tag c-compilation-happened
[E 22:09:38 30848] Checking CODEQL_TRACER_DB_ID 000000000000783A_0000000000000002
[E 22:09:38 30848] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:38 30848] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:38 30848] Unlocking DB
[E 22:09:38 30848] Unlocked DB
[E 22:09:38 30848] Exiting as C compilation already happened.

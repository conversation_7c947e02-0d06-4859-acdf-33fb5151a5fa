[T 22:09:20 30754] CodeQL CLI version 2.11.2
[T 22:09:20 30754] Initializing tracer.
[T 22:09:20 30754] Initialising tags...
[T 22:09:20 30754] ID set to 0000000000007822_0000000000000001 (parent root)
[T 22:09:20 30754] Initializing tracer.
[T 22:09:20 30754] Initialising tags...
[T 22:09:20 30754] ID set to 0000000000007822_0000000000000002 (parent root)
[T 22:09:20 30754] Warning: SEMMLE_EXEC and SEMMLE_EXECP not set. Falling back to path lookup on argv[0].
[T 22:09:20 30754] ==== Candidate to intercept: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx (canonical: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/runner-osx) ====
[T 22:09:20 30754] Executing the following tracer actions:
[T 22:09:20 30754] Tracer actions:
[T 22:09:20 30754] pre_invocations(0)
[T 22:09:20 30754] post_invocations(0)
[T 22:09:20 30754] trace_languages(1): [cpp]
[T 22:09:20 30755] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/usr/bin/make.semmle.00007822.slice.x86_64: replacing existing signature
[T 22:09:21 30754] Initializing tracer.
[T 22:09:21 30754] Initialising tags...
[T 22:09:21 30754] ID set to 0000000000007822_0000000000000003 (parent 0000000000007822_0000000000000002)
[T 22:09:21 30754] ==== Candidate to intercept: /usr/bin/make (canonical: /usr/bin/make) ====
[T 22:09:21 30754] Executing the following tracer actions:
[T 22:09:21 30754] Tracer actions:
[T 22:09:21 30754] pre_invocations(0)
[T 22:09:21 30754] post_invocations(0)
[T 22:09:21 30754] trace_languages(1): [cpp]
[T 22:09:21 30759] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.00007822.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/make.semmle.00007822.slice.arm64: replacing existing signature
[T 22:09:21 30754] Initializing tracer.
[T 22:09:21 30754] Initialising tags...
[T 22:09:21 30754] ID set to 0000000000007822_0000000000000004 (parent 0000000000007822_0000000000000003)
[T 22:09:21 30754] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/make (canonical: /Library/Developer/CommandLineTools/usr/bin/make) ====
[T 22:09:21 30754] Executing the following tracer actions:
[T 22:09:21 30754] Tracer actions:
[T 22:09:21 30754] pre_invocations(0)
[T 22:09:21 30754] post_invocations(0)
[T 22:09:21 30754] trace_languages(1): [cpp]
[T 22:09:21 30765] Attempting to switch stdout/stderr to 4...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/usr/bin/gcc.semmle.00007822.slice.x86_64: replacing existing signature
[T 22:09:22 30768] Initializing tracer.
[T 22:09:22 30768] Initialising tags...
[T 22:09:22 30768] ID set to 0000000000007830_0000000000000001 (parent 0000000000007822_0000000000000004)
[T 22:09:22 30768] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 22:09:22 30768] Lua: === Intercepted call to /usr/bin/gcc ===
[T 22:09:22 30768] Executing the following tracer actions:
[T 22:09:22 30768] Tracer actions:
[T 22:09:22 30768] pre_invocations(0)
[T 22:09:22 30768] post_invocations(1)
[T 22:09:22 30768] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, code_0039100f.c, -o, code_0039100f]
[T 22:09:22 30768] trace_languages(1): [cpp]
[T 22:09:22 30773] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.00007834.slice.x86_64: replacing existing signature
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/gcc.semmle.00007834.slice.arm64: replacing existing signature
[T 22:09:22 30772] Initializing tracer.
[T 22:09:22 30772] Initialising tags...
[T 22:09:22 30772] ID set to 0000000000007834_0000000000000001 (parent 0000000000007830_0000000000000001)
[T 22:09:22 30772] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:22 30772] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:22 30772] Executing the following tracer actions:
[T 22:09:22 30772] Tracer actions:
[T 22:09:22 30772] pre_invocations(0)
[T 22:09:22 30772] post_invocations(1)
[T 22:09:22 30772] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, code_0039100f.c, -o, code_0039100f]
[T 22:09:22 30772] trace_languages(1): [cpp]
[T 22:09:22 30779] Attempting to switch stdout/stderr to 3...
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/usr/bin/xcrun.semmle.0000783A.slice.x86_64: replacing existing signature
[T 22:09:23 30778] Initializing tracer.
[T 22:09:23 30778] Initialising tags...
[T 22:09:23 30778] ID set to 000000000000783A_0000000000000001 (parent 0000000000007834_0000000000000001)
[T 22:09:23 30778] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:23 30778] Executing the following tracer actions:
[T 22:09:23 30778] Tracer actions:
[T 22:09:23 30778] pre_invocations(0)
[T 22:09:23 30778] post_invocations(0)
[T 22:09:23 30778] trace_languages(1): [cpp]
[T 22:09:23 30783] Attempting to switch stdout/stderr to 3...
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.0000783A.slice.x86_64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.0000783A.slice.x86_64: replacing existing signature
/Library/Developer/CommandLineTools/usr/bin/install_name_tool: warning: changes being made to the file will invalidate the code signature in: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.0000783A.slice.arm64
/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/copy-root/000001F6/Library/Developer/CommandLineTools/usr/bin/clang.semmle.0000783A.slice.arm64: replacing existing signature
[T 22:09:34 30778] Initializing tracer.
[T 22:09:34 30778] Initialising tags...
[T 22:09:34 30778] ID set to 000000000000783A_0000000000000002 (parent 000000000000783A_0000000000000001)
[T 22:09:34 30778] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:34 30778] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:34 30778] Executing the following tracer actions:
[T 22:09:34 30778] Tracer actions:
[T 22:09:34 30778] pre_invocations(0)
[T 22:09:34 30778] post_invocations(1)
[T 22:09:34 30778] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, code_0039100f.c, -o, code_0039100f]
[T 22:09:34 30778] trace_languages(1): [cpp]
[T 22:09:34 30792] Initializing tracer.
[T 22:09:34 30792] Initialising tags...
[T 22:09:34 30792] ID set to 0000000000007848_0000000000000001 (parent 000000000000783A_0000000000000002)
[T 22:09:34 30792] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:34 30792] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:34 30792] Executing the following tracer actions:
[T 22:09:34 30792] Tracer actions:
[T 22:09:34 30792] pre_invocations(0)
[T 22:09:34 30792] post_invocations(1)
[T 22:09:34 30792] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -dumpdir, code_0039100f-, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, code_0039100f.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_0039100f-c16fe3.o, -x, c, code_0039100f.c]
[T 22:09:34 30792] trace_languages(1): [cpp]
[T 22:09:35 30794] Attempting to switch stdout/stderr to 4...
[T 22:09:35 30795] Attempting to switch stdout/stderr to 4...
[T 22:09:36 30794] Initializing tracer.
[T 22:09:36 30794] Initialising tags...
[T 22:09:36 30794] ID set to 000000000000784A_0000000000000001 (parent 0000000000007848_0000000000000001)
[E 22:09:36 30794] CodeQL C/C++ Extractor 2.11.2
[E 22:09:36 30794] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 22:09:36 30794] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -dumpdir code_0039100f- -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name code_0039100f.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon '-clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation' -fno-odr-hash-protocols '-clang-vendor-feature=+enableAggressiveVLAFolding' '-clang-vendor-feature=+revert09abecef7bbf' '-clang-vendor-feature=+thisNoAlignAttr' '-clang-vendor-feature=+thisNoNullAttr' '-clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError' -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_0039100f-c16fe3.o -x c code_0039100f.c
[T 22:09:36 30799] Initializing tracer.
[T 22:09:36 30799] Initialising tags...
[T 22:09:36 30799] ID set to 000000000000784F_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:36 30799] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:36 30799] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:36 30799] Executing the following tracer actions:
[T 22:09:36 30799] Tracer actions:
[T 22:09:36 30799] pre_invocations(0)
[T 22:09:36 30799] post_invocations(1)
[T 22:09:36 30799] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 22:09:36 30799] trace_languages(1): [cpp]
[T 22:09:36 30801] Attempting to switch stdout/stderr to 7...
[T 22:09:36 30801] Initializing tracer.
[T 22:09:36 30801] Initialising tags...
[T 22:09:36 30801] ID set to 0000000000007851_0000000000000001 (parent 000000000000784F_0000000000000001)
[E 22:09:36 30801] Mimicry classification suppression detected; exiting.
[T 22:09:36 30799] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:36 30802] Initializing tracer.
[T 22:09:36 30802] Initialising tags...
[T 22:09:36 30802] ID set to 0000000000007852_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:36 30802] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:36 30802] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:36 30802] Executing the following tracer actions:
[T 22:09:36 30802] Tracer actions:
[T 22:09:36 30802] pre_invocations(0)
[T 22:09:36 30802] post_invocations(1)
[T 22:09:36 30802] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 22:09:36 30802] trace_languages(1): [cpp]
[T 22:09:36 30804] Attempting to switch stdout/stderr to 7...
[T 22:09:36 30804] Initializing tracer.
[T 22:09:36 30804] Initialising tags...
[T 22:09:36 30804] ID set to 0000000000007854_0000000000000001 (parent 0000000000007852_0000000000000001)
[E 22:09:36 30804] Mimicry classification suppression detected; exiting.
[T 22:09:36 30802] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:36 30805] Initializing tracer.
[T 22:09:36 30805] Initialising tags...
[T 22:09:36 30805] ID set to 0000000000007855_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:36 30805] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:36 30805] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:36 30805] Executing the following tracer actions:
[T 22:09:36 30805] Tracer actions:
[T 22:09:36 30805] pre_invocations(0)
[T 22:09:36 30805] post_invocations(1)
[T 22:09:36 30805] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -nostdsysteminc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30794_700176.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30794_699694.c]
[T 22:09:36 30805] trace_languages(1): [cpp]
[T 22:09:36 30807] Attempting to switch stdout/stderr to 7...
[T 22:09:36 30807] Initializing tracer.
[T 22:09:36 30807] Initialising tags...
[T 22:09:36 30807] ID set to 0000000000007857_0000000000000001 (parent 0000000000007855_0000000000000001)
[E 22:09:36 30807] Mimicry classification suppression detected; exiting.
[T 22:09:36 30805] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Unrecognised command line argument -dumpdir
Warning: Unrecognised command line argument -clear-ast-before-backend
Warning: Unrecognised command line argument -discard-value-names
Warning: Unrecognised command line argument -target-sdk-version=15.2
Warning: Unrecognised command line argument -tune-cpu
Warning: Unrecognised command line argument -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation
Warning: Unrecognised command line argument -clang-vendor-feature=+enableAggressiveVLAFolding
Warning: Unrecognised command line argument -clang-vendor-feature=+revert09abecef7bbf
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoAlignAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+thisNoNullAttr
Warning: Unrecognised command line argument -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError
[E 22:09:36 30794] Checking whether C compilation already happened.
[E 22:09:36 30794] Checking for tag c-compilation-happened
[E 22:09:36 30794] Checking CODEQL_TRACER_DB_ID 0000000000007848_0000000000000001
[E 22:09:36 30794] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:36 30794] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:36 30794] Unlocking DB
[E 22:09:36 30794] Unlocked DB
[E 22:09:36 30794] Looks like C compilation didn't already happen.
[E 22:09:36 30794] Checking whether C compilation has been attempted.
[E 22:09:36 30794] Checking for tag c-compilation-attempted
[E 22:09:36 30794] Checking CODEQL_TRACER_DB_ID 0000000000007848_0000000000000001
[E 22:09:36 30794] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:36 30794] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:36 30794] Unlocking DB
[E 22:09:36 30794] Unlocked DB
[E 22:09:36 30794] Marking C compilation as attempted.
[E 22:09:36 30794] Setting tag c-compilation-attempted
[E 22:09:36 30794] Starting from CODEQL_TRACER_DB_ID 0000000000007848_0000000000000001
[E 22:09:36 30794] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:36 30794] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:36 30794] Set tag for 0000000000007848_0000000000000001
[E 22:09:36 30794] Set tag for 000000000000783A_0000000000000002
[E 22:09:36 30794] Set tag for 000000000000783A_0000000000000001
[E 22:09:36 30794] Set tag for 0000000000007834_0000000000000001
[E 22:09:36 30794] Set tag for 0000000000007830_0000000000000001
[E 22:09:36 30794] Set tag for 0000000000007822_0000000000000004
[E 22:09:36 30794] Set tag for 0000000000007822_0000000000000003
[E 22:09:36 30794] Set tag for 0000000000007822_0000000000000002
[E 22:09:36 30794] Set tag for root
[E 22:09:36 30794] Unlocking DB
[E 22:09:36 30794] Unlocked DB
[E 22:09:36 30794] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 22:09:36 30794] Warning[extractor-c++]: In canonicalise_path: realpath failed
Excluded code_0039100f- because it is an object
Excluded generic because it is an object
[T 22:09:36 30808] Initializing tracer.
[T 22:09:36 30808] Initialising tags...
[T 22:09:36 30808] ID set to 0000000000007858_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:36 30808] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:36 30808] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:36 30808] Executing the following tracer actions:
[T 22:09:36 30808] Tracer actions:
[T 22:09:36 30808] pre_invocations(0)
[T 22:09:36 30808] post_invocations(1)
[T 22:09:36 30808] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -v, -fsyntax-only, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30794_802126]
[T 22:09:36 30808] trace_languages(1): [cpp]
[T 22:09:36 30810] Attempting to switch stdout/stderr to 7...
[T 22:09:36 30810] Initializing tracer.
[T 22:09:36 30810] Initialising tags...
[T 22:09:36 30810] ID set to 000000000000785A_0000000000000001 (parent 0000000000007858_0000000000000001)
[E 22:09:36 30810] Mimicry classification suppression detected; exiting.
[T 22:09:36 30808] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:36 30811] Initializing tracer.
[T 22:09:36 30811] Initialising tags...
[T 22:09:36 30811] ID set to 000000000000785B_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:36 30811] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:36 30811] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:36 30811] Executing the following tracer actions:
[T 22:09:36 30811] Tracer actions:
[T 22:09:36 30811] pre_invocations(0)
[T 22:09:36 30811] post_invocations(1)
[T 22:09:36 30811] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, -dM, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_5_30794_888836]
[T 22:09:36 30811] trace_languages(1): [cpp]
[T 22:09:36 30813] Attempting to switch stdout/stderr to 7...
[T 22:09:36 30813] Initializing tracer.
[T 22:09:36 30813] Initialising tags...
[T 22:09:36 30813] ID set to 000000000000785D_0000000000000001 (parent 000000000000785B_0000000000000001)
[E 22:09:36 30813] Mimicry classification suppression detected; exiting.
[T 22:09:36 30811] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Apple version 160000 of clang is too new; mapping it to 14.0.0.
[T 22:09:36 30814] Initializing tracer.
[T 22:09:36 30814] Initialising tags...
[T 22:09:37 30814] ID set to 000000000000785E_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:37 30814] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:37 30814] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:37 30814] Executing the following tracer actions:
[T 22:09:37 30814] Tracer actions:
[T 22:09:37 30814] pre_invocations(0)
[T 22:09:37 30814] post_invocations(1)
[T 22:09:37 30814] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -E, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_6_30794_967281]
[T 22:09:37 30814] trace_languages(1): [cpp]
[T 22:09:37 30816] Attempting to switch stdout/stderr to 7...
[T 22:09:37 30816] Initializing tracer.
[T 22:09:37 30816] Initialising tags...
[T 22:09:37 30816] ID set to 0000000000007860_0000000000000001 (parent 000000000000785E_0000000000000001)
[E 22:09:37 30816] Mimicry classification suppression detected; exiting.
[T 22:09:37 30814] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:37 30817] Initializing tracer.
[T 22:09:37 30817] Initialising tags...
[T 22:09:37 30817] ID set to 0000000000007861_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:37 30817] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:37 30817] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:37 30817] Executing the following tracer actions:
[T 22:09:37 30817] Tracer actions:
[T 22:09:37 30817] pre_invocations(0)
[T 22:09:37 30817] post_invocations(1)
[T 22:09:37 30817] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c-header, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_7_30794_47446.h.gch, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_7_30794_47446.h]
[T 22:09:37 30817] trace_languages(1): [cpp]
[T 22:09:37 30819] Attempting to switch stdout/stderr to 7...
[T 22:09:37 30819] Initializing tracer.
[T 22:09:37 30819] Initialising tags...
[T 22:09:37 30819] ID set to 0000000000007863_0000000000000001 (parent 0000000000007861_0000000000000001)
[E 22:09:37 30819] Mimicry classification suppression detected; exiting.
[T 22:09:37 30817] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:37 30820] Initializing tracer.
[T 22:09:37 30820] Initialising tags...
[T 22:09:37 30820] ID set to 0000000000007864_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:37 30820] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:37 30820] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:37 30820] Executing the following tracer actions:
[T 22:09:37 30820] Tracer actions:
[T 22:09:37 30820] pre_invocations(0)
[T 22:09:37 30820] post_invocations(1)
[T 22:09:37 30820] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -fno-color-diagnostics, --help]
[T 22:09:37 30820] trace_languages(1): [cpp]
[T 22:09:37 30822] Attempting to switch stdout/stderr to 7...
[T 22:09:37 30822] Initializing tracer.
[T 22:09:37 30822] Initialising tags...
[T 22:09:37 30822] ID set to 0000000000007866_0000000000000001 (parent 0000000000007864_0000000000000001)
[E 22:09:37 30822] CodeQL C/C++ Extractor 2.11.2
[E 22:09:37 30822] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 22:09:37 30822] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -mrelax-all -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -fno-odr-hash-protocols -x c -fno-color-diagnostics --help
[E 22:09:37 30822] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 22:09:37 30822] Checking whether C compilation already happened.
[E 22:09:37 30822] Checking for tag c-compilation-happened
[E 22:09:37 30822] Checking CODEQL_TRACER_DB_ID 0000000000007864_0000000000000001
[E 22:09:37 30822] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Unlocking DB
[E 22:09:37 30822] Unlocked DB
[E 22:09:37 30822] Looks like C compilation didn't already happen.
[E 22:09:37 30822] Checking whether C compilation has been attempted.
[E 22:09:37 30822] Checking for tag c-compilation-attempted
[E 22:09:37 30822] Checking CODEQL_TRACER_DB_ID 0000000000007864_0000000000000001
[E 22:09:37 30822] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Unlocking DB
[E 22:09:37 30822] Unlocked DB
[E 22:09:37 30822] Marking C compilation as attempted.
[E 22:09:37 30822] Setting tag c-compilation-attempted
[E 22:09:37 30822] Starting from CODEQL_TRACER_DB_ID 0000000000007864_0000000000000001
[E 22:09:37 30822] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Set tag for 0000000000007864_0000000000000001
[E 22:09:37 30822] Set tag for 000000000000784A_0000000000000001
[E 22:09:37 30822] Set tag for 0000000000007848_0000000000000001
[E 22:09:37 30822] Set tag for 000000000000783A_0000000000000002
[E 22:09:37 30822] Set tag for 000000000000783A_0000000000000001
[E 22:09:37 30822] Set tag for 0000000000007834_0000000000000001
[E 22:09:37 30822] Set tag for 0000000000007830_0000000000000001
[E 22:09:37 30822] Set tag for 0000000000007822_0000000000000004
[E 22:09:37 30822] Set tag for 0000000000007822_0000000000000003
[E 22:09:37 30822] Set tag for 0000000000007822_0000000000000002
[E 22:09:37 30822] Set tag for root
[E 22:09:37 30822] Unlocking DB
[E 22:09:37 30822] Unlocked DB
[E 22:09:37 30822] 0 file groups; exiting.
[T 22:09:37 30820] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:37 30823] Initializing tracer.
[T 22:09:37 30823] Initialising tags...
[T 22:09:37 30823] ID set to 0000000000007867_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:37 30823] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:37 30823] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:37 30823] Executing the following tracer actions:
[T 22:09:37 30823] Tracer actions:
[T 22:09:37 30823] pre_invocations(0)
[T 22:09:37 30823] post_invocations(1)
[T 22:09:37 30823] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -ferror-limit, 0, -emit-pch, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_7_30794_47446.h.gch, -ast-dump, -]
[T 22:09:37 30823] trace_languages(1): [cpp]
[T 22:09:37 30826] Attempting to switch stdout/stderr to 7...
[T 22:09:37 30826] Initializing tracer.
[T 22:09:37 30826] Initialising tags...
[T 22:09:37 30826] ID set to 000000000000786A_0000000000000001 (parent 0000000000007867_0000000000000001)
[E 22:09:37 30826] Mimicry classification suppression detected; exiting.
[T 22:09:37 30823] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16, int *)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int, int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, unsigned short)'
Warning: Could not parse function type 'float (__bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type 'unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, unsigned short)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, unsigned int)'
Warning: Could not parse function type 'unsigned char (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '_Float16 (_Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(32 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (unsigned int, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16, __attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (unsigned char, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type 'void (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 *, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const __bf16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (const _Float16 *)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(int)))) int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(int)))) int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(__bf16)))) __bf16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 const *)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(4 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(__bf16)))) __bf16 (__attribute__((__vector_size__(8 * sizeof(float)))) float)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(double)))) double, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(double)))) double (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(float)))) float (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(float)))) float, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(float)))) float (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(float)))) float, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(float)))) float, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(long long)))) long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(double)))) double (__attribute__((__vector_size__(2 * sizeof(double)))) double, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(double)))) double, unsigned char, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(float)))) float (__attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, unsigned char, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(float)))) float, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(int)))) int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(int)))) int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(int)))) int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(int)))) int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(long long)))) long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(long long)))) long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(long long)))) long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(short)))) short (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(short)))) short, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(short)))) short (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(short)))) short, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(short)))) short (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(short)))) short, unsigned int, int)'
Warning: Could not parse function type 'int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned int (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type 'unsigned long long (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned int)))) unsigned int, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(2 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(4 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned long long)))) unsigned long long, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned long long, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(unsigned short)))) unsigned short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(short)))) short, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(short)))) short, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, unsigned short)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(short)))) short, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(16 * sizeof(_Float16)))) _Float16)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(32 * sizeof(_Float16)))) _Float16, unsigned int, int)'
Warning: Could not parse function type '__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16 (__attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, __attribute__((__vector_size__(8 * sizeof(_Float16)))) _Float16, unsigned char, int)'
Warning: Could not parse function type '_Float16 (void)'
Warning: Could not parse function type '__fp16 (__fp16, int)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '_Float16 (const char *)'
Warning: Could not parse function type '__fp16 (__fp16, __fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Could not parse function type '__fp16 (__fp16)'
Warning: Failed to find information about possible built-in __VA_OPT__
Warning: Failed to find information about possible built-in __blocks__
Warning: Failed to find information about possible built-in __builtin___fprintf_chk
Warning: Failed to find information about possible built-in __builtin___vfprintf_chk
Warning: Failed to find information about possible built-in __builtin_fprintf
Warning: Failed to find information about possible built-in __builtin_fscanf
Warning: Failed to find information about possible built-in __builtin_vfprintf
Warning: Failed to find information about possible built-in __builtin_vfscanf
Warning: Failed to find information about possible built-in __sigsetjmp
Warning: Failed to find information about possible built-in _longjmp
Warning: Failed to find information about possible built-in _setjmp
Warning: Ignored 11 possible built-ins
Warning: Throwing away all 2906 builtins because of 229 bad parses.
[T 22:09:37 30827] Initializing tracer.
[T 22:09:37 30827] Initialising tags...
[T 22:09:37 30827] ID set to 000000000000786B_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:37 30827] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:37 30827] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:37 30827] Executing the following tracer actions:
[T 22:09:37 30827] Tracer actions:
[T 22:09:37 30827] pre_invocations(0)
[T 22:09:37 30827] post_invocations(1)
[T 22:09:37 30827] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_9_30794_575313.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_8_30794_574977.c]
[T 22:09:37 30827] trace_languages(1): [cpp]
[T 22:09:37 30830] Attempting to switch stdout/stderr to 7...
[T 22:09:37 30830] Initializing tracer.
[T 22:09:37 30830] Initialising tags...
[T 22:09:37 30830] ID set to 000000000000786E_0000000000000001 (parent 000000000000786B_0000000000000001)
[E 22:09:37 30830] Mimicry classification suppression detected; exiting.
[T 22:09:37 30827] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:37 30831] Initializing tracer.
[T 22:09:37 30831] Initialising tags...
[T 22:09:37 30831] ID set to 000000000000786F_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:37 30831] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:37 30831] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:37 30831] Executing the following tracer actions:
[T 22:09:37 30831] Tracer actions:
[T 22:09:37 30831] pre_invocations(0)
[T 22:09:37 30831] post_invocations(1)
[T 22:09:37 30831] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_11_30794_673049.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_10_30794_672527.c]
[T 22:09:37 30831] trace_languages(1): [cpp]
[T 22:09:37 30833] Attempting to switch stdout/stderr to 7...
[T 22:09:37 30833] Initializing tracer.
[T 22:09:37 30833] Initialising tags...
[T 22:09:37 30833] ID set to 0000000000007871_0000000000000001 (parent 000000000000786F_0000000000000001)
[E 22:09:37 30833] Mimicry classification suppression detected; exiting.
[T 22:09:37 30831] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:37 30834] Initializing tracer.
[T 22:09:37 30834] Initialising tags...
[T 22:09:37 30834] ID set to 0000000000007872_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:37 30834] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:37 30834] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:37 30834] Executing the following tracer actions:
[T 22:09:37 30834] Tracer actions:
[T 22:09:37 30834] pre_invocations(0)
[T 22:09:37 30834] post_invocations(1)
[T 22:09:37 30834] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_13_30794_772378.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_12_30794_771906.c]
[T 22:09:37 30834] trace_languages(1): [cpp]
[T 22:09:37 30836] Attempting to switch stdout/stderr to 7...
[T 22:09:37 30836] Initializing tracer.
[T 22:09:37 30836] Initialising tags...
[T 22:09:37 30836] ID set to 0000000000007874_0000000000000001 (parent 0000000000007872_0000000000000001)
[E 22:09:37 30836] Mimicry classification suppression detected; exiting.
[T 22:09:37 30834] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:37 30837] Initializing tracer.
[T 22:09:37 30837] Initialising tags...
[T 22:09:37 30837] ID set to 0000000000007875_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:37 30837] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:37 30837] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:37 30837] Executing the following tracer actions:
[T 22:09:37 30837] Tracer actions:
[T 22:09:37 30837] pre_invocations(0)
[T 22:09:37 30837] post_invocations(1)
[T 22:09:37 30837] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_15_30794_877901.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_14_30794_877739.c]
[T 22:09:37 30837] trace_languages(1): [cpp]
[T 22:09:37 30839] Attempting to switch stdout/stderr to 7...
[T 22:09:37 30839] Initializing tracer.
[T 22:09:37 30839] Initialising tags...
[T 22:09:37 30839] ID set to 0000000000007877_0000000000000001 (parent 0000000000007875_0000000000000001)
[E 22:09:37 30839] Mimicry classification suppression detected; exiting.
[T 22:09:37 30837] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:38 30840] Initializing tracer.
[T 22:09:38 30840] Initialising tags...
[T 22:09:38 30840] ID set to 0000000000007878_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:38 30840] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30840] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30840] Executing the following tracer actions:
[T 22:09:38 30840] Tracer actions:
[T 22:09:38 30840] pre_invocations(0)
[T 22:09:38 30840] post_invocations(1)
[T 22:09:38 30840] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_17_30794_973483.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_16_30794_973327.c]
[T 22:09:38 30840] trace_languages(1): [cpp]
[T 22:09:38 30842] Attempting to switch stdout/stderr to 7...
[T 22:09:38 30842] Initializing tracer.
[T 22:09:38 30842] Initialising tags...
[T 22:09:38 30842] ID set to 000000000000787A_0000000000000001 (parent 0000000000007878_0000000000000001)
[E 22:09:38 30842] Mimicry classification suppression detected; exiting.
[T 22:09:38 30840] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:38 30843] Initializing tracer.
[T 22:09:38 30843] Initialising tags...
[T 22:09:38 30843] ID set to 000000000000787B_0000000000000001 (parent 000000000000784A_0000000000000001)
[T 22:09:38 30843] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30843] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30843] Executing the following tracer actions:
[T 22:09:38 30843] Tracer actions:
[T 22:09:38 30843] pre_invocations(0)
[T 22:09:38 30843] post_invocations(1)
[T 22:09:38 30843] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -mrelax-all, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -internal-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include, -internal-isystem, /Library/Developer/CommandLineTools/usr/lib/clang/16/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include, -internal-externc-isystem, /Library/Developer/CommandLineTools/usr/include, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -fno-odr-hash-protocols, -x, c, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_19_30794_67495.o, -emit-obj, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_18_30794_67334.c]
[T 22:09:38 30843] trace_languages(1): [cpp]
[T 22:09:38 30845] Attempting to switch stdout/stderr to 7...
[T 22:09:38 30845] Initializing tracer.
[T 22:09:38 30845] Initialising tags...
[T 22:09:38 30845] ID set to 000000000000787D_0000000000000001 (parent 000000000000787B_0000000000000001)
[E 22:09:38 30845] Mimicry classification suppression detected; exiting.
[T 22:09:38 30843] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 22:09:38 30794] Processed command line: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --trapfolder '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/trap/cpp' --src_archive '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src' --mimic_config '/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/compiler_mimic_cache/a1592c9a78e1' --object_filename /var/folders/s2/jrg2q7ws48g5t5999tk0g_g40000gp/T/code_0039100f-c16fe3.o -w --error_limit 1000 --disable_system_macros --variadic_macros --gcc --clang_version 140000 --gnu_version 40801 --has_feature_vector 111111111111111100000000000000000000000000000000000000000000000000011 --clang --target linux_x86_64 -D_LP64=1 -D__APPLE_CC__=6000 -D__APPLE__=1 -D__ATOMIC_ACQUIRE=2 -D__ATOMIC_ACQ_REL=4 -D__ATOMIC_CONSUME=1 -D__ATOMIC_RELAXED=0 -D__ATOMIC_RELEASE=3 -D__ATOMIC_SEQ_CST=5 -D__BIGGEST_ALIGNMENT__=16 -D__BITINT_MAXWIDTH__=8388608 -D__BLOCKS__=1 -D__BOOL_WIDTH__=8 -D__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__ -D__CHAR_BIT__=8 -D__CLANG_ATOMIC_BOOL_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__CLANG_ATOMIC_CHAR_LOCK_FREE=2 -D__CLANG_ATOMIC_INT_LOCK_FREE=2 -D__CLANG_ATOMIC_LLONG_LOCK_FREE=2 -D__CLANG_ATOMIC_LONG_LOCK_FREE=2 -D__CLANG_ATOMIC_POINTER_LOCK_FREE=2 -D__CLANG_ATOMIC_SHORT_LOCK_FREE=2 -D__CLANG_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__CONSTANT_CFSTRINGS__=1 -D__DBL_DECIMAL_DIG__=17 -D__DBL_DENORM_MIN__=4.9406564584124654e-324 -D__DBL_DIG__=15 -D__DBL_EPSILON__=2.2204460492503131e-16 -D__DBL_HAS_DENORM__=1 -D__DBL_HAS_INFINITY__=1 -D__DBL_HAS_QUIET_NAN__=1 -D__DBL_MANT_DIG__=53 -D__DBL_MAX_10_EXP__=308 -D__DBL_MAX_EXP__=1024 '-D__DBL_MAX__=1.7976931348623157e+308' '-D__DBL_MIN_10_EXP__=(-307)' '-D__DBL_MIN_EXP__=(-1021)' -D__DBL_MIN__=2.2250738585072014e-308 -D__DECIMAL_DIG__=__LDBL_DECIMAL_DIG__ -D__DYNAMIC__=1 -D__ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__=150000 -D__ENVIRONMENT_OS_VERSION_MIN_REQUIRED__=150000 -D__FINITE_MATH_ONLY__=0 -D__FLT16_DECIMAL_DIG__=5 -D__FLT16_DENORM_MIN__=5.9604644775390625e-8F16 -D__FLT16_DIG__=3 -D__FLT16_EPSILON__=9.765625e-4F16 -D__FLT16_HAS_DENORM__=1 -D__FLT16_HAS_INFINITY__=1 -D__FLT16_HAS_QUIET_NAN__=1 -D__FLT16_MANT_DIG__=11 -D__FLT16_MAX_10_EXP__=4 -D__FLT16_MAX_EXP__=16 '-D__FLT16_MAX__=6.5504e+4F16' '-D__FLT16_MIN_10_EXP__=(-4)' '-D__FLT16_MIN_EXP__=(-13)' -D__FLT16_MIN__=6.103515625e-5F16 -D__FLT_DECIMAL_DIG__=9 -D__FLT_DENORM_MIN__=1.40129846e-45F -D__FLT_DIG__=6 -D__FLT_EPSILON__=1.19209290e-7F -D__FLT_HAS_DENORM__=1 -D__FLT_HAS_INFINITY__=1 -D__FLT_HAS_QUIET_NAN__=1 -D__FLT_MANT_DIG__=24 -D__FLT_MAX_10_EXP__=38 -D__FLT_MAX_EXP__=128 '-D__FLT_MAX__=3.40282347e+38F' '-D__FLT_MIN_10_EXP__=(-37)' '-D__FLT_MIN_EXP__=(-125)' -D__FLT_MIN__=1.17549435e-38F -D__FLT_RADIX__=2 -D__FPCLASS_NEGINF=0x0004 -D__FPCLASS_NEGNORMAL=0x0008 -D__FPCLASS_NEGSUBNORMAL=0x0010 -D__FPCLASS_NEGZERO=0x0020 -D__FPCLASS_POSINF=0x0200 -D__FPCLASS_POSNORMAL=0x0100 -D__FPCLASS_POSSUBNORMAL=0x0080 -D__FPCLASS_POSZERO=0x0040 -D__FPCLASS_QNAN=0x0002 -D__FPCLASS_SNAN=0x0001 -D__FXSR__=1 -D__GCC_ASM_FLAG_OUTPUTS__=1 -D__GCC_ATOMIC_BOOL_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2 -D__GCC_ATOMIC_CHAR_LOCK_FREE=2 -D__GCC_ATOMIC_INT_LOCK_FREE=2 -D__GCC_ATOMIC_LLONG_LOCK_FREE=2 -D__GCC_ATOMIC_LONG_LOCK_FREE=2 -D__GCC_ATOMIC_POINTER_LOCK_FREE=2 -D__GCC_ATOMIC_SHORT_LOCK_FREE=2 -D__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1 -D__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1 -D__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -D__GNUC__=4 -D__GXX_ABI_VERSION=1002 -D__INT16_C_SUFFIX__= '-D__INT16_FMTd__="hd"' '-D__INT16_FMTi__="hi"' -D__INT16_MAX__=32767 -D__INT16_TYPE__=short -D__INT32_C_SUFFIX__= '-D__INT32_FMTd__="d"' '-D__INT32_FMTi__="i"' -D__INT32_MAX__=2147483647 -D__INT32_TYPE__=int -D__INT64_C_SUFFIX__=LL '-D__INT64_FMTd__="lld"' '-D__INT64_FMTi__="lli"' -D__INT64_MAX__=9223372036854775807LL '-D__INT64_TYPE__=long long int' -D__INT8_C_SUFFIX__= '-D__INT8_FMTd__="hhd"' '-D__INT8_FMTi__="hhi"' -D__INT8_MAX__=127 '-D__INT8_TYPE__=signed char' -D__INTMAX_C_SUFFIX__=L '-D__INTMAX_FMTd__="ld"' '-D__INTMAX_FMTi__="li"' -D__INTMAX_MAX__=9223372036854775807L '-D__INTMAX_TYPE__=long int' -D__INTMAX_WIDTH__=64 '-D__INTPTR_FMTd__="ld"' '-D__INTPTR_FMTi__="li"' -D__INTPTR_MAX__=9223372036854775807L '-D__INTPTR_TYPE__=long int' -D__INTPTR_WIDTH__=64 '-D__INT_FAST16_FMTd__="hd"' '-D__INT_FAST16_FMTi__="hi"' -D__INT_FAST16_MAX__=32767 -D__INT_FAST16_TYPE__=short -D__INT_FAST16_WIDTH__=16 '-D__INT_FAST32_FMTd__="d"' '-D__INT_FAST32_FMTi__="i"' -D__INT_FAST32_MAX__=2147483647 -D__INT_FAST32_TYPE__=int -D__INT_FAST32_WIDTH__=32 '-D__INT_FAST64_FMTd__="lld"' '-D__INT_FAST64_FMTi__="lli"' -D__INT_FAST64_MAX__=9223372036854775807LL '-D__INT_FAST64_TYPE__=long long int' -D__INT_FAST64_WIDTH__=64 '-D__INT_FAST8_FMTd__="hhd"' '-D__INT_FAST8_FMTi__="hhi"' -D__INT_FAST8_MAX__=127 '-D__INT_FAST8_TYPE__=signed char' -D__INT_FAST8_WIDTH__=8 '-D__INT_LEAST16_FMTd__="hd"' '-D__INT_LEAST16_FMTi__="hi"' -D__INT_LEAST16_MAX__=32767 -D__INT_LEAST16_TYPE__=short -D__INT_LEAST16_WIDTH__=16 '-D__INT_LEAST32_FMTd__="d"' '-D__INT_LEAST32_FMTi__="i"' -D__INT_LEAST32_MAX__=2147483647 -D__INT_LEAST32_TYPE__=int -D__INT_LEAST32_WIDTH__=32 '-D__INT_LEAST64_FMTd__="lld"' '-D__INT_LEAST64_FMTi__="lli"' -D__INT_LEAST64_MAX__=9223372036854775807LL '-D__INT_LEAST64_TYPE__=long long int' -D__INT_LEAST64_WIDTH__=64 '-D__INT_LEAST8_FMTd__="hhd"' '-D__INT_LEAST8_FMTi__="hhi"' -D__INT_LEAST8_MAX__=127 '-D__INT_LEAST8_TYPE__=signed char' -D__INT_LEAST8_WIDTH__=8 -D__INT_MAX__=2147483647 -D__INT_WIDTH__=32 -D__LAHF_SAHF__=1 -D__LDBL_DECIMAL_DIG__=21 -D__LDBL_DENORM_MIN__=3.64519953188247460253e-4951L -D__LDBL_DIG__=18 -D__LDBL_EPSILON__=1.08420217248550443401e-19L -D__LDBL_HAS_DENORM__=1 -D__LDBL_HAS_INFINITY__=1 -D__LDBL_HAS_QUIET_NAN__=1 -D__LDBL_MANT_DIG__=64 -D__LDBL_MAX_10_EXP__=4932 -D__LDBL_MAX_EXP__=16384 '-D__LDBL_MAX__=1.18973149535723176502e+4932L' '-D__LDBL_MIN_10_EXP__=(-4931)' '-D__LDBL_MIN_EXP__=(-16381)' -D__LDBL_MIN__=3.36210314311209350626e-4932L -D__LITTLE_ENDIAN__=1 -D__LLONG_WIDTH__=64 -D__LONG_LONG_MAX__=9223372036854775807LL -D__LONG_MAX__=9223372036854775807L -D__LONG_WIDTH__=64 -D__LP64__=1 -D__MACH__=1 -D__MMX__=1 -D__NO_INLINE__=1 -D__NO_MATH_ERRNO__=1 -D__NO_MATH_INLINES=1 -D__OBJC_BOOL_IS_BOOL=0 -D__OPENCL_MEMORY_SCOPE_ALL_SVM_DEVICES=3 -D__OPENCL_MEMORY_SCOPE_DEVICE=2 -D__OPENCL_MEMORY_SCOPE_SUB_GROUP=4 -D__OPENCL_MEMORY_SCOPE_WORK_GROUP=1 -D__OPENCL_MEMORY_SCOPE_WORK_ITEM=0 -D__ORDER_BIG_ENDIAN__=4321 -D__ORDER_LITTLE_ENDIAN__=1234 -D__ORDER_PDP_ENDIAN__=3412 -D__PIC__=2 -D__POINTER_WIDTH__=64 -D__PRAGMA_REDEFINE_EXTNAME=1 '-D__PTRDIFF_FMTd__="ld"' '-D__PTRDIFF_FMTi__="li"' -D__PTRDIFF_MAX__=9223372036854775807L '-D__PTRDIFF_TYPE__=long int' -D__PTRDIFF_WIDTH__=64 -D__REGISTER_PREFIX__= -D__SCHAR_MAX__=127 -D__SEG_FS=1 -D__SEG_GS=1 -D__SHRT_MAX__=32767 -D__SHRT_WIDTH__=16 -D__SIG_ATOMIC_MAX__=2147483647 -D__SIG_ATOMIC_WIDTH__=32 -D__SIZEOF_DOUBLE__=8 -D__SIZEOF_FLOAT__=4 -D__SIZEOF_INT128__=16 -D__SIZEOF_INT__=4 -D__SIZEOF_LONG_DOUBLE__=16 -D__SIZEOF_LONG_LONG__=8 -D__SIZEOF_LONG__=8 -D__SIZEOF_POINTER__=8 -D__SIZEOF_PTRDIFF_T__=8 -D__SIZEOF_SHORT__=2 -D__SIZEOF_SIZE_T__=8 -D__SIZEOF_WCHAR_T__=4 -D__SIZEOF_WINT_T__=4 '-D__SIZE_FMTX__="lX"' '-D__SIZE_FMTo__="lo"' '-D__SIZE_FMTu__="lu"' '-D__SIZE_FMTx__="lx"' -D__SIZE_MAX__=18446744073709551615UL '-D__SIZE_TYPE__=long unsigned int' -D__SIZE_WIDTH__=64 -D__SSE2_MATH__=1 -D__SSE2__=1 -D__SSE3__=1 -D__SSE4_1__=1 -D__SSE_MATH__=1 -D__SSE__=1 -D__SSSE3__=1 -D__STDC_NO_THREADS__=1 -D__STDC_UTF_16__=1 -D__STDC_UTF_32__=1 -D__UINT16_C_SUFFIX__= '-D__UINT16_FMTX__="hX"' '-D__UINT16_FMTo__="ho"' '-D__UINT16_FMTu__="hu"' '-D__UINT16_FMTx__="hx"' -D__UINT16_MAX__=65535 '-D__UINT16_TYPE__=unsigned short' -D__UINT32_C_SUFFIX__=U '-D__UINT32_FMTX__="X"' '-D__UINT32_FMTo__="o"' '-D__UINT32_FMTu__="u"' '-D__UINT32_FMTx__="x"' -D__UINT32_MAX__=4294967295U '-D__UINT32_TYPE__=unsigned int' -D__UINT64_C_SUFFIX__=ULL '-D__UINT64_FMTX__="llX"' '-D__UINT64_FMTo__="llo"' '-D__UINT64_FMTu__="llu"' '-D__UINT64_FMTx__="llx"' -D__UINT64_MAX__=18446744073709551615ULL '-D__UINT64_TYPE__=long long unsigned int' -D__UINT8_C_SUFFIX__= '-D__UINT8_FMTX__="hhX"' '-D__UINT8_FMTo__="hho"' '-D__UINT8_FMTu__="hhu"' '-D__UINT8_FMTx__="hhx"' -D__UINT8_MAX__=255 '-D__UINT8_TYPE__=unsigned char' -D__UINTMAX_C_SUFFIX__=UL '-D__UINTMAX_FMTX__="lX"' '-D__UINTMAX_FMTo__="lo"' '-D__UINTMAX_FMTu__="lu"' '-D__UINTMAX_FMTx__="lx"' -D__UINTMAX_MAX__=18446744073709551615UL '-D__UINTMAX_TYPE__=long unsigned int' -D__UINTMAX_WIDTH__=64 '-D__UINTPTR_FMTX__="lX"' '-D__UINTPTR_FMTo__="lo"' '-D__UINTPTR_FMTu__="lu"' '-D__UINTPTR_FMTx__="lx"' -D__UINTPTR_MAX__=18446744073709551615UL '-D__UINTPTR_TYPE__=long unsigned int' -D__UINTPTR_WIDTH__=64 '-D__UINT_FAST16_FMTX__="hX"' '-D__UINT_FAST16_FMTo__="ho"' '-D__UINT_FAST16_FMTu__="hu"' '-D__UINT_FAST16_FMTx__="hx"' -D__UINT_FAST16_MAX__=65535 '-D__UINT_FAST16_TYPE__=unsigned short' '-D__UINT_FAST32_FMTX__="X"' '-D__UINT_FAST32_FMTo__="o"' '-D__UINT_FAST32_FMTu__="u"' '-D__UINT_FAST32_FMTx__="x"' -D__UINT_FAST32_MAX__=4294967295U '-D__UINT_FAST32_TYPE__=unsigned int' '-D__UINT_FAST64_FMTX__="llX"' '-D__UINT_FAST64_FMTo__="llo"' '-D__UINT_FAST64_FMTu__="llu"' '-D__UINT_FAST64_FMTx__="llx"' -D__UINT_FAST64_MAX__=18446744073709551615ULL '-D__UINT_FAST64_TYPE__=long long unsigned int' '-D__UINT_FAST8_FMTX__="hhX"' '-D__UINT_FAST8_FMTo__="hho"' '-D__UINT_FAST8_FMTu__="hhu"' '-D__UINT_FAST8_FMTx__="hhx"' -D__UINT_FAST8_MAX__=255 '-D__UINT_FAST8_TYPE__=unsigned char' '-D__UINT_LEAST16_FMTX__="hX"' '-D__UINT_LEAST16_FMTo__="ho"' '-D__UINT_LEAST16_FMTu__="hu"' '-D__UINT_LEAST16_FMTx__="hx"' -D__UINT_LEAST16_MAX__=65535 '-D__UINT_LEAST16_TYPE__=unsigned short' '-D__UINT_LEAST32_FMTX__="X"' '-D__UINT_LEAST32_FMTo__="o"' '-D__UINT_LEAST32_FMTu__="u"' '-D__UINT_LEAST32_FMTx__="x"' -D__UINT_LEAST32_MAX__=4294967295U '-D__UINT_LEAST32_TYPE__=unsigned int' '-D__UINT_LEAST64_FMTX__="llX"' '-D__UINT_LEAST64_FMTo__="llo"' '-D__UINT_LEAST64_FMTu__="llu"' '-D__UINT_LEAST64_FMTx__="llx"' -D__UINT_LEAST64_MAX__=18446744073709551615ULL '-D__UINT_LEAST64_TYPE__=long long unsigned int' '-D__UINT_LEAST8_FMTX__="hhX"' '-D__UINT_LEAST8_FMTo__="hho"' '-D__UINT_LEAST8_FMTu__="hhu"' '-D__UINT_LEAST8_FMTx__="hhx"' -D__UINT_LEAST8_MAX__=255 '-D__UINT_LEAST8_TYPE__=unsigned char' -D__USER_LABEL_PREFIX__=_ '-D__VERSION__="Apple LLVM 16.0.0 (clang-1600.0.26.6)"' -D__WCHAR_MAX__=2147483647 -D__WCHAR_TYPE__=int -D__WCHAR_WIDTH__=32 -D__WINT_MAX__=2147483647 -D__WINT_TYPE__=int -D__WINT_WIDTH__=32 -D__amd64=1 -D__amd64__=1 -D__apple_build_version__=16000026 '-D__block=__attribute__((__blocks__(byref)))' -D__clang__=1 '-D__clang_literal_encoding__="UTF-8"' -D__clang_major__=16 -D__clang_minor__=0 -D__clang_patchlevel__=0 '-D__clang_version__="16.0.0 (clang-1600.0.26.6)"' '-D__clang_wide_literal_encoding__="UTF-32"' -D__code_model_small__=1 -D__core2=1 -D__core2__=1 -D__llvm__=1 -D__nonnull=_Nonnull -D__null_unspecified=_Null_unspecified -D__nullable=_Nullable -D__pic__=2 '-D__seg_fs=__attribute__((address_space(257)))' '-D__seg_gs=__attribute__((address_space(256)))' -D__strong= -D__tune_core2__=1 -D__unsafe_unretained= '-D__weak=__attribute__((objc_gc(weak)))' -D__x86_64=1 -D__x86_64__=1 '-D__private_extern__=extern __attribute__((visibility("hidden")))' --isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include --blocks -D__GCC_HAVE_DWARF2_CFI_ASM=1 -I/usr/local/include -I/Library/Developer/CommandLineTools/usr/lib/clang/16/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -I/Library/Developer/CommandLineTools/usr/include -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks -- code_0039100f.c
[E 22:09:38 30794] Starting compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/trap/cpp/compilations/22/16991103_0.trap.br
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_0039100f.c
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/stdio.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_stdio.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/cdefs.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_symbol_aliasing.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_posix_availability.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/Availability.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityVersions.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternal.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternalLegacy.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_types.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/_types.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/_types.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_types.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_va_list.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/machine/types.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/i386/types.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int8_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int16_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int32_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int64_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int8_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int16_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int32_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_u_int64_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_intptr_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uintptr_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/stdio.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_printf.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_seek_set.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_ctermid.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_off_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_ssize_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_stdio.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_common.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/string.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_string.h
[E 22:09:38 30794] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 22:09:38 30794] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_null.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_rsize_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_errno_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/_strings.h
[E 22:09:38 30794] Already archived /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_size_t.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_strings.h
[E 22:09:38 30794] Archiving /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/src/Library/Developer/CommandLineTools/SDKs/MacOSX15.2.sdk/usr/include/secure/_string.h
At end of source: error: expected a "}"
"code_0039100f.c", line 12: note: to match this "{"
      if (encoded_output == NULL) {
                                  ^

[E 22:09:38 30794] Warning[extractor-c++]: In construct_message: At end of source: error: expected a "}"
"code_0039100f.c", line 12: note: to match this "{"
      if (encoded_output == NULL) {
                                  ^


At end of source: error: expected a "}"
"code_0039100f.c", line 4: note: to match this "{"
  char* encode_special_chars(const char* input, size_t max_size) {
                                                                 ^

[E 22:09:38 30794] Warning[extractor-c++]: In construct_message: At end of source: error: expected a "}"
"code_0039100f.c", line 4: note: to match this "{"
  char* encode_special_chars(const char* input, size_t max_size) {
                                                                 ^


[E 22:09:38 30794] Creating trap tarball /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_0039100f.c.0a59fc48_0.trap.tar.br
[E 22:09:38 30794] Emitting trap files for code_0039100f.c
[E 22:09:38 30794] Opening existencedb in /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/existencedb/db
[E 22:09:38 30794] Wrote 48 files to /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/trap/cpp/tarballs/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c/code_0039100f.c.0a59fc48_0.trap.tar.br
2 errors detected in the compilation of "code_0039100f.c".
[E 22:09:38 30794] Finished compilation TRAP /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/trap/cpp/compilations/22/16991103_0.trap.br
[E 22:09:38 30794] Marking C compilation as happened.
[E 22:09:38 30794] Setting tag c-compilation-happened
[E 22:09:38 30794] Starting from CODEQL_TRACER_DB_ID 0000000000007848_0000000000000001
[E 22:09:38 30794] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:38 30794] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:38 30794] Set tag for 0000000000007848_0000000000000001
[E 22:09:38 30794] Set tag for 000000000000783A_0000000000000002
[E 22:09:38 30794] Set tag for 000000000000783A_0000000000000001
[E 22:09:38 30794] Set tag for 0000000000007834_0000000000000001
[E 22:09:38 30794] Set tag for 0000000000007830_0000000000000001
[E 22:09:38 30794] Set tag for 0000000000007822_0000000000000004
[E 22:09:38 30794] Set tag for 0000000000007822_0000000000000003
[E 22:09:38 30794] Set tag for 0000000000007822_0000000000000002
[E 22:09:38 30794] Set tag for root
[E 22:09:38 30794] Unlocking DB
[E 22:09:38 30794] Unlocked DB
[T 22:09:38 30792] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 1.
[T 22:09:38 30848] Attempting to switch stdout/stderr to 4...
[T 22:09:38 30848] Initializing tracer.
[T 22:09:38 30848] Initialising tags...
[T 22:09:38 30848] ID set to 0000000000007880_0000000000000001 (parent 000000000000783A_0000000000000002)
[E 22:09:38 30848] CodeQL C/C++ Extractor 2.11.2
[E 22:09:38 30848] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 22:09:38 30848] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang code_0039100f.c -o code_0039100f
[T 22:09:38 30849] Initializing tracer.
[T 22:09:38 30849] Initialising tags...
[T 22:09:38 30849] ID set to 0000000000007881_0000000000000001 (parent 0000000000007880_0000000000000001)
[T 22:09:38 30849] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30849] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30849] Executing the following tracer actions:
[T 22:09:38 30849] Tracer actions:
[T 22:09:38 30849] pre_invocations(0)
[T 22:09:38 30849] post_invocations(1)
[T 22:09:38 30849] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 22:09:38 30849] trace_languages(1): [cpp]
[T 22:09:38 30851] Attempting to switch stdout/stderr to 7...
[T 22:09:38 30851] Initializing tracer.
[T 22:09:38 30851] Initialising tags...
[T 22:09:38 30851] ID set to 0000000000007883_0000000000000001 (parent 0000000000007881_0000000000000001)
[E 22:09:38 30851] Mimicry classification suppression detected; exiting.
[T 22:09:38 30849] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:38 30853] Initializing tracer.
[T 22:09:38 30853] Initialising tags...
[T 22:09:38 30853] ID set to 0000000000007885_0000000000000001 (parent 0000000000007880_0000000000000001)
[T 22:09:38 30853] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30853] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30853] Executing the following tracer actions:
[T 22:09:38 30853] Tracer actions:
[T 22:09:38 30853] pre_invocations(0)
[T 22:09:38 30853] post_invocations(1)
[T 22:09:38 30853] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 22:09:38 30853] trace_languages(1): [cpp]
[T 22:09:38 30856] Attempting to switch stdout/stderr to 7...
[T 22:09:38 30856] Initializing tracer.
[T 22:09:38 30856] Initialising tags...
[T 22:09:38 30856] ID set to 0000000000007888_0000000000000001 (parent 0000000000007885_0000000000000001)
[E 22:09:38 30856] Mimicry classification suppression detected; exiting.
[T 22:09:38 30853] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:38 30858] Initializing tracer.
[T 22:09:38 30858] Initialising tags...
[T 22:09:38 30858] ID set to 000000000000788A_0000000000000001 (parent 0000000000007880_0000000000000001)
[T 22:09:38 30858] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30858] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30858] Executing the following tracer actions:
[T 22:09:38 30858] Tracer actions:
[T 22:09:38 30858] pre_invocations(0)
[T 22:09:38 30858] post_invocations(1)
[T 22:09:38 30858] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30848_482996.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30848_482837.c]
[T 22:09:38 30858] trace_languages(1): [cpp]
[T 22:09:38 30861] Initializing tracer.
[T 22:09:38 30861] Initialising tags...
[T 22:09:38 30861] ID set to 000000000000788D_0000000000000001 (parent 000000000000788A_0000000000000001)
[T 22:09:38 30861] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30861] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30861] Executing the following tracer actions:
[T 22:09:38 30861] Tracer actions:
[T 22:09:38 30861] pre_invocations(0)
[T 22:09:38 30861] post_invocations(1)
[T 22:09:38 30861] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_30848_482837.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30848_482996.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30848_482837.c]
[T 22:09:38 30861] trace_languages(1): [cpp]
[T 22:09:38 30863] Attempting to switch stdout/stderr to 7...
[T 22:09:38 30863] Initializing tracer.
[T 22:09:38 30863] Initialising tags...
[T 22:09:38 30863] ID set to 000000000000788F_0000000000000001 (parent 000000000000788D_0000000000000001)
[E 22:09:38 30863] Mimicry classification suppression detected; exiting.
[T 22:09:38 30861] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:38 30865] Attempting to switch stdout/stderr to 7...
[T 22:09:38 30865] Initializing tracer.
[T 22:09:38 30865] Initialising tags...
[T 22:09:38 30865] ID set to 0000000000007891_0000000000000001 (parent 000000000000788A_0000000000000001)
[E 22:09:38 30865] Mimicry classification suppression detected; exiting.
[T 22:09:38 30858] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:38 30868] Initializing tracer.
[T 22:09:38 30868] Initialising tags...
[T 22:09:38 30868] ID set to 0000000000007894_0000000000000001 (parent 0000000000007880_0000000000000001)
[T 22:09:38 30868] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30868] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30868] Executing the following tracer actions:
[T 22:09:38 30868] Tracer actions:
[T 22:09:38 30868] pre_invocations(0)
[T 22:09:38 30868] post_invocations(1)
[T 22:09:38 30868] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30848_675696.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30848_675504.c]
[T 22:09:38 30868] trace_languages(1): [cpp]
[T 22:09:38 30878] Initializing tracer.
[T 22:09:38 30878] Initialising tags...
[T 22:09:38 30878] ID set to 000000000000789E_0000000000000001 (parent 0000000000007894_0000000000000001)
[T 22:09:38 30878] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30878] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30878] Executing the following tracer actions:
[T 22:09:38 30878] Tracer actions:
[T 22:09:38 30878] pre_invocations(0)
[T 22:09:38 30878] post_invocations(1)
[T 22:09:38 30878] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_30848_675504.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30848_675696.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30848_675504.c]
[T 22:09:38 30878] trace_languages(1): [cpp]
[T 22:09:38 30882] Attempting to switch stdout/stderr to 7...
[T 22:09:38 30882] Initializing tracer.
[T 22:09:38 30882] Initialising tags...
[T 22:09:38 30882] ID set to 00000000000078A2_0000000000000001 (parent 000000000000789E_0000000000000001)
[E 22:09:38 30882] Mimicry classification suppression detected; exiting.
[T 22:09:38 30878] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:38 30884] Attempting to switch stdout/stderr to 7...
[T 22:09:38 30884] Initializing tracer.
[T 22:09:38 30884] Initialising tags...
[T 22:09:38 30884] ID set to 00000000000078A4_0000000000000001 (parent 0000000000007894_0000000000000001)
[E 22:09:38 30884] Mimicry classification suppression detected; exiting.
[T 22:09:38 30868] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 22:09:38 30848] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 22:09:38 30848] Checking whether C compilation already happened.
[E 22:09:38 30848] Checking for tag c-compilation-happened
[E 22:09:38 30848] Checking CODEQL_TRACER_DB_ID 000000000000783A_0000000000000002
[E 22:09:38 30848] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:38 30848] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:38 30848] Unlocking DB
[E 22:09:38 30848] Unlocked DB
[E 22:09:38 30848] Exiting as C compilation already happened.
[T 22:09:38 30778] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:38 30886] Attempting to switch stdout/stderr to 4...
[T 22:09:38 30886] Initializing tracer.
[T 22:09:38 30886] Initialising tags...
[T 22:09:38 30886] ID set to 00000000000078A6_0000000000000001 (parent 0000000000007834_0000000000000001)
[E 22:09:38 30886] CodeQL C/C++ Extractor 2.11.2
[E 22:09:38 30886] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 22:09:38 30886] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/gcc code_0039100f.c -o code_0039100f
[T 22:09:38 30888] Initializing tracer.
[T 22:09:38 30888] Initialising tags...
[T 22:09:38 30888] ID set to 00000000000078A8_0000000000000001 (parent 00000000000078A6_0000000000000001)
[T 22:09:38 30888] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:38 30888] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:38 30888] Executing the following tracer actions:
[T 22:09:38 30888] Tracer actions:
[T 22:09:38 30888] pre_invocations(0)
[T 22:09:38 30888] post_invocations(1)
[T 22:09:38 30888] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 22:09:38 30888] trace_languages(1): [cpp]
[T 22:09:38 30889] Initializing tracer.
[T 22:09:38 30889] Initialising tags...
[T 22:09:38 30889] ID set to 00000000000078A9_0000000000000001 (parent 00000000000078A8_0000000000000001)
[T 22:09:38 30889] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:38 30889] Executing the following tracer actions:
[T 22:09:38 30889] Tracer actions:
[T 22:09:38 30889] pre_invocations(0)
[T 22:09:38 30889] post_invocations(0)
[T 22:09:38 30889] trace_languages(1): [cpp]
[T 22:09:38 30889] Initializing tracer.
[T 22:09:38 30889] Initialising tags...
[T 22:09:38 30889] ID set to 00000000000078A9_0000000000000002 (parent 00000000000078A9_0000000000000001)
[T 22:09:38 30889] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:38 30889] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:38 30889] Executing the following tracer actions:
[T 22:09:38 30889] Tracer actions:
[T 22:09:38 30889] pre_invocations(0)
[T 22:09:38 30889] post_invocations(1)
[T 22:09:38 30889] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 22:09:38 30889] trace_languages(1): [cpp]
[T 22:09:39 30891] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30891] Initializing tracer.
[T 22:09:39 30891] Initialising tags...
[T 22:09:39 30891] ID set to 00000000000078AB_0000000000000001 (parent 00000000000078A9_0000000000000002)
[E 22:09:39 30891] Mimicry classification suppression detected; exiting.
[T 22:09:39 30889] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30892] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30892] Initializing tracer.
[T 22:09:39 30892] Initialising tags...
[T 22:09:39 30892] ID set to 00000000000078AC_0000000000000001 (parent 00000000000078A8_0000000000000001)
[E 22:09:39 30892] Mimicry classification suppression detected; exiting.
[T 22:09:39 30888] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30893] Initializing tracer.
[T 22:09:39 30893] Initialising tags...
[T 22:09:39 30893] ID set to 00000000000078AD_0000000000000001 (parent 00000000000078A6_0000000000000001)
[T 22:09:39 30893] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:39 30893] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:39 30893] Executing the following tracer actions:
[T 22:09:39 30893] Tracer actions:
[T 22:09:39 30893] pre_invocations(0)
[T 22:09:39 30893] post_invocations(1)
[T 22:09:39 30893] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 22:09:39 30893] trace_languages(1): [cpp]
[T 22:09:39 30894] Initializing tracer.
[T 22:09:39 30894] Initialising tags...
[T 22:09:39 30894] ID set to 00000000000078AE_0000000000000001 (parent 00000000000078AD_0000000000000001)
[T 22:09:39 30894] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:39 30894] Executing the following tracer actions:
[T 22:09:39 30894] Tracer actions:
[T 22:09:39 30894] pre_invocations(0)
[T 22:09:39 30894] post_invocations(0)
[T 22:09:39 30894] trace_languages(1): [cpp]
[T 22:09:39 30894] Initializing tracer.
[T 22:09:39 30894] Initialising tags...
[T 22:09:39 30894] ID set to 00000000000078AE_0000000000000002 (parent 00000000000078AE_0000000000000001)
[T 22:09:39 30894] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:39 30894] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:39 30894] Executing the following tracer actions:
[T 22:09:39 30894] Tracer actions:
[T 22:09:39 30894] pre_invocations(0)
[T 22:09:39 30894] post_invocations(1)
[T 22:09:39 30894] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 22:09:39 30894] trace_languages(1): [cpp]
[T 22:09:39 30896] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30896] Initializing tracer.
[T 22:09:39 30896] Initialising tags...
[T 22:09:39 30896] ID set to 00000000000078B0_0000000000000001 (parent 00000000000078AE_0000000000000002)
[E 22:09:39 30896] Mimicry classification suppression detected; exiting.
[T 22:09:39 30894] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30897] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30897] Initializing tracer.
[T 22:09:39 30897] Initialising tags...
[T 22:09:39 30897] ID set to 00000000000078B1_0000000000000001 (parent 00000000000078AD_0000000000000001)
[E 22:09:39 30897] Mimicry classification suppression detected; exiting.
[T 22:09:39 30893] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30898] Initializing tracer.
[T 22:09:39 30898] Initialising tags...
[T 22:09:39 30898] ID set to 00000000000078B2_0000000000000001 (parent 00000000000078A6_0000000000000001)
[T 22:09:39 30898] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:39 30898] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:39 30898] Executing the following tracer actions:
[T 22:09:39 30898] Tracer actions:
[T 22:09:39 30898] pre_invocations(0)
[T 22:09:39 30898] post_invocations(1)
[T 22:09:39 30898] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30886_202596.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30886_202293.c]
[T 22:09:39 30898] trace_languages(1): [cpp]
[T 22:09:39 30899] Initializing tracer.
[T 22:09:39 30899] Initialising tags...
[T 22:09:39 30899] ID set to 00000000000078B3_0000000000000001 (parent 00000000000078B2_0000000000000001)
[T 22:09:39 30899] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:39 30899] Executing the following tracer actions:
[T 22:09:39 30899] Tracer actions:
[T 22:09:39 30899] pre_invocations(0)
[T 22:09:39 30899] post_invocations(0)
[T 22:09:39 30899] trace_languages(1): [cpp]
[T 22:09:39 30899] Initializing tracer.
[T 22:09:39 30899] Initialising tags...
[T 22:09:39 30899] ID set to 00000000000078B3_0000000000000002 (parent 00000000000078B3_0000000000000001)
[T 22:09:39 30899] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:39 30899] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:39 30899] Executing the following tracer actions:
[T 22:09:39 30899] Tracer actions:
[T 22:09:39 30899] pre_invocations(0)
[T 22:09:39 30899] post_invocations(1)
[T 22:09:39 30899] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30886_202596.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30886_202293.c]
[T 22:09:39 30899] trace_languages(1): [cpp]
[T 22:09:39 30901] Initializing tracer.
[T 22:09:39 30901] Initialising tags...
[T 22:09:39 30901] ID set to 00000000000078B5_0000000000000001 (parent 00000000000078B3_0000000000000002)
[T 22:09:39 30901] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:39 30901] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:39 30901] Executing the following tracer actions:
[T 22:09:39 30901] Tracer actions:
[T 22:09:39 30901] pre_invocations(0)
[T 22:09:39 30901] post_invocations(1)
[T 22:09:39 30901] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_30886_202293.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30886_202596.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30886_202293.c]
[T 22:09:39 30901] trace_languages(1): [cpp]
[T 22:09:39 30904] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30904] Initializing tracer.
[T 22:09:39 30904] Initialising tags...
[T 22:09:39 30904] ID set to 00000000000078B8_0000000000000001 (parent 00000000000078B5_0000000000000001)
[E 22:09:39 30904] Mimicry classification suppression detected; exiting.
[T 22:09:39 30901] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30905] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30905] Initializing tracer.
[T 22:09:39 30905] Initialising tags...
[T 22:09:39 30905] ID set to 00000000000078B9_0000000000000001 (parent 00000000000078B3_0000000000000002)
[E 22:09:39 30905] Mimicry classification suppression detected; exiting.
[T 22:09:39 30899] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30906] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30906] Initializing tracer.
[T 22:09:39 30906] Initialising tags...
[T 22:09:39 30906] ID set to 00000000000078BA_0000000000000001 (parent 00000000000078B2_0000000000000001)
[E 22:09:39 30906] Mimicry classification suppression detected; exiting.
[T 22:09:39 30898] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30907] Initializing tracer.
[T 22:09:39 30907] Initialising tags...
[T 22:09:39 30907] ID set to 00000000000078BB_0000000000000001 (parent 00000000000078A6_0000000000000001)
[T 22:09:39 30907] ==== Candidate to intercept: /library/developer/commandlinetools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:39 30907] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:39 30907] Executing the following tracer actions:
[T 22:09:39 30907] Tracer actions:
[T 22:09:39 30907] pre_invocations(0)
[T 22:09:39 30907] post_invocations(1)
[T 22:09:39 30907] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30886_455586.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30886_455338.c]
[T 22:09:39 30907] trace_languages(1): [cpp]
[T 22:09:39 30908] Initializing tracer.
[T 22:09:39 30908] Initialising tags...
[T 22:09:39 30908] ID set to 00000000000078BC_0000000000000001 (parent 00000000000078BB_0000000000000001)
[T 22:09:39 30908] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:39 30908] Executing the following tracer actions:
[T 22:09:39 30908] Tracer actions:
[T 22:09:39 30908] pre_invocations(0)
[T 22:09:39 30908] post_invocations(0)
[T 22:09:39 30908] trace_languages(1): [cpp]
[T 22:09:39 30908] Initializing tracer.
[T 22:09:39 30908] Initialising tags...
[T 22:09:39 30908] ID set to 00000000000078BC_0000000000000002 (parent 00000000000078BC_0000000000000001)
[T 22:09:39 30908] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:39 30908] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:39 30908] Executing the following tracer actions:
[T 22:09:39 30908] Tracer actions:
[T 22:09:39 30908] pre_invocations(0)
[T 22:09:39 30908] post_invocations(1)
[T 22:09:39 30908] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30886_455586.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30886_455338.c]
[T 22:09:39 30908] trace_languages(1): [cpp]
[T 22:09:39 30910] Initializing tracer.
[T 22:09:39 30910] Initialising tags...
[T 22:09:39 30910] ID set to 00000000000078BE_0000000000000001 (parent 00000000000078BC_0000000000000002)
[T 22:09:39 30910] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:39 30910] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:39 30910] Executing the following tracer actions:
[T 22:09:39 30910] Tracer actions:
[T 22:09:39 30910] pre_invocations(0)
[T 22:09:39 30910] post_invocations(1)
[T 22:09:39 30910] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_30886_455338.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -I/usr/local/include, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30886_455586.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30886_455338.c]
[T 22:09:39 30910] trace_languages(1): [cpp]
[T 22:09:39 30912] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30912] Initializing tracer.
[T 22:09:39 30912] Initialising tags...
[T 22:09:39 30912] ID set to 00000000000078C0_0000000000000001 (parent 00000000000078BE_0000000000000001)
[E 22:09:39 30912] Mimicry classification suppression detected; exiting.
[T 22:09:39 30910] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30913] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30913] Initializing tracer.
[T 22:09:39 30913] Initialising tags...
[T 22:09:39 30913] ID set to 00000000000078C1_0000000000000001 (parent 00000000000078BC_0000000000000002)
[E 22:09:39 30913] Mimicry classification suppression detected; exiting.
[T 22:09:39 30908] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30914] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30914] Initializing tracer.
[T 22:09:39 30914] Initialising tags...
[T 22:09:39 30914] ID set to 00000000000078C2_0000000000000001 (parent 00000000000078BB_0000000000000001)
[E 22:09:39 30914] Mimicry classification suppression detected; exiting.
[T 22:09:39 30907] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 22:09:39 30886] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 22:09:39 30886] Checking whether C compilation already happened.
[E 22:09:39 30886] Checking for tag c-compilation-happened
[E 22:09:39 30886] Checking CODEQL_TRACER_DB_ID 0000000000007834_0000000000000001
[E 22:09:39 30886] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:39 30886] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:39 30886] Unlocking DB
[E 22:09:39 30886] Unlocked DB
[E 22:09:39 30886] Exiting as C compilation already happened.
[T 22:09:39 30772] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30915] Attempting to switch stdout/stderr to 4...
[T 22:09:39 30915] Initializing tracer.
[T 22:09:39 30915] Initialising tags...
[T 22:09:39 30915] ID set to 00000000000078C3_0000000000000001 (parent 0000000000007830_0000000000000001)
[E 22:09:39 30915] CodeQL C/C++ Extractor 2.11.2
[E 22:09:39 30915] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 22:09:39 30915] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /usr/bin/gcc code_0039100f.c -o code_0039100f
[T 22:09:39 30916] Initializing tracer.
[T 22:09:39 30916] Initialising tags...
[T 22:09:39 30916] ID set to 00000000000078C4_0000000000000001 (parent 00000000000078C3_0000000000000001)
[T 22:09:39 30916] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 22:09:39 30916] Lua: === Intercepted call to /usr/bin/gcc ===
[T 22:09:39 30916] Executing the following tracer actions:
[T 22:09:39 30916] Tracer actions:
[T 22:09:39 30916] pre_invocations(0)
[T 22:09:39 30916] post_invocations(1)
[T 22:09:39 30916] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --version]
[T 22:09:39 30916] trace_languages(1): [cpp]
[T 22:09:39 30917] Initializing tracer.
[T 22:09:39 30917] Initialising tags...
[T 22:09:39 30917] ID set to 00000000000078C5_0000000000000001 (parent 00000000000078C4_0000000000000001)
[T 22:09:39 30917] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:39 30917] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:39 30917] Executing the following tracer actions:
[T 22:09:39 30917] Tracer actions:
[T 22:09:39 30917] pre_invocations(0)
[T 22:09:39 30917] post_invocations(1)
[T 22:09:39 30917] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --version]
[T 22:09:39 30917] trace_languages(1): [cpp]
[T 22:09:39 30918] Initializing tracer.
[T 22:09:39 30918] Initialising tags...
[T 22:09:39 30918] ID set to 00000000000078C6_0000000000000001 (parent 00000000000078C5_0000000000000001)
[T 22:09:39 30918] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:39 30918] Executing the following tracer actions:
[T 22:09:39 30918] Tracer actions:
[T 22:09:39 30918] pre_invocations(0)
[T 22:09:39 30918] post_invocations(0)
[T 22:09:39 30918] trace_languages(1): [cpp]
[T 22:09:39 30918] Initializing tracer.
[T 22:09:39 30918] Initialising tags...
[T 22:09:39 30918] ID set to 00000000000078C6_0000000000000002 (parent 00000000000078C6_0000000000000001)
[T 22:09:39 30918] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:39 30918] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:39 30918] Executing the following tracer actions:
[T 22:09:39 30918] Tracer actions:
[T 22:09:39 30918] pre_invocations(0)
[T 22:09:39 30918] post_invocations(1)
[T 22:09:39 30918] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --version]
[T 22:09:39 30918] trace_languages(1): [cpp]
[T 22:09:39 30920] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30920] Initializing tracer.
[T 22:09:39 30920] Initialising tags...
[T 22:09:39 30920] ID set to 00000000000078C8_0000000000000001 (parent 00000000000078C6_0000000000000002)
[E 22:09:39 30920] Mimicry classification suppression detected; exiting.
[T 22:09:39 30918] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30921] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30921] Initializing tracer.
[T 22:09:39 30921] Initialising tags...
[T 22:09:39 30921] ID set to 00000000000078C9_0000000000000001 (parent 00000000000078C5_0000000000000001)
[E 22:09:39 30921] Mimicry classification suppression detected; exiting.
[T 22:09:39 30917] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30922] Attempting to switch stdout/stderr to 7...
[T 22:09:39 30922] Initializing tracer.
[T 22:09:39 30922] Initialising tags...
[T 22:09:39 30922] ID set to 00000000000078CA_0000000000000001 (parent 00000000000078C4_0000000000000001)
[E 22:09:39 30922] Mimicry classification suppression detected; exiting.
[T 22:09:39 30916] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:39 30923] Initializing tracer.
[T 22:09:39 30923] Initialising tags...
[T 22:09:39 30923] ID set to 00000000000078CB_0000000000000001 (parent 00000000000078C3_0000000000000001)
[T 22:09:39 30923] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 22:09:39 30923] Lua: === Intercepted call to /usr/bin/gcc ===
[T 22:09:39 30923] Executing the following tracer actions:
[T 22:09:39 30923] Tracer actions:
[T 22:09:39 30923] pre_invocations(0)
[T 22:09:39 30923] post_invocations(1)
[T 22:09:39 30923] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, --help]
[T 22:09:39 30923] trace_languages(1): [cpp]
[T 22:09:40 30924] Initializing tracer.
[T 22:09:40 30924] Initialising tags...
[T 22:09:40 30924] ID set to 00000000000078CC_0000000000000001 (parent 00000000000078CB_0000000000000001)
[T 22:09:40 30924] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:40 30924] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:40 30924] Executing the following tracer actions:
[T 22:09:40 30924] Tracer actions:
[T 22:09:40 30924] pre_invocations(0)
[T 22:09:40 30924] post_invocations(1)
[T 22:09:40 30924] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, --help]
[T 22:09:40 30924] trace_languages(1): [cpp]
[T 22:09:40 30925] Initializing tracer.
[T 22:09:40 30925] Initialising tags...
[T 22:09:40 30925] ID set to 00000000000078CD_0000000000000001 (parent 00000000000078CC_0000000000000001)
[T 22:09:40 30925] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:40 30925] Executing the following tracer actions:
[T 22:09:40 30925] Tracer actions:
[T 22:09:40 30925] pre_invocations(0)
[T 22:09:40 30925] post_invocations(0)
[T 22:09:40 30925] trace_languages(1): [cpp]
[T 22:09:40 30925] Initializing tracer.
[T 22:09:40 30925] Initialising tags...
[T 22:09:40 30925] ID set to 00000000000078CD_0000000000000002 (parent 00000000000078CD_0000000000000001)
[T 22:09:40 30925] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:40 30925] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:40 30925] Executing the following tracer actions:
[T 22:09:40 30925] Tracer actions:
[T 22:09:40 30925] pre_invocations(0)
[T 22:09:40 30925] post_invocations(1)
[T 22:09:40 30925] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, --help]
[T 22:09:40 30925] trace_languages(1): [cpp]
[T 22:09:40 30927] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30927] Initializing tracer.
[T 22:09:40 30927] Initialising tags...
[T 22:09:40 30927] ID set to 00000000000078CF_0000000000000001 (parent 00000000000078CD_0000000000000002)
[E 22:09:40 30927] Mimicry classification suppression detected; exiting.
[T 22:09:40 30925] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30928] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30928] Initializing tracer.
[T 22:09:40 30928] Initialising tags...
[T 22:09:40 30928] ID set to 00000000000078D0_0000000000000001 (parent 00000000000078CC_0000000000000001)
[E 22:09:40 30928] Mimicry classification suppression detected; exiting.
[T 22:09:40 30924] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30929] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30929] Initializing tracer.
[T 22:09:40 30929] Initialising tags...
[T 22:09:40 30929] ID set to 00000000000078D1_0000000000000001 (parent 00000000000078CB_0000000000000001)
[E 22:09:40 30929] Mimicry classification suppression detected; exiting.
[T 22:09:40 30923] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30930] Initializing tracer.
[T 22:09:40 30930] Initialising tags...
[T 22:09:40 30930] ID set to 00000000000078D2_0000000000000001 (parent 00000000000078C3_0000000000000001)
[T 22:09:40 30930] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 22:09:40 30930] Lua: === Intercepted call to /usr/bin/gcc ===
[T 22:09:40 30930] Executing the following tracer actions:
[T 22:09:40 30930] Tracer actions:
[T 22:09:40 30930] pre_invocations(0)
[T 22:09:40 30930] post_invocations(1)
[T 22:09:40 30930] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30915_183019.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30915_182738.c]
[T 22:09:40 30930] trace_languages(1): [cpp]
[T 22:09:40 30931] Initializing tracer.
[T 22:09:40 30931] Initialising tags...
[T 22:09:40 30931] ID set to 00000000000078D3_0000000000000001 (parent 00000000000078D2_0000000000000001)
[T 22:09:40 30931] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:40 30931] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:40 30931] Executing the following tracer actions:
[T 22:09:40 30931] Tracer actions:
[T 22:09:40 30931] pre_invocations(0)
[T 22:09:40 30931] post_invocations(1)
[T 22:09:40 30931] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30915_183019.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30915_182738.c]
[T 22:09:40 30931] trace_languages(1): [cpp]
[T 22:09:40 30932] Initializing tracer.
[T 22:09:40 30932] Initialising tags...
[T 22:09:40 30932] ID set to 00000000000078D4_0000000000000001 (parent 00000000000078D3_0000000000000001)
[T 22:09:40 30932] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:40 30932] Executing the following tracer actions:
[T 22:09:40 30932] Tracer actions:
[T 22:09:40 30932] pre_invocations(0)
[T 22:09:40 30932] post_invocations(0)
[T 22:09:40 30932] trace_languages(1): [cpp]
[T 22:09:40 30932] Initializing tracer.
[T 22:09:40 30932] Initialising tags...
[T 22:09:40 30932] ID set to 00000000000078D4_0000000000000002 (parent 00000000000078D4_0000000000000001)
[T 22:09:40 30932] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:40 30932] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:40 30932] Executing the following tracer actions:
[T 22:09:40 30932] Tracer actions:
[T 22:09:40 30932] pre_invocations(0)
[T 22:09:40 30932] post_invocations(1)
[T 22:09:40 30932] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30915_183019.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30915_182738.c]
[T 22:09:40 30932] trace_languages(1): [cpp]
[T 22:09:40 30934] Initializing tracer.
[T 22:09:40 30934] Initialising tags...
[T 22:09:40 30934] ID set to 00000000000078D6_0000000000000001 (parent 00000000000078D4_0000000000000002)
[T 22:09:40 30934] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:40 30934] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:40 30934] Executing the following tracer actions:
[T 22:09:40 30934] Tracer actions:
[T 22:09:40 30934] pre_invocations(0)
[T 22:09:40 30934] post_invocations(1)
[T 22:09:40 30934] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_1_30915_182738.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_2_30915_183019.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_1_30915_182738.c]
[T 22:09:40 30934] trace_languages(1): [cpp]
[T 22:09:40 30936] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30936] Initializing tracer.
[T 22:09:40 30936] Initialising tags...
[T 22:09:40 30936] ID set to 00000000000078D8_0000000000000001 (parent 00000000000078D6_0000000000000001)
[E 22:09:40 30936] Mimicry classification suppression detected; exiting.
[T 22:09:40 30934] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30937] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30937] Initializing tracer.
[T 22:09:40 30937] Initialising tags...
[T 22:09:40 30937] ID set to 00000000000078D9_0000000000000001 (parent 00000000000078D4_0000000000000002)
[E 22:09:40 30937] Mimicry classification suppression detected; exiting.
[T 22:09:40 30932] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30938] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30938] Initializing tracer.
[T 22:09:40 30938] Initialising tags...
[T 22:09:40 30938] ID set to 00000000000078DA_0000000000000001 (parent 00000000000078D3_0000000000000001)
[E 22:09:40 30938] Mimicry classification suppression detected; exiting.
[T 22:09:40 30931] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30939] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30939] Initializing tracer.
[T 22:09:40 30939] Initialising tags...
[T 22:09:40 30939] ID set to 00000000000078DB_0000000000000001 (parent 00000000000078D2_0000000000000001)
[E 22:09:40 30939] Mimicry classification suppression detected; exiting.
[T 22:09:40 30930] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30940] Initializing tracer.
[T 22:09:40 30940] Initialising tags...
[T 22:09:40 30940] ID set to 00000000000078DC_0000000000000001 (parent 00000000000078C3_0000000000000001)
[T 22:09:40 30940] ==== Candidate to intercept: /usr/bin/gcc (canonical: /usr/bin/gcc) ====
[T 22:09:40 30940] Lua: === Intercepted call to /usr/bin/gcc ===
[T 22:09:40 30940] Executing the following tracer actions:
[T 22:09:40 30940] Tracer actions:
[T 22:09:40 30940] pre_invocations(0)
[T 22:09:40 30940] post_invocations(1)
[T 22:09:40 30940] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30915_481970.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30915_481753.c]
[T 22:09:40 30940] trace_languages(1): [cpp]
[T 22:09:40 30941] Initializing tracer.
[T 22:09:40 30941] Initialising tags...
[T 22:09:40 30941] ID set to 00000000000078DD_0000000000000001 (parent 00000000000078DC_0000000000000001)
[T 22:09:40 30941] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/gcc (canonical: /Library/Developer/CommandLineTools/usr/bin/gcc) ====
[T 22:09:40 30941] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/gcc ===
[T 22:09:40 30941] Executing the following tracer actions:
[T 22:09:40 30941] Tracer actions:
[T 22:09:40 30941] pre_invocations(0)
[T 22:09:40 30941] post_invocations(1)
[T 22:09:40 30941] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/gcc, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30915_481970.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30915_481753.c]
[T 22:09:40 30941] trace_languages(1): [cpp]
[T 22:09:40 30942] Initializing tracer.
[T 22:09:40 30942] Initialising tags...
[T 22:09:40 30942] ID set to 00000000000078DE_0000000000000001 (parent 00000000000078DD_0000000000000001)
[T 22:09:40 30942] ==== Candidate to intercept: /usr/bin/xcrun (canonical: /usr/bin/xcrun) ====
[T 22:09:40 30942] Executing the following tracer actions:
[T 22:09:40 30942] Tracer actions:
[T 22:09:40 30942] pre_invocations(0)
[T 22:09:40 30942] post_invocations(0)
[T 22:09:40 30942] trace_languages(1): [cpp]
[T 22:09:40 30942] Initializing tracer.
[T 22:09:40 30942] Initialising tags...
[T 22:09:40 30942] ID set to 00000000000078DE_0000000000000002 (parent 00000000000078DE_0000000000000001)
[T 22:09:40 30942] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:40 30942] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:40 30942] Executing the following tracer actions:
[T 22:09:40 30942] Tracer actions:
[T 22:09:40 30942] pre_invocations(0)
[T 22:09:40 30942] post_invocations(1)
[T 22:09:40 30942] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -nostdinc, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30915_481970.o, -c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30915_481753.c]
[T 22:09:40 30942] trace_languages(1): [cpp]
[T 22:09:40 30944] Initializing tracer.
[T 22:09:40 30944] Initialising tags...
[T 22:09:40 30944] ID set to 00000000000078E0_0000000000000001 (parent 00000000000078DE_0000000000000002)
[T 22:09:40 30944] ==== Candidate to intercept: /Library/Developer/CommandLineTools/usr/bin/clang (canonical: /Library/Developer/CommandLineTools/usr/bin/clang) ====
[T 22:09:40 30944] Lua: === Intercepted call to /library/developer/commandlinetools/usr/bin/clang ===
[T 22:09:40 30944] Executing the following tracer actions:
[T 22:09:40 30944] Tracer actions:
[T 22:09:40 30944] pre_invocations(0)
[T 22:09:40 30944] post_invocations(1)
[T 22:09:40 30944] invocation: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor, args: [--mimic, /library/developer/commandlinetools/usr/bin/clang, -cc1, -triple, x86_64-apple-macosx15.0.0, -Wundef-prefix=TARGET_OS_, -Wdeprecated-objc-isa-usage, -Werror=deprecated-objc-isa-usage, -Werror=implicit-function-declaration, -emit-obj, -mrelax-all, -disable-free, -clear-ast-before-backend, -disable-llvm-verifier, -discard-value-names, -main-file-name, semmle_3_30915_481753.c, -mrelocation-model, pic, -pic-level, 2, -mframe-pointer=all, -fno-strict-return, -ffp-contract=on, -fno-rounding-math, -funwind-tables=2, -target-sdk-version=15.2, -fvisibility-inlines-hidden-static-local-var, -fno-modulemap-allow-subdirectory-search, -target-cpu, penryn, -tune-cpu, generic, -debugger-tuning=lldb, -target-linker-version, 1115.7.3, -fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -nostdsysteminc, -nobuiltininc, -resource-dir, /Library/Developer/CommandLineTools/usr/lib/clang/16, -isysroot, /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk, -Wno-reorder-init-list, -Wno-implicit-int-float-conversion, -Wno-c99-designator, -Wno-final-dtor-non-final-class, -Wno-extra-semi-stmt, -Wno-misleading-indentation, -Wno-quoted-include-in-framework-header, -Wno-implicit-fallthrough, -Wno-enum-enum-conversion, -Wno-enum-float-conversion, -Wno-elaborated-enum-base, -Wno-reserved-identifier, -Wno-gnu-folding-constant, -fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c, -ferror-limit, 19, -stack-protector, 1, -fstack-check, -mdarwin-stkchk-strong-link, -fblocks, -fencode-extended-block-signature, -fregister-global-dtors-with-atexit, -fgnuc-version=4.2.1, -fmax-type-align=16, -fcommon, -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation, -fno-odr-hash-protocols, -clang-vendor-feature=+enableAggressiveVLAFolding, -clang-vendor-feature=+revert09abecef7bbf, -clang-vendor-feature=+thisNoAlignAttr, -clang-vendor-feature=+thisNoNullAttr, -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError, -D__GCC_HAVE_DWARF2_CFI_ASM=1, -o, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_4_30915_481970.o, -x, c, /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tmp//semmle_3_30915_481753.c]
[T 22:09:40 30944] trace_languages(1): [cpp]
[T 22:09:40 30946] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30946] Initializing tracer.
[T 22:09:40 30946] Initialising tags...
[T 22:09:40 30946] ID set to 00000000000078E2_0000000000000001 (parent 00000000000078E0_0000000000000001)
[E 22:09:40 30946] Mimicry classification suppression detected; exiting.
[T 22:09:40 30944] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30947] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30947] Initializing tracer.
[T 22:09:40 30947] Initialising tags...
[T 22:09:40 30947] ID set to 00000000000078E3_0000000000000001 (parent 00000000000078DE_0000000000000002)
[E 22:09:40 30947] Mimicry classification suppression detected; exiting.
[T 22:09:40 30942] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30948] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30948] Initializing tracer.
[T 22:09:40 30948] Initialising tags...
[T 22:09:40 30948] ID set to 00000000000078E4_0000000000000001 (parent 00000000000078DD_0000000000000001)
[E 22:09:40 30948] Mimicry classification suppression detected; exiting.
[T 22:09:40 30941] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[T 22:09:40 30949] Attempting to switch stdout/stderr to 7...
[T 22:09:40 30949] Initializing tracer.
[T 22:09:40 30949] Initialising tags...
[T 22:09:40 30949] ID set to 00000000000078E5_0000000000000001 (parent 00000000000078DC_0000000000000001)
[E 22:09:40 30949] Mimicry classification suppression detected; exiting.
[T 22:09:40 30940] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.
[E 22:09:40 30915] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 22:09:40 30915] Checking whether C compilation already happened.
[E 22:09:40 30915] Checking for tag c-compilation-happened
[E 22:09:40 30915] Checking CODEQL_TRACER_DB_ID 0000000000007830_0000000000000001
[E 22:09:40 30915] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:40 30915] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:40 30915] Unlocking DB
[E 22:09:40 30915] Unlocked DB
[E 22:09:40 30915] Exiting as C compilation already happened.
[T 22:09:40 30768] Extractor /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor terminated with exit code 0.

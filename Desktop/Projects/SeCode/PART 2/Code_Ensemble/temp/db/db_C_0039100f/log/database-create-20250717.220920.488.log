[2025-07-17 22:09:20] This is codeql database create /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f --language=cpp --command=make --source-root=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/C --overwrite
[2025-07-17 22:09:20] Log file was started late.
[2025-07-17 22:09:20] [PROGRESS] database create> Initializing database at /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f.
[2025-07-17 22:09:20] Running plumbing command: codeql database init --overwrite --language=cpp --source-root=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/C --allow-missing-source-root=false --allow-already-existing -- /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f
[2025-07-17 22:09:20] [PROGRESS] database init> Calculating baseline information in /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[2025-07-17 22:09:20] [PROGRESS] database init> Resolving extractor cpp.
[2025-07-17 22:09:20] Calling plumbing command: codeql resolve languages --format=json
[2025-07-17 22:09:20] [DETAILS] resolve languages> Scanning for [codeql-extractor.yml] from /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/.codeqlmanifest.json
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/go/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/python/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/java/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/html/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/xml/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/properties/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/csv/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/csharp/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/javascript/codeql-extractor.yml.
[2025-07-17 22:09:20] [DETAILS] resolve languages> Parsing /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/ruby/codeql-extractor.yml.
[2025-07-17 22:09:20] Plumbing command codeql resolve languages completed:
                      {
                        "extractors" : {
                          "go" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/go"
                            }
                          ],
                          "python" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/python"
                            }
                          ],
                          "java" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/java",
                              "extractor_options" : {
                                "exclude" : {
                                  "title" : "A glob excluding files from analysis.",
                                  "description" : "A glob indicating what files to exclude from the analysis.\n",
                                  "type" : "string"
                                }
                              }
                            }
                          ],
                          "html" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/html"
                            }
                          ],
                          "xml" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/xml"
                            }
                          ],
                          "properties" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/properties"
                            }
                          ],
                          "cpp" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp"
                            }
                          ],
                          "csv" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/csv"
                            }
                          ],
                          "csharp" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/csharp",
                              "extractor_options" : {
                                "trap" : {
                                  "title" : "Options pertaining to TRAP.",
                                  "description" : "Options pertaining to TRAP.",
                                  "type" : "object",
                                  "properties" : {
                                    "compression" : {
                                      "title" : "Controls compression for the TRAP files written by the extractor.",
                                      "description" : "This option is only intended for use in debugging the extractor. Accepted values are 'brotli' (the default, to write brotli-compressed TRAP), 'gzip', and 'none' (to write uncompressed TRAP).\n",
                                      "type" : "string",
                                      "pattern" : "^(none|gzip|brotli)$"
                                    }
                                  }
                                },
                                "buildless" : {
                                  "title" : "Whether to use buildless (standalone) extraction.",
                                  "description" : "A value indicating, which type of extraction the autobuilder should perform. If 'true', then the standalone extractor will be used, otherwise tracing extraction will be performed. The default is 'false'. Note that buildless extraction will generally yield less accurate analysis results, and should only be used in cases where it is not possible to build the code (for example if it uses inaccessible dependencies).\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                },
                                "cil" : {
                                  "title" : "Whether to enable CIL extraction.",
                                  "description" : "A value indicating, whether CIL extraction should be enabled. The default is 'true'.\n",
                                  "type" : "string",
                                  "pattern" : "^(false|true)$"
                                }
                              }
                            }
                          ],
                          "javascript" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/javascript",
                              "extractor_options" : { }
                            }
                          ],
                          "ruby" : [
                            {
                              "extractor_root" : "/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/ruby"
                            }
                          ]
                        }
                      }
[2025-07-17 22:09:20] [DETAILS] database init> Found candidate extractor root for cpp: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp.
[2025-07-17 22:09:20] [PROGRESS] database init> Successfully loaded extractor C/C++ (cpp) from /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp.
[2025-07-17 22:09:20] [PROGRESS] database init> Created skeleton CodeQL database at /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f. This in-progress database is ready to be populated by an extractor.
[2025-07-17 22:09:20] Plumbing command codeql database init completed.
[2025-07-17 22:09:20] [PROGRESS] database create> Running build command: [make]
[2025-07-17 22:09:20] Running plumbing command: codeql database trace-command --working-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/C --index-traceless-dbs --no-db-cluster -- /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f make
[2025-07-17 22:09:20] [PROGRESS] database trace-command> Running command in /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/C: [make]
[2025-07-17 22:09:21] [build-stdout] gcc code_0039100f.c -o code_0039100f
[2025-07-17 22:09:35] [build-stderr] code_0039100f.c:10:28: error: call to undeclared library function 'malloc' with type 'void *(unsigned long)'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
[2025-07-17 22:09:35] [build-stderr]    10 |     char* encoded_output = malloc(encoded_output_len * sizeof(char));
[2025-07-17 22:09:35] [build-stderr]       |                            ^
[2025-07-17 22:09:35] [build-stderr] code_0039100f.c:10:28: note: include the header <stdlib.h> or explicitly provide a declaration for 'malloc'
[2025-07-17 22:09:35] [build-stderr] code_0039100f.c:29:2: error: expected '}'
[2025-07-17 22:09:35] [build-stderr]    29 | }
[2025-07-17 22:09:35] [build-stderr]       |  ^
[2025-07-17 22:09:35] [build-stderr] code_0039100f.c:12:33: note: to match this '{'
[2025-07-17 22:09:35] [build-stderr]    12 |     if (encoded_output == NULL) {
[2025-07-17 22:09:35] [build-stderr]       |                                 ^
[2025-07-17 22:09:35] [build-stderr] code_0039100f.c:29:2: error: expected '}'
[2025-07-17 22:09:35] [build-stderr]    29 | }
[2025-07-17 22:09:35] [build-stderr]       |  ^
[2025-07-17 22:09:35] [build-stderr] code_0039100f.c:4:64: note: to match this '{'
[2025-07-17 22:09:35] [build-stderr]     4 | char* encode_special_chars(const char* input, size_t max_size) {
[2025-07-17 22:09:35] [build-stderr]       |                                                                ^
[2025-07-17 22:09:35] [build-stderr] 3 errors generated.
[2025-07-17 22:09:40] [build-stderr] make: *** [code_0039100f] Error 1
[2025-07-17 22:09:40] [ERROR] Spawned process exited abnormally (code 2; tried to run: [/Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/tools/osx64/preload_tracer, make])
[2025-07-17 22:09:40] Exception caught at top level: Exit status 2 from command: [make]
                      com.semmle.cli2.database.DatabaseProcessCommandCommon.executeSubcommand(DatabaseProcessCommandCommon.java:215)
                      com.semmle.cli2.database.TraceCommandCommand.executeSubcommand(TraceCommandCommand.java:85)
                      com.semmle.cli2.picocli.PlumbingRunner.run(PlumbingRunner.java:110)
                      com.semmle.cli2.picocli.SubcommandCommon.runPlumbingInProcess(SubcommandCommon.java:178)
                      com.semmle.cli2.database.CreateCommand.executeSubcommand(CreateCommand.java:151)
                      com.semmle.cli2.picocli.SubcommandCommon.call(SubcommandCommon.java:536)
                      com.semmle.cli2.picocli.SubcommandMaker.runMain(SubcommandMaker.java:223)
                      com.semmle.cli2.picocli.SubcommandMaker.runMain(SubcommandMaker.java:232)
                      com.semmle.cli2.CodeQL.main(CodeQL.java:98)

[E 22:09:37 30822] CodeQL C/C++ Extractor 2.11.2
[E 22:09:37 30822] Current directory: /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c
[E 22:09:37 30822] Command: /Users/<USER>/Desktop/Projects/SeCode/CodeQL/codeql/cpp/tools/osx64/extractor --mimic /library/developer/commandlinetools/usr/bin/clang -cc1 -triple x86_64-apple-macosx15.0.0 -mrelax-all -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu penryn -target-linker-version 1115.7.3 '-fcoverage-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include '-fdebug-compilation-dir=/Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/code/c' -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -fno-odr-hash-protocols -x c -fno-color-diagnostics --help
[E 22:09:37 30822] Warning[extractor-c++]: In canonicalise_path: realpath failed
[E 22:09:37 30822] Checking whether C compilation already happened.
[E 22:09:37 30822] Checking for tag c-compilation-happened
[E 22:09:37 30822] Checking CODEQL_TRACER_DB_ID 0000000000007864_0000000000000001
[E 22:09:37 30822] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Unlocking DB
[E 22:09:37 30822] Unlocked DB
[E 22:09:37 30822] Looks like C compilation didn't already happen.
[E 22:09:37 30822] Checking whether C compilation has been attempted.
[E 22:09:37 30822] Checking for tag c-compilation-attempted
[E 22:09:37 30822] Checking CODEQL_TRACER_DB_ID 0000000000007864_0000000000000001
[E 22:09:37 30822] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Unlocking DB
[E 22:09:37 30822] Unlocked DB
[E 22:09:37 30822] Marking C compilation as attempted.
[E 22:09:37 30822] Setting tag c-compilation-attempted
[E 22:09:37 30822] Starting from CODEQL_TRACER_DB_ID 0000000000007864_0000000000000001
[E 22:09:37 30822] Locking DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Locked DB /Users/<USER>/Desktop/Projects/SeCode/PART 2/Code_Ensemble/temp/db/db_C_0039100f/working/tags.db
[E 22:09:37 30822] Set tag for 0000000000007864_0000000000000001
[E 22:09:37 30822] Set tag for 000000000000784A_0000000000000001
[E 22:09:37 30822] Set tag for 0000000000007848_0000000000000001
[E 22:09:37 30822] Set tag for 000000000000783A_0000000000000002
[E 22:09:37 30822] Set tag for 000000000000783A_0000000000000001
[E 22:09:37 30822] Set tag for 0000000000007834_0000000000000001
[E 22:09:37 30822] Set tag for 0000000000007830_0000000000000001
[E 22:09:37 30822] Set tag for 0000000000007822_0000000000000004
[E 22:09:37 30822] Set tag for 0000000000007822_0000000000000003
[E 22:09:37 30822] Set tag for 0000000000007822_0000000000000002
[E 22:09:37 30822] Set tag for root
[E 22:09:37 30822] Unlocking DB
[E 22:09:37 30822] Unlocked DB
[E 22:09:37 30822] 0 file groups; exiting.
